﻿using Dapper;
using System.Data;
using System.Globalization;
using FloraPack.API.Repositories.Boekestyn.Entities;

namespace FloraPack.API.Repositories.Boekestyn;

public class BoekestynRepository(IConfiguration configuration) : RepositoryBase(configuration)
{
    public async Task<IEnumerable<ItemListItem>> BoekestynItems(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);

        var items = (await GetConnection().QueryAsync<ItemListItem>("SELECT * FROM boekestyn_item_list(@required_start_date, @required_end_date)", parameters)).ToList();

        return items;
    }

    public async Task<IEnumerable<ItemListRequirementsItem>> ItemListRequirements(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);

        var items = await GetConnection()

            .QueryAsync<ItemListRequirementsItem>("SELECT * FROM boekestyn_prebook_requirements(@required_startDate, @required_endDate)", parameters);        
        return items.ToList();
    }

    public async Task<List<BoekestynPrebookItem>> GetPrebookItems(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();

        var startOfWeek = default(DateTime?);
        if (startDate.HasValue) {
            var week = ISOWeek.GetWeekOfYear(startDate.Value);
            var year = ISOWeek.GetYear(startDate.Value);
            startOfWeek = ISOWeek.ToDateTime(year, week, DayOfWeek.Monday);
        }

        var endOfWeek = default(DateTime?);
        if (endDate.HasValue) {
            var week = ISOWeek.GetWeekOfYear(endDate.Value);
            var year = ISOWeek.GetYear(endDate.Value);
            endOfWeek = ISOWeek.ToDateTime(year, week, DayOfWeek.Saturday);
        }

        parameters.Add("required_start_date", startOfWeek, DbType.Date);
        parameters.Add("required_end_date", endOfWeek, DbType.Date);

        var prebookItems = await GetConnection()
            .QueryAsync<BoekestynPrebookItem>(@"SELECT * FROM boekestyn_prebook_items(@required_start_date, @required_end_date);",
                parameters);

        return prebookItems.ToList();
    }
}

public class ItemListRequirementsItem
{
    public int Id { get; set; }
    public int PrebookId { get; set; }
    public string Product { get; set; } = string.Empty;
    public string RequiredDate { get; set; } = string.Empty;
    public string BoxCode { get; set; } = string.Empty;
    public string PotCover { get; set; } = string.Empty;
    public string Comments { get; set; } = string.Empty;
    public int OrderQuantity { get; set; }
}