﻿namespace FloraPack.API.Repositories.Prebooks;

public class PrebookDetailItem
{
    public int Id { get; set; }
    public int PrebookId { get; set; }
    public int SpireInventoryId { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int OrderQuantity { get; set; }
    public bool HasPotCover { get; set; }
    public string? PotCover { get; set; }
    public string? DateCode { get; set; }
    public string? Upc { get; set; }
    public bool WeightsAndMeasures { get; set; }
    public string? Retail { get; set; }
    public string? Comments { get; set; }
    public bool IsApproximate { get; set; }
    public int? BlanketItemId { get; set; }
    public string? BlanketWeekId { get; set; }
    public bool UpgradeSheet { get; set; }
    public int? FutureOrderItemId { get; set; }
    public string? BoekestynPlantId { get; set; }
    public string? BoekestynCustomerAbbreviation { get; set; }
    public decimal UpgradeLabourHours { get; set; }
    public int? QuantityPerFinishedItem { get; set; }
    public decimal? SpecialPrice { get; set; }
    public string? GrowerItemNotes { get; set; }
    public bool BoekPriority { get; set; }
    public DateTime? UpcPrinted { get; set; }
    public DateTime? UpcPrintedPrev { get; set; }
    public int? SpirePurchaseOrderItemId { get; set; }
    public int? FutureOrderId { get; set; }

    public List<PrebookDetailItemBlanketOption> BlanketOptions { get; set; } = new();
    public List<PrebookItemBoekestynProduct> BoekestynProducts { get; set; } = new();
}