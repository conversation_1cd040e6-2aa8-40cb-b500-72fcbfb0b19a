﻿namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class HarvestingAdminOrderItem
{
    public int StickingWorkOrderId { get; init; }
    public string OrderId { get; init; } = string.Empty;
    public string OrderNumber { get; init; } = string.Empty;
    public string PlantSize { get; init; } = string.Empty;
    public string PlantCrop { get; init; } = string.Empty;
    public string Customer { get; init; } = string.Empty;
    public int Pots { get; init; }
    public string? Colour { get; init; }
    public DateTime FlowerDate { get; init; }
    public string? Notes { get; init; }

    public List<HarvestingOrderRound> Rounds { get; init; } = [];
    public List<HarvestingOrder.HarvestingOrderVariety> Varieties { get; init; } = [];
}