﻿using System.Data;
using Dapper;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.Prebooks;

namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderRepository(IConfiguration configuration) : RepositoryBase(configuration)
{
    public async Task<IEnumerable<FutureOrderListItem>> List(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);

        var futureOrders =
            (await GetConnection()
                .QueryAsync<FutureOrderListItem>(
                    "SELECT * FROM future_order_list(@required_start_date, @required_end_date)", parameters)).ToList();
        var prebooks =
            (await GetConnection().QueryAsync<FutureOrderListItemPrebook>(
                "SELECT * FROM future_order_prebooks_list(@required_start_date, @required_end_date)", parameters))
            .ToLookup(p => p.FutureOrderId);

        foreach (var futureOrder in futureOrders.Where(o => prebooks.Contains(o.Id))) {
            futureOrder.Prebooks.AddRange(prebooks[futureOrder.Id]);
        }

        return futureOrders;
    }

    public async Task<IEnumerable<FutureOrderSummaryItem>> ItemSummary(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);
        return await GetConnection().QueryAsync<FutureOrderSummaryItem>(
            "SELECT * FROM future_order_item_summary(@required_start_date, @required_end_date)", parameters);
    }

    public async Task<IEnumerable<UpgradeItem>> UpgradeItemList(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);
        return await GetConnection()
            .QueryAsync<UpgradeItem>(
                "SELECT * FROM future_order_upgrade_list(@required_start_date, @required_end_date)", parameters);
    }

    public async Task<FutureOrderDetail?> Detail(int id)
    {
        await using var multi = await GetConnection().QueryMultipleAsync($@"
        SELECT * FROM future_orders WHERE id = @id;
        SELECT * FROM future_order_items WHERE future_order_id = @id ORDER BY sort_order;
        SELECT * FROM future_order_comments WHERE future_order_id = @id ORDER BY created DESC;
        SELECT bp.id, fi.id AS prebook_item_id, bp.boekestyn_plant_id, bp.boekestyn_customer_abbreviation, bp.quantity_per_finished_item 
        FROM future_order_items fi JOIN prebook_items pi ON fi.id = pi.future_order_item_id JOIN prebook_item_boekestyn_products bp ON pi.id = bp.prebook_item_id;
    ", new { id });
        var detail = await multi.ReadFirstOrDefaultAsync<FutureOrderDetail>();
        if (detail != null) {
            var items = (await multi.ReadAsync<FutureOrderDetailItem>()).ToList();
            detail.Comments.AddRange(await multi.ReadAsync<FutureOrderDetailComment>());
            var boekestynProducts =
                (await multi.ReadAsync<FutureOrderDetailItemBoekestynProduct>()).ToLookup(p => p.PrebookItemId);

            foreach (var item in items.Where(i => boekestynProducts.Contains(i.Id))) {
                item.BoekestynProducts.AddRange(boekestynProducts[item.Id]);
            }

            detail.Items.AddRange(items);
        }

        return detail;
    }

    public async Task<int> Create(FutureOrderCreate futureOrder, string? who, bool customerItemCodeByShipTo)
    {
        var now = DateTime.Now;
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();
        var sql = @"INSERT INTO future_orders (required_date, arrival_date, season_name, customer_id, customer_name, 
                        ship_to_id, ship_to_name, box_code, customer_purchase_order_number, freight_per_case, freight_per_load, freight_is_actual, salesperson_id, salesperson_name, 
                        spire_notes, grower_item_notes, ship_via_id, ship_via_name, requires_labels, created, created_by, modified, modified_by)
                    VALUES (@requiredDate, @arrivalDate, @seasonName, @customerId, @customerName, 
                        @shipToId, @shipToName, @boxCode, @customerPurchaseOrderNumber, @freightPerCase, @freightPerLoad, @freightIsActual, @salespersonId, @salespersonName, 
                        @spireNotes, @growerItemNotes, @shipViaId, @shipViaName, @requiresLabels, @now, @who, @now, @who)
                        RETURNING id;";
        var futureOrderParams = new {
            futureOrder.RequiredDate, futureOrder.ArrivalDate, futureOrder.SeasonName, futureOrder.CustomerId, futureOrder.CustomerName,
            futureOrder.ShipToId, futureOrder.ShipToName, futureOrder.BoxCode, futureOrder.CustomerPurchaseOrderNumber,
            futureOrder.FreightPerCase, futureOrder.FreightPerLoad, futureOrder.FreightIsActual, futureOrder.SalespersonId, futureOrder.SalespersonName,
            futureOrder.SpireNotes, futureOrder.GrowerItemNotes, futureOrder.ShipViaId, futureOrder.ShipViaName,
            futureOrder.RequiresLabels, now, who
        };
        var id = await connection.ExecuteScalarAsync<int>(sql, futureOrderParams, transaction: tx);

        var comments = futureOrder.Comments.Where(c => !string.IsNullOrWhiteSpace(c.Comments)).ToList();
        var commentSql =
            @"INSERT INTO future_order_comments (future_order_id, comments, is_standard_comment, created, created_by) 
                            VALUES (@id, @comments, @isStandardComment, @now, @who);";
        var commentParams = comments.Select(c => new { id, c.Comments, c.IsStandardComment, now, who });
        await connection.ExecuteAsync(commentSql, commentParams, transaction: tx);

        var prebookItems = futureOrder.Items.Where(i => i.VendorId.HasValue && i.CreatePrebook).ToList();

        var vendors = prebookItems
            .Select(i => i.VendorId.GetValueOrDefault())
            .Distinct()
            .ToList();
        var vendorPrebooks = new Dictionary<int, int>();

        foreach (var vendor in vendors) {
            var items = prebookItems.Where(i => i.VendorId == vendor).ToList();
            var firstItem = items.FirstOrDefault();
            if (firstItem != null) {
                var prebookSql =
                    @"INSERT INTO prebooks (future_order_id, required_date, is_blanket, season_name, box_code, vendor_id, vendor_name,
                salesperson_id, salesperson_name, customer_id, customer_name, ship_to_id, ship_to_name, grower_item_notes, created, created_by, modified, modified_by)
            VALUES (@id, @requiredDate, @isBlanket, @seasonName, @boxCode, @vendorId, @vendorName,
                @salespersonId, @salespersonName, @customerId, @customerName, @shipToId, @shipToName, @growerItemNotes, @now, @who, @now, @who)
            RETURNING id;";
                var prebookParams = new {
                    id, futureOrder.RequiredDate, firstItem.IsBlanket, futureOrder.SeasonName, futureOrder.BoxCode,
                    firstItem.VendorId, firstItem.VendorName,
                    futureOrder.SalespersonId, futureOrder.SalespersonName, futureOrder.CustomerId,
                    futureOrder.CustomerName, futureOrder.ShipToId, futureOrder.ShipToName, futureOrder.GrowerItemNotes,
                    now, who
                };
                var prebookId = await connection.ExecuteScalarAsync<int>(prebookSql, prebookParams, transaction: tx);
                if (!vendorPrebooks.ContainsKey(vendor)) {
                    vendorPrebooks.Add(vendor, prebookId);
                }
            }
        }

        var itemSql =
            @"INSERT INTO future_order_items (future_order_id, sort_order, vendor_id, vendor_name, spire_inventory_id, spire_part_number, description, 
                            order_quantity, is_approximate, blanket_item_id, has_pot_cover, pot_cover, date_code, upc, weights_and_measures, 
                            retail, use_availability_pricing, unit_price, customer_item_code, upgrade_sheet, phyto_required, comments, boekestyn_plant_id,
                            boekestyn_customer_abbreviation, special_price, grower_item_notes)
                    VALUES (@id, @sortOrder, @vendorId, @vendorName, @spireInventoryId, @spirePartNumber, @description, 
                            @orderQuantity, @isApproximate, @blanketItemId, @hasPotCover, @potCover, @dateCode, @upc, @weightsAndMeasures, 
                            @retail, @useAvailabilityPricing, @unitPrice, @customerItemCode, @upgradeSheet, @phytoRequired, @comments, @boekestynPlantId,
                            @boekestynCustomerAbbreviation, @specialPrice, @growerItemNotes)
                    RETURNING id;";
        var prebookItemSql =
            @"INSERT INTO prebook_items(prebook_id, spire_inventory_id, spire_part_number, description, order_quantity,
                            has_pot_cover, pot_cover, date_code, upc, weights_and_measures, retail, upgrade_sheet, comments, is_approximate, blanket_item_id, future_order_item_id,
                            boekestyn_plant_id, boekestyn_customer_abbreviation, upgrade_labour_hours, upgrade_product_coming_from, boekestyn_quantity_per_finished_item, special_price, grower_item_notes)
                        VALUES (@prebookId, @spireInventoryId, @spirePartNumber, @description, @orderQuantity,
                            @hasPotCover, @potCover, @dateCode, @upc, @weightsAndMeasures, @retail, @upgradeSheet, @comments, @isApproximate, @blanketItemId, @itemId,
                            @boekestynPlantId, @boekestynCustomerAbbreviation, @upgradeLabourHours, @vendorName, @quantityPerFinishedItem, @specialPrice, @growerItemNotes) RETURNING id;";

        var boekestynProductSql =
            @"INSERT INTO prebook_item_boekestyn_products (prebook_item_id, boekestyn_plant_id, boekestyn_customer_abbreviation, quantity_per_finished_item) 
                        VALUES (@prebookItemId, @boekestynPlantId, @boekestynCustomerAbbreviation, @quantityPerFinishedItem);";

        foreach (var item in futureOrder.Items) {

            // don't include the over & above comment to Boeks
            var itemComments = item.Comments;
            if (item.VendorId == Constants.BoekestynVendorId) {
                itemComments = itemComments?.Replace(Constants.OverAndAboveBlanketComment, "");
            }

            var itemsParams = new {
                id, item.SortOrder, item.VendorId, item.VendorName, item.SpireInventoryId, item.SpirePartNumber,
                item.Description,
                item.OrderQuantity, item.IsApproximate, item.BlanketItemId, item.HasPotCover, item.PotCover,
                item.DateCode, item.Upc,
                item.WeightsAndMeasures, item.Retail, item.UseAvailabilityPricing, item.UnitPrice,
                item.CustomerItemCode, item.UpgradeSheet, item.PhytoRequired,
                comments = itemComments, item.BoekestynPlantId, item.BoekestynCustomerAbbreviation, item.SpecialPrice,
                item.GrowerItemNotes
            };
            var itemId = await connection.ExecuteScalarAsync<int>(itemSql, itemsParams, transaction: tx);

            if (item.VendorId.HasValue && vendorPrebooks.ContainsKey(item.VendorId.Value)) {
                var prebookId = vendorPrebooks[item.VendorId.Value];
                var itemParams = new {
                    prebookId, item.SpireInventoryId, item.SpirePartNumber, item.Description, item.OrderQuantity,
                    item.HasPotCover, item.PotCover,
                    item.DateCode, item.Upc, item.WeightsAndMeasures, item.Retail, item.UpgradeSheet, item.Comments,
                    item.IsApproximate, item.BlanketItemId, itemId,
                    item.BoekestynPlantId, item.BoekestynCustomerAbbreviation, item.UpgradeLabourHours,
                    item.VendorName, item.QuantityPerFinishedItem, item.SpecialPrice, item.GrowerItemNotes
                };

                var prebookItemId =
                    await connection.ExecuteScalarAsync<int>(prebookItemSql, itemParams, transaction: tx);

                if (item.BoekestynProducts.Any()) {
                    await connection.ExecuteAsync(boekestynProductSql,
                        item.BoekestynProducts.Select(p => new {
                            prebookItemId, p.BoekestynPlantId, p.BoekestynCustomerAbbreviation,
                            p.QuantityPerFinishedItem
                        }),
                        transaction: tx);
                }
            }
        }

        await tx.CommitAsync();

        if (futureOrder.CustomerId.HasValue) {
            var customerItemCodeItems =
                futureOrder.Items.Where(i => !string.IsNullOrWhiteSpace(i.CustomerItemCode)).ToList();
            if (customerItemCodeByShipTo && futureOrder.ShipToId.HasValue) {
                var defaultParams = customerItemCodeItems
                    // NOTE: postgres' function parameters are case-sensitive, and lowercase: https://stackoverflow.com/a/64711380
                    .Select(i => new {
                        spireinventoryid = i.SpireInventoryId, shiptoid = futureOrder.ShipToId,
                        haspotcover = i.HasPotCover, potcover = i.PotCover,
                        upc = i.Upc, weightsandmeasures = i.WeightsAndMeasures, retail = i.Retail,
                        unitprice = i.UnitPrice, customeritemcode = i.CustomerItemCode
                    });

                await connection.ExecuteAsync(
                    "SELECT set_product_ship_to_defaults(@spireinventoryid, @shiptoid, @haspotcover, @potcover, @upc, @weightsandmeasures, @retail, @unitprice, @customeritemcode)",
                    defaultParams);
            } else if (!customerItemCodeByShipTo) {
                var defaultParams = customerItemCodeItems
                    .Select(i => new {
                        spireinventoryid = i.SpireInventoryId, customerid = futureOrder.CustomerId,
                        customeritemcode = i.CustomerItemCode
                    });

                await connection.ExecuteAsync(
                    "SELECT set_product_customer_defaults(@spireinventoryid, @customerid, @customeritemcode)",
                    defaultParams);
            }
        }

        if (futureOrder.ShipToId.HasValue) {
            var defaultParams = futureOrder.Items
                .Where(i => !i.IsBlanket)
                // NOTE: postgres' function parameters are case-sensitive, and lowercase: https://stackoverflow.com/a/64711380
                .Select(i => new {
                    spireinventoryid = i.SpireInventoryId, shiptoid = futureOrder.ShipToId, haspotcover = i.HasPotCover,
                    potcover = i.PotCover,
                    upc = i.Upc, weightsandmeasures = i.WeightsAndMeasures, retail = i.Retail, unitprice = i.UnitPrice,
                    customeritemcode = i.CustomerItemCode
                });
            await connection.ExecuteAsync(
                "SELECT set_product_ship_to_defaults(@spireinventoryid, @shiptoid, @haspotcover, @potcover, @upc, @weightsandmeasures, @retail, @unitprice, @customeritemcode)",
                defaultParams);
        }

        var vendorDefaultParams = futureOrder.Items.Where(i => i.VendorId.HasValue)
            .Select(i => new { VendorId = i.VendorId.GetValueOrDefault(), i.CreatePrebook });
        await connection.ExecuteAsync("INSERT INTO vendor_defaults (vendor_id, create_prebook) " +
                                      "VALUES (@vendorId, @createPrebook) " +
                                      "ON CONFLICT (vendor_id) DO " +
                                      "UPDATE SET create_prebook = @createPrebook;", vendorDefaultParams);

        return id;

    }

    public async Task<Dictionary<int, int>> Update(FutureOrderUpdate futureOrder, string? who,
        bool customerItemCodeByShipTo)
    {
        var itemMap = new Dictionary<int, int>();
        var now = DateTime.Now;
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var sql = @"UPDATE future_orders 
        SET
            required_date = @requiredDate, arrival_date = @arrivalDate, season_name = @seasonName, customer_id = @customerId, customer_name = @customerName, 
                    ship_to_id = @shipToId, ship_to_name = @shipToName, box_code = @boxCode, customer_purchase_order_number = @customerPurchaseOrderNumber,
                    freight_per_case = @freightPerCase, freight_per_load = @freightPerLoad, freight_is_actual = @freightIsActual, salesperson_id = @salespersonId,
                    salesperson_name = @salespersonName, spire_notes = @spireNotes, grower_item_notes = @growerItemNotes,
                    ship_via_id = @shipViaId, ship_via_name = @shipViaName, requires_labels = @requiresLabels, modified = @now, modified_by = @who
        WHERE
            id = @id";

        var futureOrderParams = new {
            futureOrder.Id, futureOrder.RequiredDate, futureOrder.ArrivalDate, futureOrder.SeasonName,
            futureOrder.CustomerId, futureOrder.CustomerName,
            futureOrder.ShipToId, futureOrder.ShipToName, futureOrder.BoxCode, futureOrder.CustomerPurchaseOrderNumber,
            futureOrder.FreightPerCase, futureOrder.FreightPerLoad, futureOrder.FreightIsActual, futureOrder.SalespersonId,
            futureOrder.SalespersonName, futureOrder.SpireNotes, futureOrder.GrowerItemNotes,
            futureOrder.ShipViaId, futureOrder.ShipViaName,
            futureOrder.RequiresLabels, now, who
        };
        await connection.ExecuteAsync(sql, futureOrderParams, transaction: tx);

        sql = "DELETE FROM future_order_comments WHERE future_order_id = @id;";
        var deleteParams = new { futureOrder.Id };

        await connection.ExecuteAsync(sql, deleteParams, transaction: tx);

        var comments = futureOrder.Comments
            .Where(c => !string.IsNullOrWhiteSpace(c.Comments))
            .ToList();

        if (comments.Any()) {
            var commentSql =
                @"INSERT INTO future_order_comments (future_order_id, comments, is_standard_comment, created, created_by) 
                            VALUES (@id, @comments, @isStandardComment, @created, @createdBy);";
            var commentParams = comments.Select(c => new {
                futureOrder.Id, c.Comments, c.IsStandardComment, Created = c.Created ?? now,
                CreatedBy = string.IsNullOrWhiteSpace(c.CreatedBy) ? who : c.CreatedBy
            });
            await connection.ExecuteAsync(commentSql, commentParams, transaction: tx);
        }

        var inserts = futureOrder.Items.Where(i => i.Id <= 0).ToList();
        var updates = futureOrder.Items.Where(i => i.Id > 0).ToList();

        sql = "DELETE FROM future_order_items WHERE future_order_id = @id";
        if (updates.Any()) {
            var submitted = string.Join(", ", updates.Select(i => i.Id));
            sql += $" AND id NOT IN ({submitted})";
        }

        sql += ";";
        await connection.ExecuteAsync(sql, deleteParams, transaction: tx);

        var existingVendors = (await connection.QueryAsync<(int id, int vendorId)>(
            "SELECT id, vendor_id FROM prebooks WHERE future_order_id = @id AND vendor_id IS NOT NULL AND deleted IS NULL;",
            new { futureOrder.Id })).ToList();

        // get the prebooks for split orders. the future_order_id of the prebook won't match this future order, but the future_order_item_id will match an item.
        var splitPrebooks = (await connection.QueryAsync<PrebookDetail>(
            "select p.* from prebooks p join prebook_items pi on p.id = pi.prebook_id join future_order_items fi on pi.future_order_item_id = fi.id where fi.future_order_id = @id;",
            new { futureOrder.Id })).ToList();

        splitPrebooks
            .Where(p => p.VendorId.HasValue)
            .Select(p => (p.Id, p.VendorId!.Value))
            .ToList()
            .ForEach(p => {
                if (!existingVendors.Contains(p)) {
                    existingVendors.Add(p);
                }
            });

        var vendorIds = existingVendors.Select(v => v.vendorId).Distinct().ToList();
        var vendors = futureOrder.Items
            .Where(i => i.VendorId.HasValue && i.CreatePrebook && !vendorIds.Contains(i.VendorId.Value))
            .Select(i => i.VendorId.GetValueOrDefault())
            .Distinct()
            .ToList();
        var vendorPrebooks = new Dictionary<int, int>();
        existingVendors.ForEach(v => {
            if (!vendorPrebooks.ContainsKey(v.vendorId)) {
                vendorPrebooks.Add(v.vendorId, v.id);
            }
        });

        var prebookItemSql =
            @"INSERT INTO prebook_items(prebook_id, spire_inventory_id, spire_part_number, description, order_quantity,
                has_pot_cover, pot_cover, date_code, upc, weights_and_measures, retail, upgrade_sheet, comments, is_approximate, blanket_item_id, future_order_item_id,
                boekestyn_plant_id, boekestyn_customer_abbreviation, upgrade_labour_hours, upgrade_product_coming_from,
                boekestyn_quantity_per_finished_item, special_price, grower_item_notes)
            VALUES (@prebookId, @spireInventoryId, @spirePartNumber, @description, @orderQuantity,
                @hasPotCover, @potCover, @dateCode, @upc, @weightsAndMeasures, @retail, @upgradeSheet, @comments, @isApproximate, @blanketItemId, @itemId,
                @boekestynPlantId, @boekestynCustomerAbbreviation, @upgradeLabourHours, @vendorName, @quantityPerFinishedItem, @specialPrice, @growerItemNotes) RETURNING id;";

        var boekestynProductSql =
            @"INSERT INTO prebook_item_boekestyn_products (prebook_item_id, boekestyn_plant_id, boekestyn_customer_abbreviation, quantity_per_finished_item) 
                        VALUES (@prebookItemId, @boekestynPlantId, @boekestynCustomerAbbreviation, @quantityPerFinishedItem);";

        foreach (var vendor in vendors) {
            var items = futureOrder.Items.Where(i => i.CreatePrebook && i.VendorId == vendor).ToList();
            var firstItem = items.FirstOrDefault();
            if (firstItem != null) {
                var prebookSql =
                    @"INSERT INTO prebooks (future_order_id, required_date, is_blanket, season_name, box_code, vendor_id, vendor_name,
        salesperson_id, salesperson_name, customer_id, customer_name, ship_to_id, ship_to_name, grower_item_notes, created, created_by, modified, modified_by)
    VALUES (@id, @requiredDate, @isBlanket, @seasonName, @boxCode, @vendorId, @vendorName,
        @salespersonId, @salespersonName, @customerId, @customerName, @shipToId, @shipToName, @growerItemNotes, @now, @who, @now, @who)
    RETURNING id;";
                var prebookParams = new {
                    futureOrder.Id, futureOrder.RequiredDate, firstItem.IsBlanket, futureOrder.SeasonName,
                    futureOrder.BoxCode, firstItem.VendorId, firstItem.VendorName,
                    futureOrder.SalespersonId, futureOrder.SalespersonName, futureOrder.CustomerId,
                    futureOrder.CustomerName, futureOrder.ShipToId, futureOrder.ShipToName, futureOrder.GrowerItemNotes,
                    now, who
                };
                var prebookId = await connection.ExecuteScalarAsync<int>(prebookSql, prebookParams, transaction: tx);
                vendorPrebooks.Add(vendor, prebookId);
            }
        }

        if (inserts.Any()) {
            var itemSql =
                @"INSERT INTO future_order_items (future_order_id, sort_order, vendor_id, vendor_name, spire_inventory_id, spire_part_number, description, 
                        order_quantity, is_approximate, blanket_item_id, has_pot_cover, pot_cover, date_code, upc, weights_and_measures, 
                        retail, use_availability_pricing, unit_price, customer_item_code, upgrade_sheet, phyto_required, phyto_ordered, comments, boekestyn_plant_id,
                        boekestyn_customer_abbreviation, special_price, grower_item_notes)
                VALUES (@id, @sortOrder, @vendorId, @vendorName, @spireInventoryId, @spirePartNumber, @description, 
                        @orderQuantity, @isApproximate, @blanketItemId, @hasPotCover, @potCover, @dateCode, @upc, @weightsAndMeasures, 
                        @retail, @useAvailabilityPricing, @unitPrice, @customerItemCode, @upgradeSheet, @phytoRequired, @phytoOrdered, @comments, @boekestynPlantId,
                        @boekestynCustomerAbbreviation, @specialPrice, @growerItemNotes)
                RETURNING id;";

            foreach (var item in inserts) {
                var itemsParams = new {
                    futureOrder.Id, item.SortOrder, item.VendorId, item.VendorName, item.SpireInventoryId,
                    item.SpirePartNumber, item.Description,
                    item.OrderQuantity, item.IsApproximate, item.BlanketItemId, item.HasPotCover, item.PotCover,
                    item.DateCode, item.Upc,
                    item.WeightsAndMeasures, item.Retail, item.UseAvailabilityPricing, item.UnitPrice,
                    item.CustomerItemCode, item.UpgradeSheet, item.PhytoRequired, item.PhytoOrdered,
                    item.Comments, item.BoekestynPlantId, item.BoekestynCustomerAbbreviation, item.SpecialPrice, item.GrowerItemNotes
                };
                var itemId = await connection.ExecuteScalarAsync<int>(itemSql, itemsParams, transaction: tx);
                itemMap.Add(item.Id, itemId);

                if (item.VendorId.HasValue && vendorPrebooks.ContainsKey(item.VendorId.Value)) {
                    var prebookId = vendorPrebooks[item.VendorId.Value];
                    // don't include the over & above comment to boeks
                    var itemComments = item.Comments;
                    if (item.VendorId == Constants.BoekestynVendorId) {
                        itemComments = itemComments?.Replace(Constants.OverAndAboveBlanketComment, "");
                    }
                    var itemParams = new {
                        prebookId, item.SpireInventoryId, item.SpirePartNumber, item.Description, item.OrderQuantity,
                        item.HasPotCover, item.PotCover,
                        item.DateCode, item.Upc, item.WeightsAndMeasures, item.Retail, item.UpgradeSheet, comments = itemComments,
                        item.IsApproximate, item.BlanketItemId, itemId,
                        item.BoekestynPlantId, item.BoekestynCustomerAbbreviation, item.UpgradeLabourHours,
                        item.VendorName,
                        item.QuantityPerFinishedItem, item.SpecialPrice, item.GrowerItemNotes
                    };

                    var prebookItemId =
                        await connection.ExecuteScalarAsync<int>(prebookItemSql, itemParams, transaction: tx);

                    if (item.BoekestynProducts.Any()) {
                        await connection.ExecuteAsync(boekestynProductSql,
                            item.BoekestynProducts.Select(p => new {
                                prebookItemId, p.BoekestynPlantId, p.BoekestynCustomerAbbreviation,
                                p.QuantityPerFinishedItem
                            }), transaction: tx);
                    }
                }
            }
        }

        if (updates.Any()) {
            sql = @"UPDATE future_order_items
            SET
                sort_order = @sortOrder, vendor_id = @vendorId, vendor_name = @vendorName, spire_inventory_id = @spireInventoryId, spire_part_number = @spirePartNumber, description = @description, 
                        order_quantity = @orderQuantity, is_approximate = @isApproximate, blanket_item_id = @blanketItemId, has_pot_cover = @hasPotCover, pot_cover = @potCover, date_code = @dateCode,
                        upc = @upc, weights_and_measures = @weightsAndMeasures, retail = @retail, use_availability_pricing = @useAvailabilityPricing, unit_price = @unitPrice,
                        customer_item_code = @customerItemCode, upgrade_sheet = @upgradeSheet, phyto_required = @phytoRequired, phyto_ordered = @phytoOrdered, comments = @comments,
                        boekestyn_plant_id = @boekestynPlantId, boekestyn_customer_abbreviation = @boekestynCustomerAbbreviation, special_price = @specialPrice, grower_item_notes = @growerItemNotes
            WHERE id = @id;";
            var updateParams = updates.Select(item => new {
                item.Id, item.SortOrder, item.VendorId, item.VendorName, item.SpireInventoryId, item.SpirePartNumber,
                item.Description,
                item.OrderQuantity, item.IsApproximate, item.BlanketItemId, item.HasPotCover, item.PotCover,
                item.DateCode, item.Upc,
                item.WeightsAndMeasures, item.Retail, item.UseAvailabilityPricing, item.UnitPrice,
                item.CustomerItemCode, item.UpgradeSheet, item.PhytoRequired, item.PhytoOrdered, item.Comments,
                item.BoekestynPlantId, item.BoekestynCustomerAbbreviation, item.SpecialPrice, item.GrowerItemNotes
            });
            await connection.ExecuteAsync(sql, updateParams, transaction: tx);

            foreach (var item in updates) {
                if (item.CreatePrebook && item.VendorId.HasValue && vendorPrebooks.ContainsKey(item.VendorId.Value)) {
                    var prebookId = vendorPrebooks[item.VendorId.Value];
                    var itemParams = new {
                        prebookId, item.SpireInventoryId, item.SpirePartNumber, item.Description, item.OrderQuantity,
                        item.HasPotCover, item.PotCover,
                        item.DateCode, item.Upc, item.WeightsAndMeasures, item.Retail, item.Comments, item.UpgradeSheet,
                        item.IsApproximate, item.BlanketItemId, itemId = item.Id,
                        item.BoekestynPlantId, item.BoekestynCustomerAbbreviation, item.UpgradeLabourHours,
                        item.VendorName,
                        item.QuantityPerFinishedItem, item.SpecialPrice, item.GrowerItemNotes
                    };

                    await connection.ExecuteAsync(prebookItemSql, itemParams, transaction: tx);
                }

                itemMap.Add(item.Id, item.Id);
            }
        }

        await tx.CommitAsync();

        if (futureOrder.CustomerId.HasValue) {
            var customerItemCodeItems =
                futureOrder.Items.Where(i => !string.IsNullOrWhiteSpace(i.CustomerItemCode)).ToList();

            if (customerItemCodeByShipTo && futureOrder.ShipToId.HasValue) {
                var defaultParams = customerItemCodeItems
                    .Select(i => new {
                        spireinventoryid = i.SpireInventoryId, shiptoid = futureOrder.ShipToId,
                        haspotcover = i.HasPotCover, potcover = i.PotCover,
                        upc = i.Upc, weightsandmeasures = i.WeightsAndMeasures, retail = i.Retail,
                        unitprice = i.UnitPrice, customeritemcode = i.CustomerItemCode
                    });
                await connection.ExecuteAsync(
                    "SELECT set_product_ship_to_defaults(@spireinventoryid, @shiptoid, @haspotcover, @potcover, @upc, @weightsandmeasures, @retail, @unitprice, @customeritemcode)",
                    defaultParams);
            } else if (!customerItemCodeByShipTo) {
                var defaultParams = customerItemCodeItems
                    .Select(i => new {
                        spireinventoryid = i.SpireInventoryId, customerid = futureOrder.CustomerId,
                        customeritemcode = i.CustomerItemCode
                    });
                await connection.ExecuteAsync(
                    "SELECT set_product_customer_defaults(@spireinventoryid, @customerid, @customeritemcode)",
                    defaultParams);
            }
        }

        if (futureOrder.ShipToId.HasValue) {
            var defaultParams = futureOrder.Items
                .Where(i => !i.IsBlanket)
                .Select(i => new {
                    spireinventoryid = i.SpireInventoryId, shiptoid = futureOrder.ShipToId, haspotcover = i.HasPotCover,
                    potcover = i.PotCover, upc = i.Upc,
                    weightsandmeasures = i.WeightsAndMeasures, retail = i.Retail, unitprice = i.UnitPrice
                });
            await connection.ExecuteAsync(
                "SELECT set_product_ship_to_defaults(@spireinventoryid, @shiptoid, @haspotcover, @potcover, @upc, @weightsandmeasures, @retail, @unitprice)",
                defaultParams);
        }

        var vendorDefaultParams = inserts.Where(i => i.VendorId.HasValue)
            .Select(i => new { VendorId = i.VendorId.GetValueOrDefault(), i.CreatePrebook });
        await connection.ExecuteAsync("INSERT INTO vendor_defaults (vendor_id, create_prebook) " +
                                      "VALUES (@vendorId, @createPrebook) " +
                                      "ON CONFLICT (vendor_id) DO " +
                                      "UPDATE SET create_prebook = @createPrebook;", vendorDefaultParams);

        return itemMap;
    }

    public async Task<int> Split(int originalFutureOrderId, FutureOrderSplit split, string? who)
    {
        var now = DateTime.Now;
        await using var connection = GetConnection();

        var existingItems = (await connection.QueryAsync<FutureOrderDetailItem>(
                "SELECT * FROM future_order_items WHERE future_order_id = @originalFutureOrderId;",
                new { originalFutureOrderId })
            ).ToList();
        var existingPrebooks = (await connection.QueryAsync<PrebookDetail>(
            "SELECT * FROM prebooks p WHERE p.future_order_id = @originalFutureOrderId AND deleted is null;",
            new { originalFutureOrderId })).ToList();
        var vendorPrebooks = new Dictionary<int, int>();

        foreach (var prebook in existingPrebooks) {
            if (!vendorPrebooks.ContainsKey(prebook.VendorId.GetValueOrDefault())) {
                vendorPrebooks.Add(prebook.VendorId.GetValueOrDefault(), prebook.Id);
            }
        }

        var existingPrebookItems = (await connection.QueryAsync<PrebookItemWithPrebookId>(
            "SELECT i.* FROM prebooks p JOIN prebook_items i on p.id = i.prebook_id join future_order_items foi on i.future_order_item_id = foi.id WHERE foi.future_order_id = @originalFutureOrderId AND p.deleted is null;",
            new { originalFutureOrderId })).ToList();

        // start the transaction
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var sql =
            @"UPDATE future_orders SET is_parent_order = True, modified = @now, modified_by = @who WHERE id = @originalFutureOrderId";
        await connection.ExecuteAsync(sql, new { originalFutureOrderId, now, who }, transaction: tx);

        int id;

        if (split.ExistingFutureOrderId.HasValue) {
            id = split.ExistingFutureOrderId.Value;
            sql =
                "UPDATE future_orders SET parent_order_id = @originalFutureOrderId, modified = @now, modified_by = @who WHERE id = @id";
            await connection.ExecuteAsync(sql, new { id, originalFutureOrderId, now, who }, transaction: tx);
        } else {
            // create the new Future Order
            sql = @"INSERT INTO future_orders (required_date, arrival_date, season_name, customer_id, customer_name, 
                        ship_to_id, ship_to_name, box_code, customer_purchase_order_number, freight_per_case, freight_per_load, freight_is_actual, salesperson_id, salesperson_name, 
                        spire_notes, grower_item_notes, ship_via_id, ship_via_name, requires_labels, parent_order_id, created, created_by, modified, modified_by)
                    VALUES (@requiredDate, @arrivalDate, @seasonName, @customerId, @customerName, 
                        @shipToId, @shipToName, @boxCode, @customerPurchaseOrderNumber, @freightPerCase, @freightPerLoad, @freightIsActual, @salespersonId, @salespersonName, 
                        @spireNotes, @growerItemNotes, @shipViaId, @shipViaName, @requiresLabels, @originalFutureOrderId, @now, @who, @now, @who)
                        RETURNING id;";
            var futureOrderParams = new {
                split.RequiredDate, split.ArrivalDate, split.SeasonName, split.CustomerId, split.CustomerName,
                split.ShipToId, split.ShipToName, split.BoxCode, split.CustomerPurchaseOrderNumber, split.FreightPerCase, split.FreightPerLoad, split.FreightIsActual,
                split.SalespersonId, split.SalespersonName, split.SpireNotes, split.GrowerItemNotes, split.ShipViaId, split.ShipViaName,
                split.RequiresLabels, originalFutureOrderId, now, who
            };
            id = await connection.ExecuteScalarAsync<int>(sql, futureOrderParams, transaction: tx);

            var comments = split.Comments.Where(c => !string.IsNullOrWhiteSpace(c.Comments)).ToList();
            var commentSql =
                @"INSERT INTO future_order_comments (future_order_id, comments, is_standard_comment, created, created_by) 
                            VALUES (@id, @comments, @isStandardComment, @now, @who);";
            var commentParams = comments.Select(c => new { id, c.Comments, c.IsStandardComment, now, who });
            await connection.ExecuteAsync(commentSql, commentParams, transaction: tx);
        }

        var updateItemSql = @"UPDATE future_order_items SET order_quantity = @newQuantity WHERE id = @id;";
        var itemSql =
            @"INSERT INTO future_order_items (future_order_id, sort_order, vendor_id, vendor_name, spire_inventory_id, spire_part_number, description, 
                            order_quantity, is_approximate, blanket_item_id, has_pot_cover, pot_cover, date_code, upc, weights_and_measures, 
                            retail, use_availability_pricing, unit_price, customer_item_code, upgrade_sheet, phyto_required, comments, boekestyn_plant_id,
                            boekestyn_customer_abbreviation, special_price, grower_item_notes)
                    VALUES (@id, @sortOrder, @vendorId, @vendorName, @spireInventoryId, @spirePartNumber, @description, 
                            @orderQuantity, @isApproximate, @blanketItemId, @hasPotCover, @potCover, @dateCode, @upc, @weightsAndMeasures, 
                            @retail, @useAvailabilityPricing, @unitPrice, @customerItemCode, @upgradeSheet, @phytoRequired, @comments, @boekestynPlantId,
                            @boekestynCustomerAbbreviation, @specialPrice, @growerItemNotes)
                    RETURNING id;";
        var prebookItemSql =
            @"INSERT INTO prebook_items(prebook_id, spire_inventory_id, spire_part_number, description, order_quantity,
                            has_pot_cover, pot_cover, date_code, upc, weights_and_measures, retail, upgrade_sheet, comments, is_approximate, blanket_item_id, future_order_item_id,
                            boekestyn_plant_id, boekestyn_customer_abbreviation, upgrade_labour_hours, upgrade_product_coming_from, boekestyn_quantity_per_finished_item, special_price, grower_item_notes)
                        VALUES (@prebookId, @spireInventoryId, @spirePartNumber, @description, @orderQuantity,
                            @hasPotCover, @potCover, @dateCode, @upc, @weightsAndMeasures, @retail, @upgradeSheet, @comments, @isApproximate, @blanketItemId, @itemId,
                            @boekestynPlantId, @boekestynCustomerAbbreviation, @upgradeLabourHours, @upgradeProductComingFrom, @quantityPerFinishedItem, @specialPrice, @growerItemNotes) RETURNING id;";

        var boekestynProductSql =
            @"INSERT INTO prebook_item_boekestyn_products (prebook_item_id, boekestyn_plant_id, boekestyn_customer_abbreviation, quantity_per_finished_item)
                SELECT @prebookItemId, b.boekestyn_plant_id, b.boekestyn_customer_abbreviation, b.quantity_per_finished_item
                FROM prebook_item_boekestyn_products b WHERE b.prebook_item_id = @id;";

        foreach (var item in split.Items) {
            var existing = existingItems.FirstOrDefault(i => i.Id == item.OriginalId);
            if (existing != null) {

                // create the new Future Order Item
                var existingPrebookItem =
                    existingPrebookItems.FirstOrDefault(i => i.FutureOrderItemId == item.OriginalId);

                var itemsParams = new {
                    id, existing.SortOrder, existing.VendorId, existing.VendorName, existing.SpireInventoryId,
                    existing.SpirePartNumber,
                    existing.Description,
                    item.OrderQuantity, existing.IsApproximate, existing.BlanketItemId, existing.HasPotCover,
                    existing.PotCover,
                    existing.DateCode, existing.Upc,
                    existing.WeightsAndMeasures, existing.Retail, existing.UseAvailabilityPricing,
                    existing.UnitPrice,
                    existing.CustomerItemCode, existing.UpgradeSheet, existing.PhytoRequired,
                    existing.Comments, existing.BoekestynPlantId, existing.BoekestynCustomerAbbreviation,
                    existing.SpecialPrice, existing.GrowerItemNotes
                };
                var itemId = await connection.ExecuteScalarAsync<int>(itemSql, itemsParams, transaction: tx);
                var newQuantity = Math.Max(existing.OrderQuantity - item.OrderQuantity, 0);

                await connection.ExecuteAsync(updateItemSql, new { existing.Id, newQuantity }, transaction: tx);

                if (existingPrebookItem != null) {
                    sql = @"UPDATE prebooks SET modified = @now, modified_by = @who WHERE id = @prebookId";
                    await connection.ExecuteAsync(sql, new { existingPrebookItem.PrebookId, now, who },
                        transaction: tx);

                    // if the item is completely split, we can just adjust the future_order_item_id field.
                    // otherwise, we need to create a new prebook item
                    if (newQuantity == 0) {
                        await connection.ExecuteAsync(
                            "UPDATE prebook_items SET future_order_item_id = @itemId WHERE id = @id;",
                            new { itemId, existingPrebookItem.Id }, transaction: tx);
                    } else {
                        // update the old item
                        await connection.ExecuteAsync(
                            "UPDATE prebook_items SET order_quantity = @newQuantity WHERE id = @id;",
                            new { newQuantity, existingPrebookItem.Id }, transaction: tx);

                        // don't include the over & above comment to Boeks
                        var itemComments = existingPrebookItem.Comments;
                        if (existing.VendorId == Constants.BoekestynVendorId) {
                            itemComments = itemComments?.Replace(Constants.OverAndAboveBlanketComment, "");
                        }

                        // create a new one
                        var itemParams = new {
                            existingPrebookItem.PrebookId, existingPrebookItem.SpireInventoryId,
                            existingPrebookItem.SpirePartNumber, existingPrebookItem.Description,
                            item.OrderQuantity,
                            existingPrebookItem.HasPotCover, existingPrebookItem.PotCover,
                            existingPrebookItem.DateCode, existingPrebookItem.Upc,
                            existingPrebookItem.WeightsAndMeasures,
                            existingPrebookItem.Retail, existingPrebookItem.UpgradeSheet,
                            comments = itemComments,
                            existingPrebookItem.IsApproximate, existingPrebookItem.BlanketItemId, itemId,
                            existingPrebookItem.BoekestynPlantId, existingPrebookItem.BoekestynCustomerAbbreviation,
                            existingPrebookItem.UpgradeLabourHours,
                            existingPrebookItem.UpgradeProductComingFrom, existingPrebookItem.QuantityPerFinishedItem,
                            existingPrebookItem.SpecialPrice, existingPrebookItem.GrowerItemNotes
                        };

                        var prebookItemId =
                            await connection.ExecuteScalarAsync<int>(prebookItemSql, itemParams, transaction: tx);

                        // and add the Boekestyn Products
                        await connection.ExecuteAsync(boekestynProductSql,
                            new { prebookItemId, existingPrebookItem.Id }, transaction: tx);
                    }
                }
            }
        }

        await tx.CommitAsync();

        return id;
    }

    public async Task<IEnumerable<FutureOrderChild>> GetChildren(int id) =>
        await GetConnection().QueryAsync<FutureOrderChild>("SELECT id, required_date, customer_name FROM future_orders WHERE parent_order_id = @id AND deleted is null;",
            new { id });

    public async Task SetSpireSalesOrder(int id, int spireSalesOrderId, string spireSalesOrderNumber, string who) =>
        await GetConnection().ExecuteAsync(
            "UPDATE future_orders SET spire_sales_order_id = @spireSalesOrderId, spire_sales_order_number = @spireSalesOrderNumber, sent_to_spire = @now, sent_to_spire_by = @who WHERE id = @id;",
            new { id, spireSalesOrderId, spireSalesOrderNumber, now = DateTime.Now, who });

    public async Task<VendorDefault> GetVendorDefault(int vendorId) =>
        (await GetConnection().QueryFirstOrDefaultAsync<VendorDefault>(
            "SELECT * FROM vendor_defaults " +
            "WHERE vendor_id = @vendorId",
            new { vendorId })) ?? new VendorDefault { VendorId = vendorId };

    public async Task<IEnumerable<DeletedFutureOrder>> DeletedOrders() =>
        await GetConnection().QueryAsync<DeletedFutureOrder>(
            "SELECT * FROM future_orders " +
            "WHERE deleted IS NOT NULL ORDER BY id DESC;");

    public async Task Delete(int id, string who) =>
        await GetConnection().ExecuteAsync(
            "SELECT future_order_delete(@id_param, @who);", new { id_param = id, who });

    public async Task Undelete(int id) =>
        await GetConnection().ExecuteAsync(
            "UPDATE future_orders " +
            "SET deleted = NULL, deleted_by = NULL " +
            "WHERE id = @id; " +
            "UPDATE prebooks " +
            "SET deleted = NULL, deleted_by = NULL " +
            "WHERE future_order_id = @id; ", new { id });

    public async Task ClearPhytoOrdered(int id) =>
        await GetConnection().ExecuteAsync(
            "UPDATE future_order_items SET phyto_ordered = false WHERE future_order_id = @id;",
            new { id });


    public async Task<List<FutureOrderDetail>> FutureOrdersForPrebook(int prebookId)
    {
        await using var multi = await GetConnection().QueryMultipleAsync(@"
select fo.* from future_orders fo where id in (select foi.future_order_id from future_order_items foi join prebook_items pi on foi.id = pi.future_order_item_id where pi.prebook_id = @prebookId);
select foi.* from future_order_items foi join prebook_items pi on foi.id = pi.future_order_item_id where pi.prebook_id = @prebookId;
",
            new { prebookId });

        var futureOrders = (await multi.ReadAsync<FutureOrderDetail>()).ToList();
        var items = (await multi.ReadAsync<FutureOrderDetailItem>()).ToLookup(i => i.FutureOrderId);

        foreach (var futureOrder in futureOrders.Where(o => items.Contains(o.Id))) {
            futureOrder.Items = items[futureOrder.Id].ToList();
        }

        return futureOrders;
    }

    private class PrebookItemWithPrebookId : PrebookDetailItem
    {
        public int PrebookId { get; set; }
        public string? UpgradeProductComingFrom { get; set; }
    }
}