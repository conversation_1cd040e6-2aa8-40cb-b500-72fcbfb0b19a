﻿using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace FloraPack.API.Spire;

public static class Filters
{
    public static string CreateFilter(params Filter[] items)
    {
        var collection = items.Select(i => new KeyValuePair<string, object>(i.Key, i.Value));
        var filter = new Dictionary<string, object>(collection);
        var json = JsonSerializer.Serialize(filter);
        var encoded = UrlEncoder.Default.Encode(json);
        return encoded;
    }

    public static JsonObject CreateILikeValue(string value) =>
        new() {
            ["$ilike"] = $"%{value}%"
        };

    public static JsonObject CreatePropertyValue(string propertyName, JsonNode value) =>
        new() {
            [propertyName] = value
        };

    public static Filter CreateOrValue(params JsonNode?[] values) =>
        new("$or", new JsonArray(values));

    public static Filter CreateAndValue(params JsonNode?[] values) =>
        new("$and", new JsonArray(values));

    public static JsonObject CreateGreaterThanOrEqualToValue(string propertyName, string value) =>
        new() {
            [propertyName] = new JsonObject {
                ["$gte"] = value
            }
        };

    public static JsonObject CreateLessThanOrEqualToValue(string propertyName, string value) =>
        new() {
            [propertyName] = new JsonObject {
                ["$lte"] = value
            }
        };

    public static Filter CreateGreaterThanOrEqualToFilter(string propertyName, string value) =>
        new(propertyName, new JsonObject() {
            ["$gte"] = value
        });

    public static Filter CreateLessThanOrEqualToFilter(string propertyName, string value) =>
        new(propertyName, new JsonObject() {
            ["$lte"] = value
        });
}

public record Filter(string Key, object Value);