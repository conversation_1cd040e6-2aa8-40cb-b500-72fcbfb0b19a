﻿using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Spire;

namespace FloraPack.API.Reports.CustomerConfirmations;

public class CustomerConfirmationFactory(FutureOrderRepository futureOrderRepository, SpireRepository spireRepository)
{
    // TODO: Replace with alternative report implementation
    public async Task<object?> CreateReport(int id, bool includeZeroQuantity)
    {
        // Temporarily disabled - GrapeCity ActiveReports not available
        return null;
    }

    /*
    public async Task<CustomerConfirmation?> CreateReportOriginal(int id, bool includeZeroQuantity)
    {
        var futureOrder = await futureOrderRepository.Detail(id);
        if (futureOrder == null) {
            return null;
        }

        var availabilityPricingIds = futureOrder.Items
            .Where(i => i.UseAvailabilityPricing)
            .Select(i => i.SpireInventoryId)
            .Distinct()
            .ToList();
        var prices = await spireRepository.GetItemPrices(futureOrder.CustomerId, futureOrder.ShipToId,
            futureOrder.RequiredDate, availabilityPricingIds);
        foreach (var price in prices) {
            futureOrder.Items
                .Where(i => i.UseAvailabilityPricing && i.SpireInventoryId == price.SpireInventoryItemId)
                .ToList()
                .ForEach(item => item.UnitPrice = price.Price);
        }

        var items = futureOrder.Items
            .Where(i => includeZeroQuantity || i.OrderQuantity > 0)
            .OrderBy(i => i.SortOrder)
            .Select(i => {
                return new FutureOrderSummaryReportFutureOrderItem {
                    SpirePartNumber = i.SpirePartNumber,
                    Description = i.Description,
                    OrderQuantity = i.OrderQuantity,
                    IsApproximate = i.IsApproximate,
                    PotCover = i.HasPotCover ? (i.PotCover ?? string.Empty) : string.Empty,
                    DateCode = i.DateCode ?? string.Empty,
                    Upc = i.Upc ?? string.Empty,
                    WeightsAndMeasures = i.WeightsAndMeasures,
                    Retail = i.Retail ?? string.Empty,
                    UnitPrice = i.UnitPrice,
                    CustomerItemCode = i.CustomerItemCode ?? string.Empty,
                    Comments = i.Comments ?? string.Empty
                };
            })
            .ToList();

        var reportFutureOrder = new FutureOrderSummaryReportFutureOrder {
            Id = futureOrder.Id,
            SpireSalesOrderNumber = futureOrder.SpireSalesOrderNumber ?? string.Empty,
            RequiredDate = futureOrder.RequiredDate,
            ArrivalDate = futureOrder.ArrivalDate.HasValue
                ? futureOrder.ArrivalDate.Value.ToString("MMM d, yyyy")
                : (futureOrder.SeasonName ?? string.Empty),
            CustomerName = futureOrder.CustomerName ?? string.Empty,
            ShipToName = futureOrder.ShipToName ?? string.Empty,
            BoxCode = futureOrder.BoxCode ?? string.Empty,
            CustomerPurchaseOrderNumber = futureOrder.CustomerPurchaseOrderNumber ?? string.Empty,
            SalespersonName = futureOrder.SalespersonName ?? string.Empty,
            ShipViaName = futureOrder.ShipViaName ?? string.Empty,
            TotalOrderQuantity = items.Sum(i => i.OrderQuantity),
            TotalPrice = items.Sum(i => i.UnitPrice.GetValueOrDefault() * i.OrderQuantity),
            SpireNotes = futureOrder.SpireNotes ?? string.Empty,
            FreightPerLoad = futureOrder.FreightPerLoad,
            FreightPerCase = futureOrder.FreightPerCase,
            FreightIsActual = futureOrder.FreightIsActual
        };

        var report = new CustomerConfirmation(reportFutureOrder, items);
        return report;
    }
    */
}