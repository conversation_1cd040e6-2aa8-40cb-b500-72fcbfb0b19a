﻿namespace FloraPack.API.Reports
{
    /// <summary>
    /// Summary description for FutureOrderSummaryItem.
    /// </summary>
    partial class FutureOrderSummaryItem
    {
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing) {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FutureOrderSummaryItem));
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.rtfCases = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.txtPackQuantity = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.rtfPotCover = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.txtUnitPrice = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfUPC = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfDateCode = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfRetail = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.txtWeightsAndMeasures = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtVendor = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfComments = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.txtSent = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.rtfDescription = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            ((System.ComponentModel.ISupportInitialize)(this.txtPackQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWeightsAndMeasures)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // detail
            // 
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.rtfCases,
            this.txtPackQuantity,
            this.rtfPotCover,
            this.txtUnitPrice,
            this.rtfUPC,
            this.rtfDateCode,
            this.rtfRetail,
            this.txtWeightsAndMeasures,
            this.txtVendor,
            this.rtfComments,
            this.txtSent,
            this.rtfDescription});
            this.detail.Height = 0.2F;
            this.detail.KeepTogether = true;
            this.detail.Name = "detail";
            this.detail.Format += new System.EventHandler(this.OnFormat);
            this.detail.BeforePrint += new System.EventHandler(this.OnBeforePrint);
            // 
            // txtCases
            // 
            this.rtfCases.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfCases.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfCases.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfCases.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfCases.Height = 0.2F;
            this.rtfCases.Left = 0F;
            this.rtfCases.Name = "rtfCases";
            this.rtfCases.RTF = resources.GetString("rtfCases.RTF");
            this.rtfCases.Text = "Cases";
            this.rtfCases.Top = 0F;
            this.rtfCases.Width = 0.5F;
            // 
            // txtPackQuantity
            // 
            this.txtPackQuantity.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.DataField = "PackQuantity";
            this.txtPackQuantity.Height = 0.2F;
            this.txtPackQuantity.Left = 2.5F;
            this.txtPackQuantity.Name = "txtPackQuantity";
            this.txtPackQuantity.OutputFormat = "#,##0";
            this.txtPackQuantity.Style = "font-size: 8pt; text-align: center";
            this.txtPackQuantity.Text = "Pack";
            this.txtPackQuantity.Top = 0F;
            this.txtPackQuantity.Width = 0.5F;
            // 
            // txtPotCover
            // 
            this.rtfPotCover.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPotCover.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPotCover.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPotCover.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPotCover.DataField = "PotCover";
            this.rtfPotCover.Height = 0.2F;
            this.rtfPotCover.Left = 3F;
            this.rtfPotCover.Name = "rtfPotCover";
            this.rtfPotCover.Text = "Cover";
            this.rtfPotCover.Top = 0F;
            this.rtfPotCover.Width = 1F;
            // 
            // txtUnitPrice
            // 
            this.txtUnitPrice.AutoReplaceFields = true;
            this.txtUnitPrice.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUnitPrice.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUnitPrice.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUnitPrice.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUnitPrice.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.txtUnitPrice.Height = 0.2F;
            this.txtUnitPrice.Html = resources.GetString("txtUnitPrice.Html");
            this.txtUnitPrice.Left = 4F;
            this.txtUnitPrice.Name = "txtUnitPrice";
            this.txtUnitPrice.Top = 0F;
            this.txtUnitPrice.Width = 0.75F;
            // 
            // txtUPC
            // 
            this.rtfUPC.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfUPC.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfUPC.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfUPC.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfUPC.DataField = "UPC";
            this.rtfUPC.Height = 0.2F;
            this.rtfUPC.Left = 4.75F;
            this.rtfUPC.Name = "rtfUPC";
            this.rtfUPC.Text = "UPC";
            this.rtfUPC.Top = 0F;
            this.rtfUPC.Width = 1F;
            // 
            // txtDateCode
            // 
            this.rtfDateCode.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDateCode.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDateCode.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDateCode.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDateCode.DataField = "DateCode";
            this.rtfDateCode.Height = 0.2F;
            this.rtfDateCode.Left = 5.75F;
            this.rtfDateCode.Name = "rtfDateCode";
            this.rtfDateCode.Text = "Date Code";
            this.rtfDateCode.Top = 0F;
            this.rtfDateCode.Width = 0.75F;
            // 
            // txtRetail
            // 
            this.rtfRetail.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfRetail.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfRetail.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfRetail.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfRetail.DataField = "Retail";
            this.rtfRetail.Height = 0.2F;
            this.rtfRetail.Left = 6.5F;
            this.rtfRetail.Name = "rtfRetail";
            this.rtfRetail.Text = "Retail";
            this.rtfRetail.Top = 0F;
            this.rtfRetail.Width = 0.75F;
            // 
            // txtWeightsAndMeasures
            // 
            this.txtWeightsAndMeasures.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasures.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasures.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasures.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasures.DataField = "=WeightsAndMeasures ? \"\" : \"\"";
            this.txtWeightsAndMeasures.Height = 0.2F;
            this.txtWeightsAndMeasures.Left = 7.25F;
            this.txtWeightsAndMeasures.Name = "txtWeightsAndMeasures";
            this.txtWeightsAndMeasures.Style = "font-family: Segoe UI Symbol; font-size: 8pt; text-align: center";
            this.txtWeightsAndMeasures.Text = "W&M";
            this.txtWeightsAndMeasures.Top = 0F;
            this.txtWeightsAndMeasures.Width = 0.5F;
            // 
            // txtVendor
            // 
            this.txtVendor.AutoReplaceFields = true;
            this.txtVendor.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtVendor.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtVendor.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtVendor.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtVendor.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.txtVendor.Height = 0.2F;
            this.txtVendor.Left = 7.75F;
            this.txtVendor.Name = "txtVendor";
            this.txtVendor.RTF = resources.GetString("txtVendor.RTF");
            this.txtVendor.Top = 0F;
            this.txtVendor.Width = 1.5F;
            // 
            // rtfComments
            // 
            this.rtfComments.AutoReplaceFields = true;
            this.rtfComments.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfComments.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfComments.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfComments.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfComments.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfComments.Height = 0.2F;
            this.rtfComments.Left = 9.25F;
            this.rtfComments.Name = "rtfComments";
            this.rtfComments.RTF = resources.GetString("rtfComments.RTF");
            this.rtfComments.Top = 0F;
            this.rtfComments.Width = 1.750001F;
            // 
            // txtSent
            // 
            this.txtSent.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtSent.Height = 0.2F;
            this.txtSent.Left = 0.5F;
            this.txtSent.Name = "txtSent";
            this.txtSent.Style = "font-family: Segoe UI Symbol; font-size: 8pt";
            this.txtSent.Text = "✉";
            this.txtSent.Top = 0F;
            this.txtSent.Width = 0.15F;
            // 
            // rtfDescription
            // 
            this.rtfDescription.AutoReplaceFields = true;
            this.rtfDescription.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfDescription.Height = 0.2F;
            this.rtfDescription.Html = resources.GetString("rtfDescription.Html");
            this.rtfDescription.Left = 0.5F;
            this.rtfDescription.Name = "rtfDescription";
            this.rtfDescription.Top = 0F;
            this.rtfDescription.Width = 2F;
            // 
            // FutureOrderSummaryItem
            // 
            this.MasterReport = false;
            this.CompatibilityMode = GrapeCity.ActiveReports.Document.CompatibilityModes.CrossPlatform;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 11F;
            this.Sections.Add(this.detail);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; color: Black; fon" +
            "t-family: \"Calibri\"; ddo-char-set: 186; font-size: 9pt", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Times New Roman; font-size: 14pt; font-weight: bold; font-style: ita" +
            "lic", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold", "Heading3", "Normal"));
            ((System.ComponentModel.ISupportInitialize)(this.txtPackQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWeightsAndMeasures)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfCases;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPackQuantity;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfPotCover;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox txtUnitPrice;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfUPC;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfRetail;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtWeightsAndMeasures;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox txtVendor;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfDateCode;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfComments;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSent;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfDescription;
    }
}
