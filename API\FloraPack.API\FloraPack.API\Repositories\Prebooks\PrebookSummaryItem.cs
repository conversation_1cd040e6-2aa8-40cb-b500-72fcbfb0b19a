﻿namespace FloraPack.API.Repositories.Prebooks;

public class PrebookSummaryItem
{
    public long Id { get; set; }
    public string? Customer { get; set; }
    public string? ShipTo { get; set; }
    public string? Vendor { get; set; }
    public string? Salesperson { get; set; }
    public string? Season { get; set; }
    public bool IsBlanket { get; set; }
    public string? Date { get; set; }
    public int? Week { get; set; }
    public int? WeekYear { get; set; }
    public int? Month { get; set; }
    public int? Year { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public long OrderQuantity { get; set; }
    public string PrebookIdList { get; set; } = string.Empty;
}