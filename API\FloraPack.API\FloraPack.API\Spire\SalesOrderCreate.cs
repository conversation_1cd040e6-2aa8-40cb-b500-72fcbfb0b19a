﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Spire;

public class SalesOrderCreate
{
    private const int KrogerCustomerId = 206;

    public SalesOrderCreate(int customerId, string shipToShipId, string salespersonCode, string? shipCode, DateTime requiredDate, DateTime arrivalDate, string? customerPo, string? shipSheetComment, IEnumerable<SalesOrderItem> items)
    {
        Customer = new SalesOrderCustomer(customerId);
        ShippingAddress = new SalesOrderShippingAddress(shipToShipId, shipCode);
        Salesperson = new SalesOrderSalesperson(salespersonCode);
        RequiredDate = requiredDate.ToString("yyyy-MM-dd");
        ShipDate = arrivalDate.ToString("yyyy-MM-dd");
        CustomerPo = customerPo;
        if (!string.IsNullOrWhiteSpace(shipSheetComment)) {
            UDF = new SalesOrderUserDefinedFields(shipSheetComment);
        }
        Items.AddRange(items);
    }

    [JsonPropertyName("customer")]
    public SalesOrderCustomer Customer { get; }

    [JsonPropertyName("shippingAddress")]
    public SalesOrderShippingAddress ShippingAddress { get; }

    [JsonPropertyName("salesperson")]
    public SalesOrderSalesperson Salesperson { get; }

    [JsonPropertyName("type")]
    public string Type => "O";

    [JsonPropertyName("requiredDate")]
    public string RequiredDate { get; }

    [JsonPropertyName("shipDate")]
    public string ShipDate { get; }

    [JsonPropertyName("customerPO")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? CustomerPo { get; }

    [JsonPropertyName("udf")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public SalesOrderUserDefinedFields? UDF { get; }

    [JsonPropertyName("items")]
    public List<SalesOrderItem> Items { get; } = new();

    public record SalesOrderCustomer(int id);

    public record SalesOrderSalesperson(string code);

    public class SalesOrderShippingAddress
    {
        public SalesOrderShippingAddress(string shipId, string? shipCode)
        {
            ShipId = shipId;
            ShipCode = shipCode;
        }

        [JsonPropertyName("shipId")]
        public string ShipId { get; set; }

        [JsonPropertyName("shipCode")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? ShipCode { get; }
    }

    public record SalesOrderUserDefinedFields(string comment);

    public class SalesOrderItem
    {
        public SalesOrderItem(int id, int sequence, int customerId, int inventoryId, int orderQuantity, string vendor, string? comment, string? potCover, string? upc, string? retail,
            string? dateCode, bool weightsAndMeasures, decimal? unitPrice, string? customerItemCode, decimal? specialPrice, int? prebookId, string? growerItemNotes)
        {
            Id = id;
            Sequence = sequence;
            Inventory = new SalesOrderItemInventory(inventoryId);
            OrderQuantity = orderQuantity;
            Vendor = vendor;

            var comments = new List<string>();
            if (!string.IsNullOrWhiteSpace(comment)) {
                comments.Add(comment);
            }

            if (!string.IsNullOrWhiteSpace(potCover)) {
                comments.Add(potCover);
            }

            if (!string.IsNullOrWhiteSpace(upc)) {
                comments.Add(upc);
            }

            if (!string.IsNullOrWhiteSpace(retail)) {
                comments.Add(retail);
            }

            if (!string.IsNullOrWhiteSpace(dateCode)) {
                comments.Add(dateCode);
            }

            if (weightsAndMeasures) {
                comments.Add("W&M's");
            }

            Comment = string.Join(" | ", comments);
            UnitPrice = unitPrice;
            ReferenceNumber = customerItemCode;

            if (prebookId.HasValue) {
                UDF = new SalesOrderItemUserDefinedFields(prebookId.Value, upc ?? string.Empty, customerId, specialPrice, growerItemNotes);
            }
        }

        public SalesOrderItem(int sequence, string comment)
        {
            Sequence = sequence;
            Comment = comment;
            Description = comment.Split("\n").FirstOrDefault();
        }

        [JsonIgnore]
        public int? Id { get; set; }

        [JsonPropertyName("sequence")]
        public int Sequence { get; }

        [JsonPropertyName("inventory")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public SalesOrderItemInventory? Inventory { get; }

        [JsonPropertyName("vendor")]
        public string? Vendor { get; }

        [JsonPropertyName("comment")]
        public string? Comment { get; }

        [JsonPropertyName("orderQty")]
        public int OrderQuantity { get; }

        [JsonPropertyName("backorderQty")]
        public int BackOrderQuantity => OrderQuantity;

        [JsonPropertyName("committedQty")]
        public int CommittedQuantity => 0;

        [JsonPropertyName("description")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Description { get; }

        [JsonPropertyName("unitPrice")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public decimal? UnitPrice { get; }

        [JsonPropertyName("referenceNo")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? ReferenceNumber { get; }

        [JsonPropertyName("udf")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public SalesOrderItemUserDefinedFields? UDF { get; }
    }

    public record SalesOrderItemInventory(int id);

    public class SalesOrderItemUserDefinedFields
    {
        public SalesOrderItemUserDefinedFields(int prebookId, string upc, int customerId, decimal? specialPrice, string? growerItemNotes)
        {
            PrebookId = prebookId.ToString("00000");

            // Kroger's UPCs are 14 digits, with a "10" prefix
            var upcPrefix = customerId == KrogerCustomerId ? "10" : "";
            UPC = upcPrefix + upc.Replace(" ", "").Replace("/", "").Replace("\\", "").Replace("-", "");

            //SpecialPrice = specialPrice?.ToString("F2");

            GrowerItemNotes = (string.IsNullOrWhiteSpace(growerItemNotes) ? string.Empty : growerItemNotes) +
                              (specialPrice.HasValue ? $" Special Price from Grower: {specialPrice.Value:C2}" : string.Empty);
        }

        [JsonPropertyName("prebook_id")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string PrebookId { get; }

        [JsonPropertyName("upc")]
        public string UPC { get; set; }

        [JsonPropertyName("special_price")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? SpecialPrice { get; }

        [JsonPropertyName("grower_item_notes")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? GrowerItemNotes { get; }
    }
}