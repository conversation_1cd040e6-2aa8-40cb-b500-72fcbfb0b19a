﻿namespace FloraPack.API.Repositories.FutureOrders;

public class DeletedFutureOrder
{
    public int Id { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? CustomerName { get; set; }
    public string? ShipToName { get; set; }
    public string? CustomerPurchaseOrderNumber { get; set; }
    public string? SalespersonName { get; set; }
    public string? SpireSalesOrderNumber { get; set; }
    public DateTime Deleted { get; set; }
    public string DeletedBy { get; set; } = string.Empty;
}