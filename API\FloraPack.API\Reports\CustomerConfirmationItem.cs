﻿using System;
using System.Collections.Generic;
using System.Web;

namespace FloraPack.API.Reports
{
    public partial class CustomerConfirmationItem : GrapeCity.ActiveReports.SectionReport
    {
        private int _formatIndex;

        public CustomerConfirmationItem()
        {
            InitializeComponent();
        }

        private void OnBeforePrint(object sender, EventArgs e)
        {
            var height = detail.Height;
            rtfCases.Height = height;
            rtfDescription.Height = height;
            txtPackQuantity.Height = height;
            txtPotCover.Height = height;
            txtUnitPrice.Height = height;
            txtDateCode.Height = height;
            txtUPC.Height = height;
            txtRetail.Height = height;
            txtWeightsAndMeasures.Height = height;
            rtfComments.Height = height;
        }

        private void OnFormat(object sender, EventArgs e)
        {
            if (DataSource is List<FutureOrderSummaryReportFutureOrderItem> items && _formatIndex < items.Count) {
                var item = items[_formatIndex];
                var html =
                    "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='margin-left: 10px; font-family: Calibri; font-size: 8pt;'>" +
                    HttpUtility.HtmlEncode(item.Description) + "</div>";

                if (!string.IsNullOrWhiteSpace(item.CustomerItemCode)) {
                    html += $"<div style='font-family: Calibri; font-size: 8pt; font-style: italic; margin-left: 10px;'>Item Code {item.CustomerItemCode}</div>";
                }

                html += "</body></html>";
                rtfDescription.Html = html;
                
                var quantityHtml =
                        "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='text-align: center; font-family: Calibri; font-size: 8pt;'>" +
                        item.OrderQuantityDisplay + "</div></body></html>";
                rtfCases.Html = quantityHtml;

                html = "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='font-family: Calibri; font-size: 8pt; text-align: center;'>" +
                       (item.UnitPrice.HasValue ? item.UnitPrice.Value.ToString("C2") : "") + "</div>";
                if (item.UnitPrice.HasValue && item.PackQuantity.HasValue && item.PackQuantity.Value > 1) {
                    html += $"<div style='font-family: Calibri; font-size: 8pt; text-align: center; font-style: italic;'>{item.UnitPrice.Value / item.PackQuantity.Value:C2}/ea</div>";
                }

                html += "</body></html>";
                txtUnitPrice.Html = html;

                var commentHtml = "<html><body style='margin: 0; padding: 0 0 10px 0;'>";

                var comments = HttpUtility.HtmlEncode(item.Comments ?? string.Empty).Replace("\n", "<br>");
                if (!string.IsNullOrWhiteSpace(comments)) {
                    comments += "<br>";
                }

                if (!string.IsNullOrWhiteSpace(comments)) {
                    commentHtml += $"<div style='font-family: Calibri; font-size: 8pt;'>{comments}</div>";
                }

                commentHtml += "</body></html>";
                rtfComments.Html = commentHtml;

                _formatIndex++;
            }
        }
    }
}
