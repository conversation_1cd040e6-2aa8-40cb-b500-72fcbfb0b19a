﻿using FloraPack.API.Utilities;

namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class ItemListItem
{
    public int Id { get; set; }
    public int PrebookId { get; set; }
    public int? FutureOrderId { get; set; }
    public string? RequiredDate { get; set; }
    public string? Season { get; set; }
    public string? Customer { get; set; }
    public string? ShipTo { get; set; }
    public string? Comments { get; set; }
    public string? GrowerItemNotes { get; set; }
    public string? ItemGrowerItemNotes { get; set; }
    public string? SpirePartNumber { get; set; }
    public string? Description { get; set; }
    public string? BoxCode { get; set; }
    public string? PotCover { get; set; }
    public string? Upc { get; set; }
    public string? DateCode { get; set; }
    public string? Retail { get; set; }
    public bool WeightsAndMeasures { get; set; }
    public string? ItemComments { get; set; }
    public bool Priority { get; set; }
    public string? UpcPrinted { get; set; }
    public string? UpcPrintedPrev { get; set; }
    public bool IsApproximate { get; set; }
    public long CaseCount { get; set; }
    public string Created { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public string Modified { get; set; } = string.Empty;
    public string ModifiedBy { get; set; } = string.Empty;

    public int? PackQuantity => PackQuantityParser.Parse(Description);
}