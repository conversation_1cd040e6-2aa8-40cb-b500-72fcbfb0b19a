﻿using System.Text.Json.Serialization;
using FloraPack.API.Utilities;

namespace FloraPack.API.Repositories.FutureOrders;

public class UpgradeItem
{
    public int Id { get; set; }
    public int FutureOrderId { get; set; }
    public int PrebookId { get; set; }
    public string? Date { get; set; }
    public string? Season { get; set; }
    public int SpireInventoryId { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Comments { get; set; }
    public int OrderQuantity { get; set; }
    public int? CustomerId { get; set; }
    public string? Customer { get; set; }
    public string? ShipTo { get; set; }
    public string? BoxCode { get; set; }
    public string? Upc { get; set; }
    public string? DateCode { get; set; }
    public string? Retail { get; set; }
    public string? PotCover { get; set; }
    public bool WeightsAndMeasures { get; set; }
    public string? ContainerPickDescription { get; set; }
    public string? UpgradeComments { get; set; }
    public string? ProductComingFrom { get; set; }
    public string? UpcComment { get; set; }
    public bool UpcApprovalRequired { get; set; }
    public string? Origins { get; set; }
    public string? Costs { get; set; }
    public decimal LabourHours { get; set; }
    public string? GrowerItemNotes { get; set; }
    public string? ItemGrowerItemNotes { get; set; }
    public DateTime? UpgradeConfirmed { get; set; }
    public string? UpgradeConfirmedBy { get; set; }
    public bool Priority { get; set; }
    public string? UpcPrinted { get; set; }
    public string? UpcPrintedPrev { get; set; }
    public bool IsApproximate { get; set; }
    public string? TariffCode { get; set; }

    [JsonIgnore] public int? PackQuantity => PackQuantityParser.Parse(Description);

    [JsonIgnore]
    public int RoundedLabourHours
    {
        get
        {
            var rawHours =
                LabourHours == 0 ? 0 : OrderQuantity / LabourHours;
            // Make this rounding match javascript's rounding, per https://stackoverflow.com/a/1863604
            var labourHours = Convert.ToInt32(Math.Floor(
                (rawHours > 0 && rawHours < 1 ? 1 : rawHours) + 0.5M
            ));
            return labourHours;
        }
    }
}
