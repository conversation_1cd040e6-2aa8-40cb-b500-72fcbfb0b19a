﻿namespace FloraPack.API.Repositories.Boekestyn.Entities
{
    public class HarvestingWorkOrder
    {
        public int Id { get; init; }
        public int ScheduleId { get; init; }
        public int SortOrder { get; init; }
        public string OrderId { get; init; } = string.Empty;
        public string OrderNumber { get; init; } = string.Empty;
        public string PlantSize { get; init; } = string.Empty;
        public string PlantCrop { get; init; } = string.Empty;
        public string Customer { get; init; } = string.Empty;
        public int Pots { get; init; }
        public int Cases { get; init; }
        public string Zone { get; init; } = string.Empty;
        public int CrewSize { get; init; }
        public string? OrderComments { get; init; }
        public string? HarvestingComments { get; init; }
        public int DefaultExpectedHarvestPercentage { get; init; }
        public bool FinalRound { get; init; }
        public int StickingWorkOrderId { get; init; }
        public List<HarvestingWorkOrderVariety> Varieties { get; init; } = [];
        public List<HarvestingWorkOrderLabour> Labour { get; init; } = [];
        public List<HarvestingWorkOrderLabourVarietyItem> LabourVarieties { get; init; } = [];
    }
}
