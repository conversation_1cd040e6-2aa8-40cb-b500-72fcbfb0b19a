﻿namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderUpdate
{
    public int Id { get; set; }
    public DateTime? RequiredDate { get; set; }
    public DateTime? ArrivalDate { get; set; }
    public string? SeasonName { get; set; }
    public int? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public int? ShipToId { get; set; }
    public string? ShipToName { get; set; }
    public string? BoxCode { get; set; }
    public string? CustomerPurchaseOrderNumber { get; set; }
    public decimal? FreightPerCase { get; set; }
    public decimal? FreightPerLoad { get; set; }
    public bool FreightIsActual { get; set; }
    public int? SalespersonId { get; set; }
    public string? SalespersonName { get; set; }
    public string? SpireNotes { get; set; }
    public string? GrowerItemNotes { get; set; }
    public int? ShipViaId { get; set; }
    public string? ShipViaName { get; set; }
    public bool RequiresLabels { get; set; }

    public List<FutureOrderUpdateItem> Items { get; set; } = new();
    public List<FutureOrderUpdateComment> Comments { get; set; } = new();
}