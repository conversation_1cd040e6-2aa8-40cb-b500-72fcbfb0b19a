﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="picLogo.ImageBase64String" xml:space="preserve">
    <value>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</value>
  </data>
  <metadata name="$this.ScriptEditorPositionForUndo" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
  <metadata name="$this.ScriptEditorPositionForRedo" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
</root>