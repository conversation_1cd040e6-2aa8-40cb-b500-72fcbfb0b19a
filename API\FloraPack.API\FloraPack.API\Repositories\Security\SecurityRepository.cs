﻿using Dapper;

namespace FloraPack.API.Repositories.Security;

public class SecurityRepository : RepositoryBase
{
    public SecurityRepository(IConfiguration configuration) : base(configuration) { }

    public async Task<IEnumerable<string>> UserGroups(string email) =>
        await GetConnection().QueryAsync<string>("SELECT g.name FROM groups g JOIN group_users gu ON g.id = gu.group_id WHERE gu.user_name ilike @email;", new { email });

    public async Task<IEnumerable<Group>> Groups() =>
        await GetConnection().QueryAsync<Group>("SELECT * FROM groups ORDER BY name;");

    public async Task<IEnumerable<GroupUser>> GroupUsers() =>
        await GetConnection().QueryAsync<GroupUser>("SELECT * FROM group_users;");

    public async Task UpdateGroupUsers(List<GroupUser> groupUsers)
    {
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        await connection.ExecuteAsync("DELETE FROM group_users", transaction: tx);

        await connection.ExecuteAsync("INSERT INTO group_users (group_id, user_name) VALUES (@groupId, @userName);", groupUsers, transaction: tx);

        await tx.CommitAsync();
    }
}