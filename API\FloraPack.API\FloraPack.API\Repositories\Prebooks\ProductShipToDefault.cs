﻿namespace FloraPack.API.Repositories.Prebooks;

public class ProductShipToDefault
{
    public int Id { get; set; }
    public int SpireInventoryId { get; set; }
    public int ShipToId { get; set; }
    public bool HasPotCover { get; set; }
    public string? PotCover { get; set; }
    public string? Upc { get; set; }
    public bool WeightsAndMeasures { get; set; }
    public string? Retail { get; set; }
    public decimal? UnitPrice { get; set; }
    public string? CustomerItemCode { get; set; }
}