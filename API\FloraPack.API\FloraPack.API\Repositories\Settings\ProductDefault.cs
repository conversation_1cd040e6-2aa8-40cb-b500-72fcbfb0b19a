﻿namespace FloraPack.API.Repositories.Settings;

public class ProductDefault
{
    public int Id { get; set; }
    public int SpireInventoryId { get; set; }
    public string? BoekestynPlantId { get; set; }
    public string? BoekestynCustomerAbbreviation { get; set; }
    public decimal? UpgradeLabourHours { get; set; }
    public int? QuantityPerFinishedItem { get; set; }
    public bool IsUpgrade { get; set; }
    public bool IgnoreOverrideQuantity { get; set; }

    public List<ProductDefaultBoekestynProduct> Products { get; set; } = new();
    public List<ProductDefaultBoekestynProductOverride> Overrides { get; set; } = new();
}