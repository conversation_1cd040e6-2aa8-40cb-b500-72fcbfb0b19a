﻿using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Repositories.Prebooks;
using FloraPack.API.Repositories.Settings;
using FloraPack.API.Spire;
using FloraPack.API.Utilities;

namespace FloraPack.API.Reports.FutureOrders;

public class FutureOrderReportFactory(FutureOrderRepository futureOrderRepository, PrebookRepository prebookRepository,
    PrebookEmailRepository prebookEmailRepository, SpireRepository spireRepository, SettingsRepository settingsRepository)
{
    private static string? GetPriceWarning(int? customerId, int? shipToId, FutureOrderDetailItem item, decimal? availabilityPrice, List<PriceDeviationWarning> priceDeviationWarnings)
    {
        var casePrice = item.UnitPrice.GetValueOrDefault();
        var packQuantity = PackQuantityParser.Parse(item.Description);
        var unitPrice = (packQuantity.HasValue && packQuantity.Value != 0)
            ? availabilityPrice / packQuantity.Value
            : 0;

        if(item.UseAvailabilityPricing || item.SpecialPrice.HasValue || availabilityPrice == null || casePrice == 0 || unitPrice == 0) {
            return null;
        }

        var devianceScores = priceDeviationWarnings
            .Where((w) => {
                if (w.CustomerId != null && customerId != w.CustomerId) {
                    return false;
                }

                if (w.ShipToId != null && shipToId != w.ShipToId) {
                    return false;
                }

                if (w.MaxUnitPrice != null && unitPrice > w.MaxUnitPrice) {
                    return false;
                }

                if (w.MinPackSize != null && packQuantity > w.MinPackSize) {
                    return false;
                }

                return true;
            })
            .Select((w) => {
                var score =
                    (w.CustomerId != null && customerId == w.CustomerId ? 1 : 0) +
                    (w.ShipToId != null && shipToId == w.ShipToId ? 1 : 0) +
                    (w.MaxUnitPrice != null && unitPrice < w.MaxUnitPrice ? 1 : 0) +
                    (w.MinPackSize != null && packQuantity < w.MinPackSize ? 1 : 0);

                return new { score, w };
            })
            .OrderBy(w => w.score);
        var deviance = devianceScores.FirstOrDefault()?.w;
        var difference = (1 - casePrice / availabilityPrice) * 100;

        if (deviance == null) {
            return null;
        }

        if (difference <= deviance.AllowableDeviation) {
            return null;
        }

        return $"Avail: {availabilityPrice:C2}";
    }

    // TODO: Replace with alternative report implementation
    public async Task<object?> CreateReport(int id)
    {
        // Temporarily disabled - GrapeCity ActiveReports not available
        return null;
    }

    // Original implementation commented out - GrapeCity ActiveReports not available
}