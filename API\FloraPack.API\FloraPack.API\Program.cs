using System.Net;
using System.Net.Http.Headers;
using System.Text;
using FloraPack.API.Controllers;
using FloraPack.API.Services;
using Lamar.Microsoft.DependencyInjection;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.FileProviders;
using Microsoft.Identity.Web;
using Polly;
using Polly.Extensions.Http;

var builder = WebApplication.CreateBuilder(args);

if (builder.Environment.IsProduction()) {
    builder.WebHost.UseIISIntegration();
}

builder.Host.UseLamar((_, registry) => {
   
    registry.AddHttpClient("Spire", client => {
        var apiBase = builder.Configuration["Spire:ApiBase"];
        var username = builder.Configuration["Spire:UserName"];
        var password = builder.Configuration["Spire:Password"];
        var companyName = builder.Configuration["Spire:CompanyName"];
        // https://stackoverflow.com/a/13956730
        var auth = Convert.ToBase64String(Encoding.GetEncoding("ISO-8859-1").GetBytes($"{username}:{password}"));

        client.BaseAddress = new Uri($"{apiBase}api/v2/companies/{companyName}/");
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", auth);
    });

    // https://learn.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/implement-http-call-retries-exponential-backoff-polly
    var policy = HttpPolicyExtensions.HandleTransientHttpError()
        .OrResult(msg => msg.StatusCode == HttpStatusCode.TooManyRequests)
        .WaitAndRetryAsync(new[] {
            TimeSpan.FromSeconds(1),
            TimeSpan.FromSeconds(3),
            TimeSpan.FromSeconds(5)
        });

    registry
        .AddHttpClient("Couch", client => {
            var username = builder.Configuration["CouchDB:ApiKey"];
            var password = builder.Configuration["CouchDB:Password"];
            var authorization = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));

            var urlBase = builder.Configuration["CouchDB:UrlBase"];
            var database = builder.Configuration["CouchDB:Database"];
            var uri = $"{urlBase}{database}/";
            client.BaseAddress = new Uri(uri);
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authorization);
        })
        .SetHandlerLifetime(TimeSpan.FromMinutes(5))
        .AddPolicyHandler(policy);
    
    registry.AddControllers();
    registry.AddSignalR();

    registry.AddHostedService<SalesOrderUdfService>();
});

builder.Services.AddCors(options =>
    options.AddDefaultPolicy(policy =>
        policy
            .WithOrigins(builder.Configuration["ClientUrl"]!)
            .WithMethods("GET", "POST", "PUT", "DELETE", "PATCH")
            .AllowAnyHeader()
            .AllowCredentials()
    )
);

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"))
    .EnableTokenAcquisitionToCallDownstreamApi()
    .AddMicrosoftGraph(builder.Configuration.GetSection("GraphBeta"))
    .AddInMemoryTokenCaches();

var app = builder.Build();

if (app.Environment.IsDevelopment()) {
    app.UseExceptionHandler("/error-development");
} else {
    app.UseExceptionHandler("/error");
}

app.UseHttpsRedirection();

app.UseStaticFiles(new StaticFileOptions {
    FileProvider = new PhysicalFileProvider(Environment.ExpandEnvironmentVariables(builder.Configuration["AttachmentsPath"]!)),
    RequestPath = "/Attachments"
});

app.UseCors();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers()
    .RequireAuthorization();

app.MapHub<FloraPackHub>("/hubs/florapack");

app.Run();

