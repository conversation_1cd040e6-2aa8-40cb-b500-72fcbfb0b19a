﻿namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderDetail
{
    public int Id { get; set; }
    public DateTime? RequiredDate { get; set; }
    public DateTime? ArrivalDate { get; set; }
    public string? SeasonName { get; set; }
    public int? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public int? ShipToId { get; set; }
    public string? ShipToName { get; set; }
    public string? BoxCode { get; set; }
    public string? CustomerPurchaseOrderNumber { get; set; }
    public decimal? FreightPerCase { get; set; }
    public decimal? FreightPerLoad { get; set; }
    public bool FreightIsActual { get; set; }
    public int? SalespersonId { get; set; }
    public string? SalespersonName { get; set; }
    public string? SpireNotes { get; set; }
    public string? GrowerItemNotes { get; set; }
    public int? ShipViaId { get; set; }
    public string? ShipViaName { get; set; }
    public bool RequiresLabels { get; set; }
    public int? SpireSalesOrderId { get; set; }
    public bool IsParentOrder { get; set; }
    public int? ParentOrderId { get; set; }
    public string? SpireSalesOrderNumber { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime Modified { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime? SentToSpire { get; set; }
    public string? SentToSpireBy { get; set; }

    public List<FutureOrderDetailItem> Items { get; set; } = new();
    public List<FutureOrderDetailComment> Comments { get; set; } = new();
}