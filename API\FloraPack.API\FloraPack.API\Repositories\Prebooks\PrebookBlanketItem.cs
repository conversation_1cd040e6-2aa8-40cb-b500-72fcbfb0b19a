﻿namespace FloraPack.API.Repositories.Prebooks;

public class PrebookBlanketItem
{
    public int Id { get; set; }
    public int SpireInventoryId { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int VendorId { get; set; }
    public string? VendorName { get; set; }
    public int? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public int? ShipToId { get; set; }
    public string? ShipToName { get; set; }
    public string? RequiredDate { get; set; }
    public string? BlanketStartDate { get; set; }
    public string? BlanketWeekId { get; set; }
    public int BlanketQuantity { get; set; }
    public long BookedQuantity { get; set; }
}