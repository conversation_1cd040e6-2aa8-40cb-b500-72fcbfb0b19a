﻿using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

public class ErrorController : FloraPackControllerBase
{
    public ErrorController(IConfiguration configuration) : base(configuration) { }

    [Route("/error-development")]
    public IActionResult ErrorDevelopment()
    {
        var exceptionHandlerFeature = HttpContext.Features.Get<IExceptionHandlerFeature>()!;

        return Problem(
            title: exceptionHandlerFeature.Error.Message,
            detail: exceptionHandlerFeature.Error.StackTrace
        );
    }

    [Route("/error")]
    public IActionResult Error()
    {
        var exceptionHandlerFeature = HttpContext.Features.Get<IExceptionHandlerFeature>()!;

        return Problem(
            title: exceptionHandlerFeature.Error.Message
        );
    }
}