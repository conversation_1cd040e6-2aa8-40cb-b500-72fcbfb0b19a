﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Repositories.Boekestyn.Couch;

public class ViewResponse<TDoc, TValue, TKey>
{
    [JsonPropertyName("total_rows")]
    public int TotalRows { get; set; }

    public int Offset { get; set; }

    public List<ViewRow<TDoc, TValue, TKey>> Rows { get; set; } = [];
}

public class BulkGetResponse<T>
{
    [JsonPropertyName("rows")]
    public List<BulkGetRow<T>> Rows { get; set; } = [];
}

public class BulkGetRow<T>
{
    public T Doc { get; set; }
}
