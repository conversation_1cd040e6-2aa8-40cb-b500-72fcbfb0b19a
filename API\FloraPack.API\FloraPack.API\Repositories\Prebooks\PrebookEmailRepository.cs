﻿using Dapper;
using FloraPack.API.Utilities;

namespace FloraPack.API.Repositories.Prebooks;

public class PrebookEmailRepository(IConfiguration configuration) : RepositoryBase(configuration)
{
    public async Task<PrebookEmail?> Create(PrebookDetail detail, string to, string? cc, string? bcc, string subject, string body, string from)
    {
        var pending = await FromPrebook(detail);
        if (pending == null)
        {
            return null;
        }

        var now = DateTime.Now;

        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var id = await connection.ExecuteScalarAsync<int>(
            "INSERT INTO prebook_emails (" +
            "prebook_id, vendor_name, prebook_date, previous_prebook_date, required_date, comments, created, created_by, subject, body, \"to\", cc, bcc, \"from\"" +
            ") VALUES (" +
            "@prebookId, @vendorName, @prebookDate, @previousPrebookDate, @requiredDate, @comments, @now, @from, @subject, @body, @to, @cc, @bcc, @from" +
            ") RETURNING id;",
            new { pending.PrebookId, pending.VendorName, pending.PrebookDate, pending.PreviousPrebookDate, pending.RequiredDate, pending.Comments, now, subject, body, to, cc, bcc, from }, tx);

        await connection.ExecuteAsync(
            "INSERT INTO prebook_email_items (" +
            "prebook_email_id, prebook_item_id, spire_part_number, description, order_quantity, previous_order_quantity, pack_quantity, box_code, date_code, pot_cover, upc, weights_and_measures, retail, comments, upgrade_sheet" +
            ") VALUES (" +
            "@id, @prebookItemId, @spirePartNumber, @description, @orderQuantity, @previousOrderQuantity, @packQuantity, @boxCode, @dateCode, @potCover, @upc, @weightsAndMeasures, @retail, @comments, @upgradeSheet" +
            ");",
            pending.Items.Select(i => new
            {
                id,
                i.PrebookItemId,
                i.SpirePartNumber,
                i.Description,
                i.OrderQuantity,
                i.PreviousOrderQuantity,
                i.PackQuantity,
                i.BoxCode,
                i.DateCode,
                i.PotCover,
                i.Upc,
                i.WeightsAndMeasures,
                i.Retail,
                i.Comments,
                i.UpgradeSheet
            }), tx);

        await tx.CommitAsync();

        await using var multi = await connection.QueryMultipleAsync(@"
                SELECT * FROM prebook_emails WHERE id = @id;
                SELECT * FROM prebook_email_items WHERE prebook_email_id = @id",
            new { id });
        var email = await multi.ReadFirstOrDefaultAsync<PrebookEmail>();
        if (email != null)
        {
            email.Items = (await multi.ReadAsync<PrebookEmailItem>()).ToList();
        }

        return email;
    }

    public async Task<IEnumerable<PrebookEmail>> GetEmailsForPrebook(int prebookId)
    {
        await using var connection = GetConnection();
        var emails = (await connection.QueryAsync<PrebookEmail>("SELECT * FROM prebook_emails WHERE prebook_id = @prebookId;", new { prebookId })).ToList();
        if (emails.Any())
        {
            var ids = string.Join(", ", emails.Select(e => e.Id));

            var items = (await connection.QueryAsync<PrebookEmailItem>($"SELECT * FROM prebook_email_items WHERE prebook_email_id IN ({ids})")).ToLookup(i => i.PrebookEmailId);
            foreach (var email in emails.Where(e => items.Contains(e.Id)))
            {
                email.Items = items[email.Id].ToList();
            }
        }

        return emails;
    }

    public async Task<IEnumerable<PrebookEmail>> GetEmailsForFutureOrder(int futureOrderId)
    {
        await using var connection = GetConnection();
        var emails = (await connection.QueryAsync<PrebookEmail>("SELECT e.* FROM prebook_emails e JOIN prebooks p ON e.prebook_id = p.id WHERE p.future_order_id = @futureOrderId;", new { futureOrderId })).ToList();
        var parentEmails = await connection.QueryAsync<PrebookEmail>(@"
select
    e.*
from
    future_order_items fi
    join prebook_items pi on fi.id = pi.future_order_item_id
    join prebook_emails e on pi.prebook_id = e.prebook_id
where
    fi.future_order_id = @futureOrderId;", new { futureOrderId });

        foreach (var email in parentEmails) {
            if (!emails.Any(e => e.Id == email.Id)) {
                emails.Add(email);
            }
        }

        if (emails.Any())
        {
            var ids = string.Join(", ", emails.Select(e => e.Id));

            var items = (await connection.QueryAsync<PrebookEmailItem>($"SELECT * FROM prebook_email_items WHERE prebook_email_id IN ({ids})")).ToLookup(i => i.PrebookEmailId);
            foreach (var email in emails.Where(e => items.Contains(e.Id)))
            {
                email.Items = items[email.Id].ToList();
            }
        }

        return emails;
    }

    public async Task<PendingPrebookEmail?> FromPrebook(PrebookDetail detail)
    {
        await using var connection = GetConnection();

        var items = new List<PendingPrebookEmailItem>();
        foreach (var i in detail.Items) {
            var packQuantity = PackQuantityParser.Parse(i.Description);

            var boxCode = detail.BoxCode;
            if (i.FutureOrderItemId.HasValue) {
                var itemBoxCode = await connection.QueryFirstOrDefaultAsync<FutureOrderBoxCode>(
                    "SELECT o.id as future_order_id, o.box_code FROM future_orders o JOIN future_order_items i on o.id = i.future_order_id WHERE i.id = @id;",
                    new { Id = i.FutureOrderItemId.Value });
                if (itemBoxCode != null && itemBoxCode.FutureOrderId != detail.Id) {
                    boxCode = itemBoxCode.BoxCode;
                }
            }

            var item = new PendingPrebookEmailItem {
                PrebookItemId = i.Id,
                SpirePartNumber = i.SpirePartNumber,
                Description = i.Description,
                OrderQuantity = i.OrderQuantity,
                PackQuantity = packQuantity,
                BoxCode = boxCode,
                DateCode = i.DateCode,
                PotCover = i.PotCover,
                Upc = i.Upc,
                WeightsAndMeasures = i.WeightsAndMeasures,
                Retail = i.Retail,
                Comments = i.Comments,
                UpgradeSheet = i.UpgradeSheet
            };
            items.Add(item);
        }

        var prebookEmail = new PendingPrebookEmail
        {
            PrebookId = detail.Id,
            VendorName = detail.VendorName ?? string.Empty,
            PrebookDate = DateTime.Today,
            RequiredDate = detail.RequiredDate,
            SeasonName = detail.SeasonName,
            Comments = detail.Comments,
            Items = items
        };

        var previousEmail = await connection.QueryFirstOrDefaultAsync<PrebookEmail>("SELECT * FROM prebook_emails WHERE prebook_id = @id ORDER BY prebook_date DESC LIMIT 1;", new { detail.Id });
        if (previousEmail != null)
        {
            prebookEmail.PreviousPrebookDate = previousEmail.PrebookDate;

            var previousEmailItems = (await connection
                .QueryAsync<PrebookEmailItem>("SELECT * FROM prebook_email_items WHERE prebook_email_id = @id;", new { previousEmail.Id }))
                // if the prebook item was deleted, it will have a null PrebookItemId in the PrebookEmailItem table
                .Where(i => i.PrebookItemId.HasValue)
                .ToDictionary(i => i.PrebookItemId.GetValueOrDefault());

            foreach (var item in prebookEmail.Items.Where(i => i.PrebookItemId.HasValue && previousEmailItems.ContainsKey(i.PrebookItemId.Value)))
            {
                var previousItem = previousEmailItems[item.PrebookItemId.GetValueOrDefault()];
                item.PreviousOrderQuantity = previousItem.OrderQuantity;
            }
        }

        return prebookEmail;
    }

    public record FutureOrderBoxCode(int FutureOrderId, string? BoxCode);
}