﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Spire;

public class InventoryComment
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("comments")]
    public string Comments { get; set; } = string.Empty;
}