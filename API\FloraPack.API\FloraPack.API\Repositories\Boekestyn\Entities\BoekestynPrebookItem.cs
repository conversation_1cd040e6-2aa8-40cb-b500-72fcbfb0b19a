﻿namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class BoekestynPrebookItem
{
    public int Id { get; set; }
    public int PrebookId { get; set; }
    public int? FutureOrderId { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string? ShipToName { get; set; }
    public string? BoxCode { get; set; }
    public int Week { get; set; }
    public int Year { get; set; }
    public int OrderQuantity { get; set; }
    public string BoekestynPlantId { get; set; } = string.Empty;
    public string BoekestynCustomerAbbreviation { get; set; } = string.Empty;
    public int? QuantityPerFinishedItem { get; set; }
    public bool MultipleProducts { get; set; }
    public int PackQuantity { get; set; }
    public bool IgnoreOverrideQuantity { get; set; }

    public string CouchCustomer => CustomerName + (string.IsNullOrEmpty(ShipToName) ? string.Empty : $": {ShipToName}");
}