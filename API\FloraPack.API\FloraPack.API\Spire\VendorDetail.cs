﻿using System.Text.Json.Serialization;
using FloraPack.API.Repositories.Boekestyn;

namespace FloraPack.API.Spire;

public class VendorDetail
{
    public int Id { get; set; }
    public string VendorNo { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;

    public List<string> GrowerPoEmailAddresses()
    {
        var addresses = new List<string>();
        if (!string.IsNullOrWhiteSpace(Address?.Email)) {
            addresses.Add(Address.Email);
        }

        if (Address?.Contacts != null) {

            if (Id == Constants.BoekestynVendorId) {
                var contact = Address.Contacts.FirstOrDefault(c => c.Name == "PREBOOKS");
                if (!string.IsNullOrWhiteSpace(contact?.Email)) {
                    addresses.Add(contact.Email);
                }
            } else if (Id == Constants.UpgradesVendorId) {
                var contacts = Address.Contacts.Where(a => !string.IsNullOrWhiteSpace(a.Email) && a.ContactType?.Name?.StartsWith("BOEK PREBOOKS") == true).ToList();
                if (!contacts.Any()) {
                    contacts = Address.Contacts.Where(a => !string.IsNullOrWhiteSpace(a.Email)).ToList();
                }

                addresses.AddRange(contacts.Select(a => a.Email.ToLower()).Distinct());
            } else {
                var contacts = Address.Contacts.Where(a => !string.IsNullOrWhiteSpace(a.Email) && a.ContactType?.Name?.StartsWith("Grower PO") == true).ToList();
                if (!contacts.Any()) {
                    contacts = Address.Contacts.Where(a => !string.IsNullOrWhiteSpace(a.Email)).ToList();
                }

                addresses.AddRange(contacts.Select(a => a.Email.ToLower()).Distinct());
            }
        }

        return addresses;
    }

    public VendorDetailAddress? Address { get; set; }
}

public class VendorDetailAddress
{
    public string? Email { get; set; }

    public List<VendorContact>? Contacts { get; set; }
}

public class VendorContact
{
    public int? Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    [JsonPropertyName("contact_type")]
    public VendorContactType? ContactType { get; set; }
}

public class VendorContactType
{
    public string? Name { get; set; }
}