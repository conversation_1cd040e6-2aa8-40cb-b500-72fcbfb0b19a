﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Spire;

public class CustomerAddress : Address
{
    public string LinkNo { get; set; } = string.Empty;
}

public class CustomerShippingAddress : Address
{
    public ShipToUdf? Udf { get; set; }
    public string? BoxCode => Udf?.ShipToBoxCode;
    public string? Labels => Udf?.Labels;
    public string? CustomerInfo => Udf?.CustomerInfo;

    // these are set separately
    public string? PriceLevel { get; set; }
    public decimal? DefaultFreightPerCase { get; set; }
};

public class ShipToUdf
{
    [JsonPropertyName("labels")]
    public string? Labels { get; set; }
    [JsonPropertyName("check_skids")]
    public string? CheckSkids { get; set; }
    [JsonPropertyName("ship_to_box_code")]
    public string? ShipToBoxCode { get; set; }
    [JsonPropertyName("customer_info")]
    public string? CustomerInfo { get; set; }
    [JsonPropertyName("special_label_details")]
    public string? SpecialLabelDetails { get; set; }
}