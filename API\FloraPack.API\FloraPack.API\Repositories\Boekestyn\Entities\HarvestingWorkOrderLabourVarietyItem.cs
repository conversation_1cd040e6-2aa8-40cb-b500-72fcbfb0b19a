﻿namespace FloraPack.API.Repositories.Boekestyn.Entities
{
    public class HarvestingWorkOrderLabourVarietyItem
    {
        public int Id { get; init; }
        public int WorkOrderId { get; init; }
        public string VarietyName { get; init; } = string.Empty;
        public int Harvested { get; init; }
        public int ThrownOut { get; init; }
        public bool NumberTwo { get; init; }
        public string? Comments { get; init; }
    }
}
