﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="picLogo.ImageBase64String" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="txtRequiredDate.Html" xml:space="preserve">
    <value>&lt;html&gt;&lt;body&gt;richTextBox1&lt;/body&gt;&lt;/html&gt;</value>
  </data>
  <data name="txtComments.Html" xml:space="preserve">
    <value>&lt;html&gt;&lt;body&gt;richTextBox1&lt;/body&gt;&lt;/html&gt;</value>
  </data>
  <metadata name="$this.ScriptEditorPositionForUndo" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
  <metadata name="$this.ScriptEditorPositionForRedo" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
</root>