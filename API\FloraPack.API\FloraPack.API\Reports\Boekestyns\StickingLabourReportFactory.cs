﻿using ClosedXML.Excel;
using FloraPack.API.Repositories.Boekestyn.Entities;

namespace FloraPack.API.Reports.Boekestyns;

public static class StickingLabourReportFactory
{
    public static MemoryStream CreateReport(DateTime start, DateTime end, IEnumerable<StickingLabourReportItem> items)
    {
        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 2;
        var col = 1;

        sheet.Cell(row, col).SetValue("Schedule Date").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Size").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Crop").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Customer").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Cuttings").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Pots").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Crew Size").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Est. Hours").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Act. Hours").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Diff").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Est. Man Hours").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Act. Man Hours").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Diff").Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Comments").Style.Font.Bold = true;

        sheet.Range(sheet.Cell(1, 1), sheet.Cell(2, col)).Style.Font.Bold = true;

        row++;

        foreach (var item in items) {
            col = 1;
            sheet.Cell(row, col).SetValue(item.ScheduleDate).Style.DateFormat.Format = "yyyy-mm-dd";
            col++;
            sheet.Cell(row, col).SetValue(item.PlantSize);
            col++;
            sheet.Cell(row, col).SetValue(item.PlantCrop);
            col++;
            sheet.Cell(row, col).SetValue(item.Customer);
            col++;
            sheet.Cell(row, col).SetValue(item.Cuttings).Style.NumberFormat.Format = "#,##0";
            col++;
            sheet.Cell(row, col).SetValue(item.Pots).Style.NumberFormat.Format = "#,##0";
            col++;
            sheet.Cell(row, col).SetValue(item.CrewSize);
            col++;
            sheet.Cell(row, col).SetValue(item.EstimatedHours).Style.NumberFormat.Format = "#,##0.00";
            col++;
            if (item.ActualHours > 0) {
                sheet.Cell(row, col).SetValue(item.ActualHours).Style.NumberFormat.Format = "#,##0.00";
            }
            col++;

            if (item.ActualHours > 0) {
                sheet.Cell(row, col).SetFormulaR1C1("=RC[-1]-RC[-2]").Style.NumberFormat.Format = "#,##0.00";
            }

            col++;
            sheet.Cell(row, col).SetFormulaR1C1("=RC[-3]*RC[-4]").Style.NumberFormat.Format = "#,##0.00";

            col++;
            if (item.ActualHours > 0) {
                sheet.Cell(row, col).SetFormulaR1C1("=RC[-3]*RC[-5]").Style.NumberFormat.Format = "#,##0.00";
            }
            col++;

            if (item.ActualHours > 0) {
                sheet.Cell(row, col).SetFormulaR1C1("=RC[-1]-RC[-2]").Style.NumberFormat.Format = "#,##0.00";
            }

            col++;
            sheet.Cell(row, col).SetValue(item.Comments).Style.Alignment.WrapText = true;

            row++;
        }

        sheet.Columns().AdjustToContents();

        sheet.Cell(1, 1).SetValue($"Sticking Labour: {start:yyyy-MM-dd} - {end:yyyy-MM-dd}").Style.Font.Bold = true;

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }
}