﻿namespace FloraPack.API.Repositories.Prebooks;

public interface IPendingPrebookEmail
{
    public int PrebookId { get; set; }
    public string VendorName { get; set; }
    public DateTime PrebookDate { get; set; }
    public DateTime? PreviousPrebookDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? Comments { get; set; }
}

public class PendingPrebookEmail : IPendingPrebookEmail
{
    public int PrebookId { get; set; }
    public string VendorName { get; set; } = string.Empty;
    public DateTime PrebookDate { get; set; }
    public DateTime? PreviousPrebookDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? SeasonName { get; set; }
    public string? Comments { get; set; }

    public List<PendingPrebookEmailItem> Items { get; set; } = new();
}

public class PendingPrebookEmailItem
{
    public int? PrebookItemId { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int OrderQuantity { get; set; }
    public int? PreviousOrderQuantity { get; set; }
    public int? PackQuantity { get; set; }
    public string? BoxCode { get; set; }
    public string? DateCode { get; set; }
    public string? PotCover { get; set; }
    public string? Upc { get; set; }
    public bool WeightsAndMeasures { get; set; }
    public string? Retail { get; set; }
    public string? Comments { get; set; }
    public bool UpgradeSheet { get; set; }
}