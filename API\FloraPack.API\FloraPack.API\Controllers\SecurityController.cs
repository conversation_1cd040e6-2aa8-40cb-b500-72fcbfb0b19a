﻿using FloraPack.API.Repositories.Security;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph;
using Group = FloraPack.API.Repositories.Security.Group;

namespace FloraPack.API.Controllers;

[Route("API/Security")]
public class SecurityController : FloraPackControllerBase
{
    private readonly SecurityRepository _securityRepository;

    public SecurityController(IConfiguration configuration, SecurityRepository securityRepository) : base(configuration)
    {
        _securityRepository = securityRepository;
    }

    [HttpGet("")]
    public async Task<IActionResult> Index()
    {
        var groups = await _securityRepository.Groups();
        var groupUsers = await _securityRepository.GroupUsers();
        var users = await GetUsers();

        var response = new IndexResponse(groups, groupUsers, users);

        return Ok(response);
    }

    [HttpGet("Me")]
    public async Task<IActionResult> Me()
    {
        var email = await UserEmail();

        if (string.IsNullOrWhiteSpace(email)) {
            return Unauthorized();
        }

        var groups = await _securityRepository.UserGroups(email);

        var response = new MeResponse(groups);

        return Ok(response);
    }

    [HttpPost("Group-Users")]
    public async Task<IActionResult> UpdateGroupUsers(UpdateGroupUserModel model)
    {
        await _securityRepository.UpdateGroupUsers(model.GroupUsers);
        return Ok();
    }

    private async Task<List<string>> GetUsers()
    {
        var emails = new List<string>();

        var client = GetGraphServiceClient();
        var userRequest = await client.Users.Request().Select(u => u.Mail).GetAsync();
        var pageIterator = PageIterator<User>.CreatePageIterator(client, userRequest, u => {
            if (!string.IsNullOrWhiteSpace(u.Mail)) {
                emails.Add(u.Mail);
            }
            return true;
        });

        await pageIterator.IterateAsync();

        return emails.OrderBy(e => e).ToList();
    }

    private record IndexResponse(IEnumerable<Group> Groups, IEnumerable<GroupUser> GroupUsers, IEnumerable<string> Users);

    private record MeResponse(IEnumerable<string> Groups);

    public class UpdateGroupUserModel
    {
        public List<GroupUser> GroupUsers { get; set; } = new();
    }
}