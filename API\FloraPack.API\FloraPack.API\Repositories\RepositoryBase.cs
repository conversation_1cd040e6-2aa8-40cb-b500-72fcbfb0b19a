﻿using Npgsql;

namespace FloraPack.API.Repositories;

public abstract class RepositoryBase
{
    private readonly string _connectionString;
    private readonly string _spireConnectionString;

    protected RepositoryBase(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection")!;
        _spireConnectionString = configuration.GetConnectionString("SpireConnection")!;
        Dapper.DefaultTypeMap.MatchNamesWithUnderscores = true;
    }

    protected NpgsqlConnection GetConnection() => new (_connectionString);

    protected NpgsqlConnection GetSpireConnection() => new(_spireConnectionString);
}