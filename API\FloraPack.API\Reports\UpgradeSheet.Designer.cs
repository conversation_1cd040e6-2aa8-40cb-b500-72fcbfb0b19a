﻿namespace Reports
{
    /// <summary>
    /// Summary description for UpgradeSheet.
    /// </summary>
    partial class UpgradeSheet
    {
        private GrapeCity.ActiveReports.SectionReportModel.PageHeader pageHeader;
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;
        private GrapeCity.ActiveReports.SectionReportModel.PageFooter pageFooter;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing) {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(UpgradeSheet));
            this.pageHeader = new GrapeCity.ActiveReports.SectionReportModel.PageHeader();
            this.picLogo = new GrapeCity.ActiveReports.SectionReportModel.Picture();
            this.lblDate = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtDate = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.lblRequiredDate = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtRequiredDate = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.lblAddress = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblQty = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblPackQuantity = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblDescription = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblProductComingFrom = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblUPC = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblUPCApprovalRequired = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblContainerPickDescription = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblOrigins = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblCosts = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblBoxCode = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblHours = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.txtQty = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtDescription = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtProductComingFrom = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtUPC = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtUPCApprovalRequired = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtContainerPickDescription = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtOrigins = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtCosts = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtBoxCode = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtLabourHours = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPackQuantity = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.pageFooter = new GrapeCity.ActiveReports.SectionReportModel.PageFooter();
            this.infoRunTime = new GrapeCity.ActiveReports.SectionReportModel.ReportInfo();
            this.infoPageNumber = new GrapeCity.ActiveReports.SectionReportModel.ReportInfo();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblRequiredDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRequiredDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblAddress)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblQty)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblPackQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblProductComingFrom)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblUPC)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblUPCApprovalRequired)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblContainerPickDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblOrigins)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblCosts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblBoxCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblHours)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtQty)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProductComingFrom)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUPC)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUPCApprovalRequired)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtContainerPickDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrigins)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCosts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBoxCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLabourHours)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPackQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.infoRunTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.infoPageNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // pageHeader
            // 
            this.pageHeader.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.picLogo,
            this.lblDate,
            this.txtDate,
            this.lblRequiredDate,
            this.txtRequiredDate,
            this.lblAddress,
            this.lblQty,
            this.lblPackQuantity,
            this.lblDescription,
            this.lblProductComingFrom,
            this.lblUPC,
            this.lblUPCApprovalRequired,
            this.lblContainerPickDescription,
            this.lblOrigins,
            this.lblCosts,
            this.lblBoxCode,
            this.lblHours});
            this.pageHeader.Height = 2.425F;
            this.pageHeader.Name = "pageHeader";
            // 
            // picLogo
            // 
            this.picLogo.Height = 0.75F;
            this.picLogo.ImageBase64String = resources.GetString("picLogo.ImageBase64String");
            this.picLogo.Left = 0F;
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeMode = GrapeCity.ActiveReports.SectionReportModel.SizeModes.Zoom;
            this.picLogo.Top = 0F;
            this.picLogo.Width = 10F;
            // 
            // lblDate
            // 
            this.lblDate.Height = 0.2F;
            this.lblDate.HyperLink = null;
            this.lblDate.Left = 0F;
            this.lblDate.Name = "lblDate";
            this.lblDate.Style = "font-weight: bold; text-align: right";
            this.lblDate.Text = "DATE:";
            this.lblDate.Top = 1.5F;
            this.lblDate.Width = 0.5F;
            // 
            // txtDate
            // 
            this.txtDate.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDate.Height = 0.2F;
            this.txtDate.Left = 0.5000005F;
            this.txtDate.Name = "txtDate";
            this.txtDate.OutputFormat = "d MMMM yyyy";
            this.txtDate.Style = "font-weight: bold; text-align: center";
            this.txtDate.Text = null;
            this.txtDate.Top = 1.5F;
            this.txtDate.Width = 1.5F;
            // 
            // lblRequiredDate
            // 
            this.lblRequiredDate.Height = 0.2F;
            this.lblRequiredDate.HyperLink = null;
            this.lblRequiredDate.Left = 7F;
            this.lblRequiredDate.Name = "lblRequiredDate";
            this.lblRequiredDate.Style = "font-weight: bold; text-align: right";
            this.lblRequiredDate.Text = "UPGRADE FOR:";
            this.lblRequiredDate.Top = 1.5F;
            this.lblRequiredDate.Width = 1.5F;
            // 
            // txtRequiredDate
            // 
            this.txtRequiredDate.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtRequiredDate.Height = 0.2F;
            this.txtRequiredDate.Left = 8.5F;
            this.txtRequiredDate.Name = "txtRequiredDate";
            this.txtRequiredDate.OutputFormat = "d MMMM yyyy";
            this.txtRequiredDate.Style = "font-weight: bold; text-align: center";
            this.txtRequiredDate.Text = null;
            this.txtRequiredDate.Top = 1.5F;
            this.txtRequiredDate.Width = 1.5F;
            // 
            // lblAddress
            // 
            this.lblAddress.Height = 0.5369999F;
            this.lblAddress.HyperLink = null;
            this.lblAddress.Left = 0F;
            this.lblAddress.Name = "lblAddress";
            this.lblAddress.Style = "text-align: center; vertical-align: middle";
            this.lblAddress.Text = "1539 Fourth Avenue, St. Catharines, ON\r\nTelephone: ************ x247\r\nA better wa" +
    "y to floral";
            this.lblAddress.Top = 0.7500001F;
            this.lblAddress.Width = 10F;
            // 
            // lblQty
            // 
            this.lblQty.Height = 0.4F;
            this.lblQty.HyperLink = null;
            this.lblQty.Left = 0F;
            this.lblQty.Name = "lblQty";
            this.lblQty.Style = "text-align: center";
            this.lblQty.Text = "Qty";
            this.lblQty.Top = 2F;
            this.lblQty.Width = 0.5F;
            // 
            // lblPackQuantity
            // 
            this.lblPackQuantity.Height = 0.4F;
            this.lblPackQuantity.HyperLink = null;
            this.lblPackQuantity.Left = 0.5F;
            this.lblPackQuantity.Name = "lblPackQuantity";
            this.lblPackQuantity.Style = "text-align: center";
            this.lblPackQuantity.Text = "Pk";
            this.lblPackQuantity.Top = 2F;
            this.lblPackQuantity.Width = 0.5F;
            // 
            // lblDescription
            // 
            this.lblDescription.Height = 0.4F;
            this.lblDescription.HyperLink = null;
            this.lblDescription.Left = 1F;
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Style = "text-align: center";
            this.lblDescription.Text = "Description";
            this.lblDescription.Top = 2F;
            this.lblDescription.Width = 1.5F;
            // 
            // lblProductComingFrom
            // 
            this.lblProductComingFrom.Height = 0.4F;
            this.lblProductComingFrom.HyperLink = null;
            this.lblProductComingFrom.Left = 2.5F;
            this.lblProductComingFrom.Name = "lblProductComingFrom";
            this.lblProductComingFrom.Style = "text-align: center";
            this.lblProductComingFrom.Text = "Product Coming From";
            this.lblProductComingFrom.Top = 2F;
            this.lblProductComingFrom.Width = 1F;
            // 
            // lblUPC
            // 
            this.lblUPC.Height = 0.4F;
            this.lblUPC.HyperLink = null;
            this.lblUPC.Left = 3.5F;
            this.lblUPC.Name = "lblUPC";
            this.lblUPC.Style = "text-align: center";
            this.lblUPC.Text = "UPC";
            this.lblUPC.Top = 2F;
            this.lblUPC.Width = 1.5F;
            // 
            // lblUPCApprovalRequired
            // 
            this.lblUPCApprovalRequired.Height = 0.4F;
            this.lblUPCApprovalRequired.HyperLink = null;
            this.lblUPCApprovalRequired.Left = 5F;
            this.lblUPCApprovalRequired.Name = "lblUPCApprovalRequired";
            this.lblUPCApprovalRequired.Style = "font-size: 9pt; text-align: center";
            this.lblUPCApprovalRequired.Text = "UPC Approval req\'d";
            this.lblUPCApprovalRequired.Top = 2F;
            this.lblUPCApprovalRequired.Width = 0.75F;
            // 
            // lblContainerPickDescription
            // 
            this.lblContainerPickDescription.Height = 0.4F;
            this.lblContainerPickDescription.HyperLink = null;
            this.lblContainerPickDescription.Left = 5.75F;
            this.lblContainerPickDescription.Name = "lblContainerPickDescription";
            this.lblContainerPickDescription.Style = "text-align: center";
            this.lblContainerPickDescription.Text = "Container / Pick Description";
            this.lblContainerPickDescription.Top = 2F;
            this.lblContainerPickDescription.Width = 1.5F;
            // 
            // lblOrigins
            // 
            this.lblOrigins.Height = 0.4F;
            this.lblOrigins.HyperLink = null;
            this.lblOrigins.Left = 7.25F;
            this.lblOrigins.Name = "lblOrigins";
            this.lblOrigins.Style = "text-align: center";
            this.lblOrigins.Text = "Origins";
            this.lblOrigins.Top = 2F;
            this.lblOrigins.Width = 0.75F;
            // 
            // lblCosts
            // 
            this.lblCosts.Height = 0.4F;
            this.lblCosts.HyperLink = null;
            this.lblCosts.Left = 8F;
            this.lblCosts.Name = "lblCosts";
            this.lblCosts.Style = "text-align: center";
            this.lblCosts.Text = "Costs";
            this.lblCosts.Top = 2F;
            this.lblCosts.Width = 0.75F;
            // 
            // lblBoxCode
            // 
            this.lblBoxCode.Height = 0.4F;
            this.lblBoxCode.HyperLink = null;
            this.lblBoxCode.Left = 8.75F;
            this.lblBoxCode.Name = "lblBoxCode";
            this.lblBoxCode.Style = "text-align: center";
            this.lblBoxCode.Text = "Box Code";
            this.lblBoxCode.Top = 2F;
            this.lblBoxCode.Width = 0.75F;
            // 
            // lblHours
            // 
            this.lblHours.Height = 0.4F;
            this.lblHours.HyperLink = null;
            this.lblHours.Left = 9.5F;
            this.lblHours.Name = "lblHours";
            this.lblHours.Style = "text-align: center";
            this.lblHours.Text = "Hrs";
            this.lblHours.Top = 2F;
            this.lblHours.Width = 0.5F;
            // 
            // detail
            // 
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.txtQty,
            this.txtDescription,
            this.txtProductComingFrom,
            this.txtUPC,
            this.txtUPCApprovalRequired,
            this.txtContainerPickDescription,
            this.txtOrigins,
            this.txtCosts,
            this.txtBoxCode,
            this.txtLabourHours,
            this.txtPackQuantity});
            this.detail.Height = 0.2F;
            this.detail.KeepTogether = true;
            this.detail.Name = "detail";
            this.detail.Format += new System.EventHandler(this.OnDetailFormat);
            this.detail.BeforePrint += new System.EventHandler(this.OnDetailBeforePrint);
            // 
            // txtQty
            // 
            this.txtQty.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtQty.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtQty.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtQty.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtQty.DataField = "OrderQuantity";
            this.txtQty.Height = 0.2F;
            this.txtQty.Left = 0F;
            this.txtQty.Name = "txtQty";
            this.txtQty.OutputFormat = "#,##0";
            this.txtQty.Style = "font-size: 8pt; text-align: center";
            this.txtQty.Text = null;
            this.txtQty.Top = 0F;
            this.txtQty.Width = 0.5F;
            // 
            // txtDescription
            // 
            this.txtDescription.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDescription.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDescription.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDescription.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDescription.DataField = "Description";
            this.txtDescription.Height = 0.2F;
            this.txtDescription.Left = 1F;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Padding = new GrapeCity.ActiveReports.PaddingEx(5, 0, 0, 0);
            this.txtDescription.Style = "font-size: 8pt";
            this.txtDescription.Text = null;
            this.txtDescription.Top = 0F;
            this.txtDescription.Width = 1.5F;
            // 
            // txtProductComingFrom
            // 
            this.txtProductComingFrom.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtProductComingFrom.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtProductComingFrom.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtProductComingFrom.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtProductComingFrom.DataField = "ProductComingFrom";
            this.txtProductComingFrom.Height = 0.2F;
            this.txtProductComingFrom.Left = 2.5F;
            this.txtProductComingFrom.Name = "txtProductComingFrom";
            this.txtProductComingFrom.Style = "font-size: 8pt; text-align: center";
            this.txtProductComingFrom.Text = null;
            this.txtProductComingFrom.Top = 0F;
            this.txtProductComingFrom.Width = 1F;
            // 
            // txtUPC
            // 
            this.txtUPC.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPC.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPC.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPC.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPC.DataField = "UPCFormatted";
            this.txtUPC.Height = 0.2F;
            this.txtUPC.Left = 3.5F;
            this.txtUPC.Name = "txtUPC";
            this.txtUPC.Style = "font-size: 8pt; text-align: center";
            this.txtUPC.Text = null;
            this.txtUPC.Top = 0F;
            this.txtUPC.Width = 1.5F;
            // 
            // txtUPCApprovalRequired
            // 
            this.txtUPCApprovalRequired.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPCApprovalRequired.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPCApprovalRequired.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPCApprovalRequired.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPCApprovalRequired.DataField = "=UPCApprovalRequired ? \"\" : \"\"";
            this.txtUPCApprovalRequired.Height = 0.2F;
            this.txtUPCApprovalRequired.Left = 5F;
            this.txtUPCApprovalRequired.Name = "txtUPCApprovalRequired";
            this.txtUPCApprovalRequired.Style = "text-align: center; vertical-align: middle";
            this.txtUPCApprovalRequired.Text = null;
            this.txtUPCApprovalRequired.Top = 0F;
            this.txtUPCApprovalRequired.Width = 0.75F;
            // 
            // txtContainerPickDescription
            // 
            this.txtContainerPickDescription.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtContainerPickDescription.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtContainerPickDescription.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtContainerPickDescription.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtContainerPickDescription.DataField = "ContainerPickDescription";
            this.txtContainerPickDescription.Height = 0.2F;
            this.txtContainerPickDescription.Left = 5.75F;
            this.txtContainerPickDescription.Name = "txtContainerPickDescription";
            this.txtContainerPickDescription.Style = "font-size: 8pt; text-align: center";
            this.txtContainerPickDescription.Text = null;
            this.txtContainerPickDescription.Top = 0F;
            this.txtContainerPickDescription.Width = 1.5F;
            // 
            // txtOrigins
            // 
            this.txtOrigins.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtOrigins.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtOrigins.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtOrigins.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtOrigins.DataField = "Origins";
            this.txtOrigins.Height = 0.2F;
            this.txtOrigins.Left = 7.25F;
            this.txtOrigins.Name = "txtOrigins";
            this.txtOrigins.Style = "font-size: 8pt; text-align: center";
            this.txtOrigins.Text = null;
            this.txtOrigins.Top = 0F;
            this.txtOrigins.Width = 0.75F;
            // 
            // txtCosts
            // 
            this.txtCosts.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtCosts.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtCosts.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtCosts.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtCosts.DataField = "Costs";
            this.txtCosts.Height = 0.2F;
            this.txtCosts.Left = 8F;
            this.txtCosts.Name = "txtCosts";
            this.txtCosts.Style = "font-size: 8pt; text-align: center";
            this.txtCosts.Text = null;
            this.txtCosts.Top = 0F;
            this.txtCosts.Width = 0.75F;
            // 
            // txtBoxCode
            // 
            this.txtBoxCode.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtBoxCode.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtBoxCode.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtBoxCode.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtBoxCode.DataField = "BoxCode";
            this.txtBoxCode.Height = 0.2F;
            this.txtBoxCode.Left = 8.75F;
            this.txtBoxCode.Name = "txtBoxCode";
            this.txtBoxCode.Style = "font-size: 8pt; text-align: center";
            this.txtBoxCode.Text = null;
            this.txtBoxCode.Top = 0F;
            this.txtBoxCode.Width = 0.75F;
            // 
            // txtLabourHours
            // 
            this.txtLabourHours.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtLabourHours.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtLabourHours.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtLabourHours.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtLabourHours.DataField = "RoundedLabourHours";
            this.txtLabourHours.Height = 0.2F;
            this.txtLabourHours.Left = 9.5F;
            this.txtLabourHours.Name = "txtLabourHours";
            this.txtLabourHours.Style = "font-size: 8pt; text-align: center";
            this.txtLabourHours.Text = null;
            this.txtLabourHours.Top = 0F;
            this.txtLabourHours.Width = 0.5F;
            // 
            // txtPackQuantity
            // 
            this.txtPackQuantity.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.DataField = "PackQuantity";
            this.txtPackQuantity.Height = 0.2F;
            this.txtPackQuantity.Left = 0.5F;
            this.txtPackQuantity.Name = "txtPackQuantity";
            this.txtPackQuantity.OutputFormat = "#,##0";
            this.txtPackQuantity.Style = "font-size: 8pt; text-align: center";
            this.txtPackQuantity.Text = null;
            this.txtPackQuantity.Top = 0F;
            this.txtPackQuantity.Width = 0.5F;
            // 
            // pageFooter
            // 
            this.pageFooter.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.infoRunTime,
            this.infoPageNumber});
            this.pageFooter.Height = 0.3F;
            this.pageFooter.Name = "pageFooter";
            // 
            // infoRunTime
            // 
            this.infoRunTime.FormatString = "{RunDateTime:yyyy-MM-dd h:mm}";
            this.infoRunTime.Height = 0.2F;
            this.infoRunTime.Left = 0F;
            this.infoRunTime.Name = "infoRunTime";
            this.infoRunTime.Style = "";
            this.infoRunTime.Top = 0.1F;
            this.infoRunTime.Width = 3F;
            // 
            // infoPageNumber
            // 
            this.infoPageNumber.FormatString = "Page {PageNumber} of {PageCount}";
            this.infoPageNumber.Height = 0.2F;
            this.infoPageNumber.Left = 7F;
            this.infoPageNumber.Name = "infoPageNumber";
            this.infoPageNumber.Style = "text-align: right";
            this.infoPageNumber.Top = 0.1F;
            this.infoPageNumber.Width = 3F;
            // 
            // UpgradeSheet
            // 
            this.MasterReport = false;
            this.CompatibilityMode = GrapeCity.ActiveReports.Document.CompatibilityModes.CrossPlatform;
            this.PageSettings.Margins.Bottom = 0.5F;
            this.PageSettings.Margins.Left = 0.5F;
            this.PageSettings.Margins.Right = 0.5F;
            this.PageSettings.Margins.Top = 0.5F;
            this.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 10F;
            this.Sections.Add(this.pageHeader);
            this.Sections.Add(this.detail);
            this.Sections.Add(this.pageFooter);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; font-size: 10pt; " +
            "color: Black; font-family: \"Calibri\"; ddo-char-set: 186", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold; ddo-char-set: 186", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: italic; font-family: \"Calibri\"; d" +
            "do-char-set: 186", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 186", "Heading3", "Normal"));
            ((System.ComponentModel.ISupportInitialize)(this.picLogo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblRequiredDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRequiredDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblAddress)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblQty)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblPackQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblProductComingFrom)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblUPC)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblUPCApprovalRequired)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblContainerPickDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblOrigins)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblCosts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblBoxCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblHours)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtQty)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProductComingFrom)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUPC)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUPCApprovalRequired)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtContainerPickDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrigins)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCosts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBoxCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLabourHours)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPackQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.infoRunTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.infoPageNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion

        private GrapeCity.ActiveReports.SectionReportModel.Picture picLogo;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblDate;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDate;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblRequiredDate;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtRequiredDate;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblAddress;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblQty;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblPackQuantity;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblDescription;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblProductComingFrom;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblUPC;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblUPCApprovalRequired;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblContainerPickDescription;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblOrigins;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblCosts;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblBoxCode;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblHours;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtQty;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDescription;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtProductComingFrom;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtUPC;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtUPCApprovalRequired;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtContainerPickDescription;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtOrigins;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtCosts;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtBoxCode;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtLabourHours;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPackQuantity;
        private GrapeCity.ActiveReports.SectionReportModel.ReportInfo infoRunTime;
        private GrapeCity.ActiveReports.SectionReportModel.ReportInfo infoPageNumber;
    }
}
