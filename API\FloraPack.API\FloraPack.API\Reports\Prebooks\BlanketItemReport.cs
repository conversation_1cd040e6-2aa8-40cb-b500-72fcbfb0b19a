﻿using ClosedXML.Excel;
using static FloraPack.API.Controllers.PrebooksController;

namespace FloraPack.API.Reports.Prebooks;

public static class BlanketItemReport
{
    public static MemoryStream Create(BlanketItemReportModel model)
    {
        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 1;
        var col = 1;

        var hasSeasons = model.BlanketItems.Any(i => !string.IsNullOrWhiteSpace(i.Season));
        var hasWeeks = model.BlanketItems.Any(i => i.BlanketWeekId?.Length == 6);

        sheet.Cell(row, col).SetValue("Vendor");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Part No");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Description");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Start Date");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("End Date");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        if (hasWeeks) {
            sheet.Cell(row, col).SetValue("Week");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        sheet.Cell(row, col).SetValue("Salesperson");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Customer");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Ship To");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        if (hasSeasons) {
            sheet.Cell(row, col).SetValue("Season");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        sheet.Cell(row, col).SetValue("Blanket Qty");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Booked Qty");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Remaining");
        sheet.Cell(row, col).Style.Font.Bold = true;

        sheet.Range(row, 1, row, col).Style.Border.BottomBorder = XLBorderStyleValues.Thin;

        row++;
        foreach (var item in model.BlanketItems) {
            col = 1;

            sheet.Cell(row, col).SetValue(item.Vendor);
            col++;

            sheet.Cell(row, col).SetValue(item.SpirePartNumber);
            col++;

            sheet.Cell(row, col).SetValue(item.Description);
            col++;

            sheet.Cell(row, col).SetValue(item.BlanketStartDate);
            col++;

            sheet.Cell(row, col).SetValue(item.RequiredDate);
            col++;

            if (hasWeeks) {
                if (item.BlanketWeekId?.Length == 6 && int.TryParse(item.BlanketWeekId.Substring(4, 2), out var week)) {
                    sheet.Cell(row, col).SetValue($"Week {week}");
                }

                col++;
            }

            sheet.Cell(row, col).SetValue(item.Salesperson);
            col++;

            sheet.Cell(row, col).SetValue(item.Customer);
            col++;

            sheet.Cell(row, col).SetValue(item.ShipTo);
            col++;

            if (hasSeasons) {
                sheet.Cell(row, col).SetValue(item.Season);
                col++;
            }

            if (!item.BookingItems.Any()) {
                sheet.Cell(row, col).SetValue(item.BlanketQuantity);
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
                sheet.Cell(row, col).Style.Font.Bold = true;
                col++;

                sheet.Cell(row, col).SetValue(item.BookedQuantity);
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
                sheet.Cell(row, col).Style.Font.Bold = true;
                col++;

                sheet.Cell(row, col).FormulaR1C1 = "=RC[-2]-RC[-1]";
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
                sheet.Cell(row, col).Style.Font.Bold = true;

                sheet.Range(row, 1, row, col).Style.Border.BottomBorder = XLBorderStyleValues.Thin;
            }

            row++;

            foreach (var bookingItem in item.BookingItems) {
                col = 5;
                sheet.Cell(row, col).SetValue(bookingItem.RequiredDate);
                sheet.Cell(row, col).Style.Font.Italic = true;
                col++;

                if (hasWeeks) {
                    col++;
                }

                sheet.Cell(row, col).SetValue(bookingItem.Salesperson);
                sheet.Cell(row, col).Style.Font.Italic = true;
                col++;
                sheet.Cell(row, col).SetValue(bookingItem.Customer);
                sheet.Cell(row, col).Style.Font.Italic = true;
                col++;
                sheet.Cell(row, col).SetValue(bookingItem.ShipTo);
                sheet.Cell(row, col).Style.Font.Italic = true;
                col++;
                if (hasSeasons) {
                    sheet.Cell(row, col).SetValue(bookingItem.Season);
                    sheet.Cell(row, col).Style.Font.Italic = true;
                    col++;
                }

                col++;
                sheet.Cell(row, col).SetValue(bookingItem.OrderQuantity);
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
                sheet.Cell(row, col).Style.Font.Italic = true;
                col++;
                row++;
            }

            if (item.BookingItems.Any()) {
                var firstCol = 8;
                if (hasSeasons) {
                    firstCol++;
                }

                if (hasWeeks) {
                    firstCol++;
                }

                col = firstCol;
                sheet.Cell(row, col).SetValue("Blanket Total");
                sheet.Cell(row, col).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                col++;

                sheet.Cell(row, col).SetValue(item.BlanketQuantity);
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
                col++;

                sheet.Cell(row, col).FormulaR1C1 = $"=SUM(R[-{item.BookingItems.Count}]C:R[-1]C)";
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
                col++;

                sheet.Cell(row, col).FormulaR1C1 = "=RC[-2]-RC[-1]";
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");

                sheet.Range(row, firstCol, row, col).Style.Border.TopBorder = XLBorderStyleValues.Double;
                sheet.Range(row, firstCol, row, col).Style.Font.Bold = true;

                sheet.Range(row, 1, row, col).Style.Border.BottomBorder = XLBorderStyleValues.Thin;

                row++;
            }
        }

        sheet.Columns(1, col).AdjustToContents();

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }
}