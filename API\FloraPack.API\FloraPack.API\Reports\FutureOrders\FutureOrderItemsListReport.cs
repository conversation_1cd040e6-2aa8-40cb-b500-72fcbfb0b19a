﻿using System.Globalization;
using ClosedXML.Excel;
using FloraPack.API.Controllers;

namespace FloraPack.API.Reports.FutureOrders;

public static class FutureOrderItemsListReport
{
    public static MemoryStream Create(FutureOrdersController.FutureOrderItemsListReportModel model)
    {
        var hideSeason = model.Items.All(i => string.IsNullOrWhiteSpace(i.Season));

        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 1;
        var col = 1;

        sheet.Cell(row, col).SetValue("Future Order");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Product");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Description");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Date");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Week");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        if (!hideSeason) {
            sheet.Cell(row, col).SetValue("Season");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        sheet.Cell(row, col).SetValue("Customer");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Ship To");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Salesperson");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Vendor");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Cases");
        sheet.Cell(row, col).Style.Font.Bold = true;

        sheet.Range(row, 1, row, col).Style.Border.BottomBorder = XLBorderStyleValues.Thin;

        row++;

        foreach (var item in model.Items) {
            col = 1;

            sheet.Cell(row, col).SetValue(item.FutureOrderId.ToString("00000"));
            col++;

            sheet.Cell(row, col).SetValue(item.SpirePartNumber);
            col++;

            sheet.Cell(row, col).SetValue(item.Description);
            col++;

            var parsed = DateTime.TryParse(item.Date ?? string.Empty, out var date);

            if (parsed) {
                sheet.Cell(row, col).SetValue(date.ToString("MMM d, yyyy"));
            }
            col++;

            if (parsed) {
                sheet.Cell(row, col).SetValue(ISOWeek.GetWeekOfYear(date));
            }
            col++;

            if (!hideSeason) {
                sheet.Cell(row, col).SetValue(item.Season);
                col++;
            }

            sheet.Cell(row, col).SetValue(item.Customer);
            col++;

            sheet.Cell(row, col).SetValue(item.ShipTo);
            col++;

            sheet.Cell(row, col).SetValue(item.Salesperson);
            col++;

            sheet.Cell(row, col).SetValue(item.Vendor);
            col++;

            sheet.Cell(row, col).SetValue(item.OrderQuantity);
            sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");

            row++;
        }

        sheet.Columns(1, col).AdjustToContents();

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }
}