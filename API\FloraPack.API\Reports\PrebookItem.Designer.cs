namespace FloraPack.API.Reports
{
    /// <summary>
    /// Summary description for PrebookItem.
    /// </summary>
    partial class PrebookItem
    {
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing) {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Resources.ResourceManager resources = new System.Resources.ResourceManager(typeof(PrebookItem));
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.txtOrderQuantity = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPreviousQuantity = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtWeightsAndMeasures = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtQtyBox = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtWeightsAndMeasuresBox = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.rtfPotCover = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfPackQuantity = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfBoxCode = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfDateCode = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfRetail = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfUPC = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfDescription = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.reportHeader1 = new GrapeCity.ActiveReports.SectionReportModel.ReportHeader();
            this.reportFooter1 = new GrapeCity.ActiveReports.SectionReportModel.ReportFooter();
            this.txtTotal = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtOrderQuantityTotal = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPreviousQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWeightsAndMeasures)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtQtyBox)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWeightsAndMeasuresBox)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTotal)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderQuantityTotal)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // detail
            // 
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.txtOrderQuantity,
            this.txtPreviousQuantity,
            this.txtWeightsAndMeasures,
            this.txtQtyBox,
            this.txtWeightsAndMeasuresBox,
            this.rtfPotCover,
            this.rtfPackQuantity,
            this.rtfBoxCode,
            this.rtfDateCode,
            this.rtfRetail,
            this.rtfUPC,
            this.rtfDescription});
            this.detail.Height = 0.2F;
            this.detail.Name = "detail";
            this.detail.Format += new System.EventHandler(this.OnFormat);
            this.detail.BeforePrint += new System.EventHandler(this.OnBeforePrint);
            // 
            // txtOrderQuantity
            // 
            this.txtOrderQuantity.DataField = "OrderQuantityDisplay";
            this.txtOrderQuantity.Height = 0.2F;
            this.txtOrderQuantity.Left = 0.3F;
            this.txtOrderQuantity.Name = "txtOrderQuantity";
            this.txtOrderQuantity.OutputFormat = "#,##0";
            this.txtOrderQuantity.Padding = new GrapeCity.ActiveReports.PaddingEx(3, 0, 0, 0);
            this.txtOrderQuantity.Style = "font-weight: bold; text-align: center";
            this.txtOrderQuantity.Text = "Qty";
            this.txtOrderQuantity.Top = 0F;
            this.txtOrderQuantity.Width = 0.3F;
            // 
            // txtPreviousQuantity
            // 
            this.txtPreviousQuantity.DataField = "PreviousOrderQuantity";
            this.txtPreviousQuantity.Height = 0.2F;
            this.txtPreviousQuantity.Left = 0F;
            this.txtPreviousQuantity.Name = "txtPreviousQuantity";
            this.txtPreviousQuantity.OutputFormat = "#,##0";
            this.txtPreviousQuantity.Padding = new GrapeCity.ActiveReports.PaddingEx(0, 0, 3, 0);
            this.txtPreviousQuantity.Style = "text-align: right; text-decoration: line-through";
            this.txtPreviousQuantity.Text = "Prev";
            this.txtPreviousQuantity.Top = 0F;
            this.txtPreviousQuantity.Width = 0.3F;
            // 
            // txtWeightsAndMeasures
            // 
            this.txtWeightsAndMeasures.DataField = "=WeightsAndMeasures ? \"yes\" : \"\"";
            this.txtWeightsAndMeasures.Height = 0.2F;
            this.txtWeightsAndMeasures.Left = 7.265F;
            this.txtWeightsAndMeasures.Name = "txtWeightsAndMeasures";
            this.txtWeightsAndMeasures.OutputFormat = "#,##0";
            this.txtWeightsAndMeasures.Style = "text-align: center";
            this.txtWeightsAndMeasures.Text = null;
            this.txtWeightsAndMeasures.Top = 0F;
            this.txtWeightsAndMeasures.Width = 0.5F;
            // 
            // txtQtyBox
            // 
            this.txtQtyBox.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtQtyBox.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtQtyBox.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtQtyBox.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtQtyBox.Height = 0.2F;
            this.txtQtyBox.Left = 0F;
            this.txtQtyBox.Name = "txtQtyBox";
            this.txtQtyBox.Text = null;
            this.txtQtyBox.Top = 0F;
            this.txtQtyBox.Width = 0.6F;
            // 
            // txtWeightsAndMeasuresBox
            // 
            this.txtWeightsAndMeasuresBox.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasuresBox.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasuresBox.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasuresBox.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasuresBox.Height = 0.2F;
            this.txtWeightsAndMeasuresBox.Left = 7.265F;
            this.txtWeightsAndMeasuresBox.Name = "txtWeightsAndMeasuresBox";
            this.txtWeightsAndMeasuresBox.Text = null;
            this.txtWeightsAndMeasuresBox.Top = 0F;
            this.txtWeightsAndMeasuresBox.Width = 0.5F;
            // 
            // rtfPotCover
            // 
            this.rtfPotCover.AutoReplaceFields = true;
            this.rtfPotCover.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPotCover.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPotCover.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPotCover.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPotCover.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfPotCover.Height = 0.2F;
            this.rtfPotCover.Html = resources.GetString("rtfPotCover.Html");
            this.rtfPotCover.Left = 3.65F;
            this.rtfPotCover.Name = "rtfPotCover";
            this.rtfPotCover.Top = 0F;
            this.rtfPotCover.Width = 1.25F;
            // 
            // rtfPackQuantity
            // 
            this.rtfPackQuantity.AutoReplaceFields = true;
            this.rtfPackQuantity.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPackQuantity.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPackQuantity.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPackQuantity.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfPackQuantity.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfPackQuantity.Height = 0.2F;
            this.rtfPackQuantity.Html = resources.GetString("rtfPackQuantity.Html");
            this.rtfPackQuantity.Left = 0.5999999F;
            this.rtfPackQuantity.Name = "rtfPackQuantity";
            this.rtfPackQuantity.Top = 0F;
            this.rtfPackQuantity.Width = 0.5F;
            // 
            // rtfBoxCode
            // 
            this.rtfBoxCode.AutoReplaceFields = true;
            this.rtfBoxCode.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfBoxCode.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfBoxCode.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfBoxCode.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfBoxCode.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfBoxCode.Height = 0.2F;
            this.rtfBoxCode.Html = resources.GetString("rtfBoxCode.Html");
            this.rtfBoxCode.Left = 7.75F;
            this.rtfBoxCode.Name = "rtfBoxCode";
            this.rtfBoxCode.Top = 0F;
            this.rtfBoxCode.Width = 0.75F;
            // 
            // rtfDateCode
            // 
            this.rtfDateCode.AutoReplaceFields = true;
            this.rtfDateCode.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDateCode.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDateCode.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDateCode.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDateCode.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfDateCode.Height = 0.2F;
            this.rtfDateCode.Html = resources.GetString("rtfDateCode.Html");
            this.rtfDateCode.Left = 5.89F;
            this.rtfDateCode.Name = "rtfDateCode";
            this.rtfDateCode.Top = 0F;
            this.rtfDateCode.Width = 0.75F;
            // 
            // rtfRetail
            // 
            this.rtfRetail.AutoReplaceFields = true;
            this.rtfRetail.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfRetail.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfRetail.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfRetail.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfRetail.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfRetail.Height = 0.2F;
            this.rtfRetail.Html = resources.GetString("rtfRetail.Html");
            this.rtfRetail.Left = 6.65F;
            this.rtfRetail.Name = "rtfRetail";
            this.rtfRetail.Top = 0F;
            this.rtfRetail.Width = 0.6F;
            // 
            // rtfUPC
            // 
            this.rtfUPC.AutoReplaceFields = true;
            this.rtfUPC.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfUPC.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfUPC.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfUPC.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfUPC.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfUPC.Height = 0.2F;
            this.rtfUPC.Html = resources.GetString("rtfUPC.Html");
            this.rtfUPC.Left = 4.9F;
            this.rtfUPC.Name = "rtfUPC";
            this.rtfUPC.Top = 0F;
            this.rtfUPC.Width = 1F;
            // 
            // rtfDescription
            // 
            this.rtfDescription.AutoReplaceFields = true;
            this.rtfDescription.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfDescription.Height = 0.2F;
            this.rtfDescription.Html = resources.GetString("rtfDescription.Html");
            this.rtfDescription.Left = 1.1F;
            this.rtfDescription.Name = "rtfDescription";
            this.rtfDescription.Top = 0F;
            this.rtfDescription.Width = 2.55F;
            // 
            // reportHeader1
            // 
            this.reportHeader1.Height = 0F;
            this.reportHeader1.Name = "reportHeader1";
            // 
            // reportFooter1
            // 
            this.reportFooter1.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.txtTotal,
            this.txtOrderQuantityTotal});
            this.reportFooter1.Name = "reportFooter1";
            // 
            // txtTotal
            // 
            this.txtTotal.Height = 0.2F;
            this.txtTotal.Left = 0.6000001F;
            this.txtTotal.Name = "txtTotal";
            this.txtTotal.OutputFormat = "#,##0";
            this.txtTotal.Padding = new GrapeCity.ActiveReports.PaddingEx(5, 0, 0, 0);
            this.txtTotal.Style = "font-weight: bold; text-align: left";
            this.txtTotal.Text = "Total";
            this.txtTotal.Top = 0F;
            this.txtTotal.Width = 0.6000001F;
            // 
            // txtOrderQuantityTotal
            // 
            this.txtOrderQuantityTotal.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtOrderQuantityTotal.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtOrderQuantityTotal.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtOrderQuantityTotal.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtOrderQuantityTotal.DataField = "OrderQuantity";
            this.txtOrderQuantityTotal.Height = 0.2F;
            this.txtOrderQuantityTotal.Left = 0F;
            this.txtOrderQuantityTotal.Name = "txtOrderQuantityTotal";
            this.txtOrderQuantityTotal.OutputFormat = "#,##0";
            this.txtOrderQuantityTotal.Style = "font-weight: bold; text-align: center";
            this.txtOrderQuantityTotal.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.GrandTotal;
            this.txtOrderQuantityTotal.Text = null;
            this.txtOrderQuantityTotal.Top = -7.450581E-09F;
            this.txtOrderQuantityTotal.Width = 0.6000001F;
            // 
            // PrebookItem
            // 
            this.MasterReport = false;
            this.CompatibilityMode = GrapeCity.ActiveReports.Document.CompatibilityModes.CrossPlatform;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 8.5F;
            this.Sections.Add(this.reportHeader1);
            this.Sections.Add(this.detail);
            this.Sections.Add(this.reportFooter1);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; color: Black; fon" +
            "t-family: \"Calibri\"; ddo-char-set: 186; font-size: 9pt", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Times New Roman; font-size: 14pt; font-weight: bold; font-style: ita" +
            "lic", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold", "Heading3", "Normal"));
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPreviousQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWeightsAndMeasures)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtQtyBox)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWeightsAndMeasuresBox)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTotal)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderQuantityTotal)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion

        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtOrderQuantity;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPreviousQuantity;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtWeightsAndMeasures;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtQtyBox;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtWeightsAndMeasuresBox;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfPotCover;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfPackQuantity;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfBoxCode;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfDateCode;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfRetail;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfUPC;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfDescription;
        private GrapeCity.ActiveReports.SectionReportModel.ReportHeader reportHeader1;
        private GrapeCity.ActiveReports.SectionReportModel.ReportFooter reportFooter1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtOrderQuantityTotal;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtTotal;
    }
}
