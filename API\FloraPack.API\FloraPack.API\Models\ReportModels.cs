using System;
using System.Collections.Generic;
using System.Globalization;

namespace FloraPack.API.Models
{
    // Stub classes to replace GrapeCity ActiveReports dependencies
    // These classes maintain the same structure as the original report classes
    // but without the GrapeCity dependencies

    public class CobornsLabelItem
    {
        public string StoreName { get; set; } = string.Empty;
        public int? StoreNumber { get; set; }
        public string ProductNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string UPC { get; set; } = string.Empty;
        public string Size { get; set; } = string.Empty;
        public DateTime ReceivingDate { get; set; }
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public int? PackQuantity { get; set; }
        public int LabelNumber { get; set; }
        public int LabelCount { get; set; }

        public string DescriptionDisplay => $"{Description} {Size} x {PackQuantity}";
        public string DeliveryDateDisplay => "Delivery Date - " + ReceivingDate.ToString("M/d/yyyy", CultureInfo.InvariantCulture);
        public string PurchaseOrderNumberDisplay => string.IsNullOrWhiteSpace(PurchaseOrderNumber) ? string.Empty : $"PO # - {PurchaseOrderNumber}";
        public string UPCDisplay => UPC;
        public string LabelNumberDisplay => $"{LabelNumber} of {LabelCount}";
        public string StoreNumberDisplay => $"Store #{StoreNumber}";
    }

    public class AlbrechtsLabelItem
    {
        public string StoreName { get; set; } = string.Empty;
        public int? StoreNumber { get; set; }
        public string ProductNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string UPC { get; set; } = string.Empty;
        public string Retail { get; set; } = string.Empty;
        public string Cost { get; set; } = string.Empty;
        public DateTime ReceivingDate { get; set; }
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public int? PackQuantity { get; set; }
        public int LabelNumber { get; set; }
        public int LabelCount { get; set; }

        public string DescriptionDisplay => $"{Description} x {PackQuantity}";
        public string DeliveryDateDisplay => "Delivery Date - " + ReceivingDate.ToString("M/d/yyyy", CultureInfo.InvariantCulture);
        public string PurchaseOrderNumberDisplay => string.IsNullOrWhiteSpace(PurchaseOrderNumber) ? string.Empty : $"PO # - {PurchaseOrderNumber}";
        public string UPCDisplay => UPC;
        public string LabelNumberDisplay => $"{LabelNumber} of {LabelCount}";
        public string StoreNumberDisplay => $"Store #{StoreNumber}";
    }

    public class HeinensLabelItem
    {
        public string StoreNumber { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
        public string ProductNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string UPC { get; set; } = string.Empty;
        public DateTime ReceivingDate { get; set; }
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public int? PackQuantity { get; set; }
        public int LabelNumber { get; set; }
        public int LabelCount { get; set; }

        public string DescriptionDisplay => $"{Description}\nPack {PackQuantity}";
        public string PurchaseOrderNumberDisplay => string.IsNullOrWhiteSpace(PurchaseOrderNumber) ? string.Empty : $"PO#:{PurchaseOrderNumber}";
        public string LabelNumberDisplay => LabelNumber == 0 ? "SKID" : $"{LabelNumber} of {LabelCount}";
        public string ProductNumberDisplay => $"Article# {ProductNumber}";
    }

    public class UpgradeSheet
    {
        public UpgradeSheet(List<FloraPack.API.Repositories.FutureOrders.UpgradeItem> items)
        {
            // Stub constructor - GrapeCity ActiveReports not available
        }
    }
}
