﻿namespace FloraPack.API.Repositories.Prebooks;

public class PrebookDetail
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public DateTime? RequiredDate { get; set; }
    public bool IsBlanket { get; set; }
    public DateTime? BlanketStartDate { get; set; }
    public bool BlanketIsClosed { get; set; }
    public string? SeasonName { get; set; }
    public string? BoxCode { get; set; }
    public int? VendorId { get; set; }
    public string? VendorName { get; set; }
    public int? SalespersonId { get; set; }
    public string? SalespersonName { get; set; }
    public int? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public int? ShipToId { get; set; }
    public string? ShipToName { get; set; }
    public int? SpirePurchaseOrderId { get; set; }
    public string? SpirePurchaseOrderNumber { get; set; }
    public int? FutureOrderId { get; set; }
    public string? Comments { get; set; }
    public string? GrowerItemNotes { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime Modified { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime? Confirmed { get; set; }
    public string? ConfirmedBy { get; set; }
    public DateTime? Deleted { get; set; }
    public string? DeletedBy { get; set; }

    public List<PrebookDetailItem> Items { get; set; } = new();
}
