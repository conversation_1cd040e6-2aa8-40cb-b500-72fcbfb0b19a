﻿using Dapper;
using FloraPack.API.Repositories.FutureOrders;

namespace FloraPack.API.Repositories.Phytos;

public class PhytoEmailRepository(IConfiguration configuration) : RepositoryBase(configuration)
{
    public async Task Create(FutureOrderDetail detail, IEnumerable<string> to, string subject, string body, string from)
    {
        if (!detail.Items.Any(i => i.PhytoRequired)) {
            return;
        }

        var now = DateTime.Now;

        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var id = await connection.ExecuteScalarAsync<int>(@"insert into future_order_phyto_emails (
    future_order_id,
    required_date,
    box_code,
    created,
    created_by,
    subject,
    body,
    ""to"",
    ""from""
) values (
    @futureOrderId,
    @requiredDate,
    @boxCode,
    @now,
    @from,
    @subject,
    @body,
    @to,
    @from
) returning id;",
            new {
                FutureOrderId = detail.Id,
                detail.RequiredDate,
                detail.BoxCode,
                now,
                from,
                subject,
                body,
                to = string.Join(", ", to)
            }, transaction: tx);

        await connection.ExecuteAsync(@"insert into future_order_phyto_email_items (
    future_order_phyto_email_id,
    future_order_item_id,
    spire_part_number,
    ""description"",
    vendor_name,
    order_quantity
) values (
    @phytoEmailId,
    @id,
    @spirePartNumber,
    @description,
    @vendorName,
    @orderQuantity    
);
", detail.Items
            .Where(i => i.PhytoRequired)
            .Select(i => new {
                PhytoEmailId = id,
                i.Id,
                i.SpirePartNumber,
                i.Description,
                i.VendorName,
                i.OrderQuantity
            }), transaction: tx);

        await tx.CommitAsync();
    }

    public async Task<bool> HasChanged(FutureOrderDetail detail)
    {
        var detailItems = detail.Items.Where(i => i.PhytoRequired).ToList();

        var connection = GetConnection();
        await using var multi = await connection.QueryMultipleAsync(@"
            select * from future_order_phyto_emails where future_order_id = @id order by created desc limit 1;
            select i.* from future_order_phyto_email_items i join future_order_phyto_emails e on e.id = i.future_order_phyto_email_id where e.future_order_id = @id;
", new { detail.Id });

        var email = await multi.ReadFirstOrDefaultAsync<PhytoEmail>();
        if (email == null) {
            return detailItems.Any();
        }

        if(detail.RequiredDate != email.RequiredDate || detail.BoxCode != email.BoxCode) {
            return true;
        }

        var items = (await multi.ReadAsync<PhytoEmailItem>())
            .Where(i => i.FutureOrderPhytoEmailId == email.Id)
            .ToList();

        // new items not on the previous email
        if (detailItems.Any(i => !items.Any(ei => ei.FutureOrderItemId == i.Id))) {
            return true;
        }

        if (items.Any(ei => !detailItems.Any(i => i.Id == ei.FutureOrderItemId))) {
            return true;
        }

        //items that have changed
        if (items.Any(i => {
                var detailItem = detailItems.First(di => di.Id == i.FutureOrderItemId);
                return detailItem.OrderQuantity != i.OrderQuantity ||
                       detailItem.SpirePartNumber != i.SpirePartNumber ||
                       detailItem.VendorName != i.VendorName;
            })) {
            return true;
        }

        return false;
    }
}