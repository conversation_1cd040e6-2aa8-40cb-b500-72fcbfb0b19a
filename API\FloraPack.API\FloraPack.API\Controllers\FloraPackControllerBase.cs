﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph;
using Microsoft.Identity.Client;

namespace FloraPack.API.Controllers;

[ApiController]
public abstract class FloraPackControllerBase : ControllerBase
{
    private readonly GraphServiceClient _graphServiceClient;

    protected FloraPackControllerBase(IConfiguration configuration)
    {
        var clientId = configuration["AzureAd:ClientId"];
        var tenantId = configuration["AzureAd:TenantId"];
        var clientSecret = configuration["AzureAd:ClientSecret"];

        var scopes = configuration.GetSection("AzureAd:Scopes").Get<string[]>();

        var clientApplication = ConfidentialClientApplicationBuilder
            .Create(clientId)
            .WithTenantId(tenantId)
            .WithClientSecret(clientSecret)
            .Build();
        var authProvider = new DelegateAuthenticationProvider(async (request) => {
            // Use Microsoft.Identity.Client to retrieve token
            var assertion = new UserAssertion(UserAccessToken);
            var result = await clientApplication.AcquireTokenOnBehalfOf(scopes, assertion).ExecuteAsync();

            request.Headers.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", result.AccessToken);
        });

        _graphServiceClient = new GraphServiceClient(authProvider);
    }

    protected async Task<string?> UserEmail()
    {
        var client = GetGraphServiceClient();
        var me = await client.Me.Request().GetAsync();
        return me?.Mail ?? me?.UserPrincipalName;
    }

    protected async Task<string?> UserName()
    {
        var client = GetGraphServiceClient();
        var me = await client.Me.Request().GetAsync();
        return me.DisplayName;
    }

    protected string UserAccessToken => Request.Headers.Authorization.ToString().Replace("Bearer ", "");

    protected GraphServiceClient GetGraphServiceClient() => _graphServiceClient;

    protected async Task SendMail(string subject, string htmlBody, string to) => await SendMail(subject, htmlBody, new List<string> { to });

    protected async Task SendMail(string subject, string htmlBody, IEnumerable<string> to, IEnumerable<string>? cc = null,
        IEnumerable<string>? bcc = null, IEnumerable<EmailAttachment>? reports = null)
    {
        var attachments = new MessageAttachmentsCollectionPage();
        reports?.ToList().ForEach(r => attachments.Add(new FileAttachment {
            ODataType = "#microsoft.graph.fileAttachment",
            ContentBytes = r.Content,
            ContentType = r.ContentType,
            ContentId = r.FileName,
            Name = r.FileName
        }));

        var message = new Message {
            Subject = subject,
            Body = new ItemBody {
                ContentType = BodyType.Html,
                Content = htmlBody
            },
            ToRecipients = to.Select(t =>
                new Recipient {
                    EmailAddress = new EmailAddress {
                        Address = t
                    }
                }),
            CcRecipients = (cc ?? new List<string>()).Select(c =>
                new Recipient {
                    EmailAddress = new EmailAddress {
                        Address = c
                    }
                }
            ),
            BccRecipients = (bcc ?? new List<string>()).Select(b =>
                new Recipient {
                    EmailAddress = new EmailAddress {
                        Address = b
                    }
                }
            ),
            Attachments = attachments
        };

        await GetGraphServiceClient().Me.SendMail(message, true)
            .Request()
            .PostAsync();
    }

    public record EmailAttachment(byte[] Content, string FileName, string ContentType);

}