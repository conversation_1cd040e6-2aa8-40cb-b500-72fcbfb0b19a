﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class SalesCustomer
{
    [JsonPropertyName("_id")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("_rev")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string Revision { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type => "sales-customer";

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

}