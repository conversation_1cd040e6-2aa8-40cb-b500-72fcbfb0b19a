﻿namespace FloraPack.API.Spire;

public class EmailAttachment
{
    public string MimeType { get; set; } = string.Empty;

    public string Filename { get; set; } = string.Empty;

    public EmailAttachmentLinks Links { get; set; } = new();

    public class EmailAttachmentLinks
    {
        public string Self { get; set; } = string.Empty;

        public string Data { get; set; } = string.Empty;
    }
}