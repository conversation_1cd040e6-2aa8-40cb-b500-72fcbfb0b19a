﻿namespace FloraPack.API.Repositories.LargeOrders;

public class LargeOrderEmail
{
    public int Id { get; set; }
    public int PrebookId { get; set; }
    public DateTime RequiredDate { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string From { get; set; } = string.Empty;

    public List<LargeOrderEmailItem> Items { get; set; } = new();
}