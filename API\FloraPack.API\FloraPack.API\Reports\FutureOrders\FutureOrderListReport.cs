﻿using System.Globalization;
using ClosedXML.Excel;
using FloraPack.API.Controllers;

namespace FloraPack.API.Reports.FutureOrders;

public static class FutureOrderListReport
{
    public static MemoryStream Create(FutureOrdersController.FutureOrderListReportModel model)
    {
        var hideSeason = model.Items.All(i => string.IsNullOrWhiteSpace(i.Season));
        var hideDollars = model.Items.All(i => i.DollarValue == 0);
        var hideSpireOrderNumber = model.Items.All(i => string.IsNullOrWhiteSpace(i.SpireSalesOrderNumber));

        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 1;
        var col = 1;

        sheet.Cell(row, col).SetValue("Order ID");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Date");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Week");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        if (!hideSeason) {
            sheet.Cell(row, col).SetValue("Season");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        sheet.Cell(row, col).SetValue("Customer");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Ship To");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Salesperson");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Truck");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        if (!hideSpireOrderNumber) {
            sheet.Cell(row, col).SetValue("Spire Order #");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        if (!hideDollars) {
            sheet.Cell(row, col).SetValue("$");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        sheet.Cell(row, col).SetValue("Cases");
        sheet.Cell(row, col).Style.Font.Bold = true;
        var caseCol = col;

        sheet.Range(row, 1, row, col).Style.Border.BottomBorder = XLBorderStyleValues.Thin;

        row++;

        foreach (var item in model.Items) {
            col = 1;

            sheet.Cell(row, col).SetValue(item.Id.ToString("00000"));
            col++;

            var parsed = DateTime.TryParse(item.RequiredDate ?? string.Empty, out var date);

            if (parsed) {
                sheet.Cell(row, col).SetValue(date.ToString("MMM d, yyyy"));
            }
            col++;

            if (parsed) {
                sheet.Cell(row, col).SetValue(ISOWeek.GetWeekOfYear(date));
            }
            col++;

            if (!hideSeason) {
                sheet.Cell(row, col).SetValue(item.Season);
                col++;
            }

            sheet.Cell(row, col).SetValue(item.Customer);
            col++;

            sheet.Cell(row, col).SetValue(item.ShipTo);
            col++;

            sheet.Cell(row, col).SetValue(item.Salesperson);
            col++;

            sheet.Cell(row, col).SetValue(item.Truck);
            col++;

            if (!hideSpireOrderNumber) {
                sheet.Cell(row, col).SetValue(item.SpireSalesOrderNumber);
                col++;
            }

            if (!hideDollars) {
                if (item.DollarValue != 0) {
                    sheet.Cell(row, col).SetValue(item.DollarValue);
                    sheet.Cell(row, col).Style.NumberFormat.SetFormat("$#,##0.00");
                }

                col++;
            }


            if (!item.Prebooks.Any()) {
                sheet.Cell(row, col).SetValue(item.CaseCount);
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
            }

            row++;

            foreach (var prebook in item.Prebooks) {
                col = caseCol - 1;

                sheet.Cell(row, 1).SetValue(prebook.Id.ToString("00000"));
                sheet.Cell(row, 1).Style.Font.Italic = true;

                sheet.Range(sheet.Cell(row, 2), sheet.Cell(row, col)).Merge();

                sheet.Cell(row, 2).SetValue(prebook.Vendor);
                sheet.Cell(row, 2).Style.Font.Italic = true;
                sheet.Cell(row, 2).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;

                col = caseCol;

                sheet.Cell(row, col).SetValue(prebook.CaseCount);
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
                sheet.Cell(row, col).Style.Font.Italic = true;

                row++;
            }

            if (item.Prebooks.Any()) {
                col = caseCol - 1;

                sheet.Cell(row, col).SetValue("Total Cases");
                sheet.Cell(row, col).Style.Font.Italic = true;
                sheet.Cell(row, col).Style.Font.Bold = true;
                col++;

                sheet.Cell(row, col).FormulaR1C1 = $"=SUM(R[-{item.Prebooks.Count}]C:R[-1]C)";
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
                sheet.Cell(row, col).Style.Font.Italic = true;
                sheet.Cell(row, col).Style.Font.Bold = true;

                row++;
            }
        }

        sheet.Columns(1, col).AdjustToContents();

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }
}