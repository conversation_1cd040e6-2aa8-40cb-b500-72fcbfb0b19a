﻿namespace FloraPack.API.Repositories.Boekestyn.Entities
{
    public class HarvestingLabourReportItem
    {
        public DateTime ScheduleDate { get; init; }
        public string PlantSize { get; init; } = string.Empty;
        public string PlantCrop { get; init; } = string.Empty;
        public string Customer { get; init; } = string.Empty;
        public string Variety {  get; init; } = string.Empty;
        public int Harvested { get; init; }
        public int ThrownOut { get; init; }
        public int NumberTwos {  get; init; }
        public decimal CrewSize { get; init; }
        public decimal ActualHours { get; init; }
        public decimal ManHours { get; init; }
        public string? Comments { get; init; } = string.Empty;
    }
}
