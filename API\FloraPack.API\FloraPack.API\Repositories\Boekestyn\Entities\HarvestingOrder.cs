﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Repositories.Boekestyn.Entities
{
    public class HarvestingOrder
    {
        [JsonPropertyName("_id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("_rev")]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? Revision { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; } = "order";

        [JsonPropertyName("orderNumber")]
        public string OrderNumber { get; set; } = string.Empty;

        [JsonPropertyName("stickDate")]
        public string StickDate { get; set; } = string.Empty;

        [JsonPropertyName("flowerDate")]
        public string FlowerDate { get; set; } = string.Empty;

        [JsonPropertyName("pots")]
        public int Pots { get; set; }

        [JsonPropertyName("cases")]
        public int Cases { get; set; }

        [JsonPropertyName("tableCountSpaced")]
        public int TableCountSpaced { get; set; }

        [JsonPropertyName("notes")]
        public string? Notes { get; set; }

        [JsonPropertyName("customer")]
        public HarvestingOrderCustomer Customer { get; set; } = new();

        [JsonPropertyName("plant")]
        public HarvestingOrderPlant Plant { get; set; } = new();

        [JsonPropertyName("varieties")]
        public List<HarvestingOrderVariety> Varieties { get; set; } = [];

        [JsonPropertyName("stickZone")]
        public HarvestingOrderZone StickZone { get; set; } = new();

        [JsonPropertyName("fullSpaceZone")]
        public HarvestingOrderZone? FullSpaceZone { get; set; }

        [JsonPropertyName("lightsOutZone")]
        public HarvestingOrderZone? LightsOutZone { get; set; }

        public List<HarvestingOrderRound> Rounds { get; set; } = [];
        public int StickingWorkOrder { get; internal set; }

        public class HarvestingOrderCustomer
        {
            [JsonPropertyName("abbreviation")]
            public string Abbreviation { get; set; } = string.Empty;

            [JsonPropertyName("name")]
            public string Name { get; set; } = string.Empty;
        }

        public class HarvestingOrderPlant
        {
            [JsonPropertyName("_id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("name")]
            public string Name { get; set; } = string.Empty;

            [JsonPropertyName("abbreviation")]
            public string Abbreviation { get; set; } = string.Empty;

            [JsonPropertyName("crop")]
            public string Crop { get; set; } = string.Empty;

            [JsonPropertyName("size")]
            public string Size { get; set; } = string.Empty;

            [JsonPropertyName("cuttingsPerPot")]
            public int CuttingsPerPot { get; set; }

            [JsonPropertyName("cuttingsPerTableTight")]
            public int CuttingsPerTableTight { get; set; }

            [JsonPropertyName("cuttingsPerTableSpaced")]
            public int CuttingsPerTableSpaced { get; set; }

            [JsonPropertyName("cuttingsPerTablePartiallySpaced")]
            public int? CuttingsPerTablePartiallySpaced { get; set; }

            [JsonPropertyName("potsPerCase")]
            public int PotsPerCase { get; set; }

            [JsonPropertyName("stickingCuttingsPerHour")]
            public int StickingCuttingsPerHour { get; set; }

            [JsonPropertyName("spacingPotsPerHour")]
            public int SpacingPotsPerHour { get; set; }

            [JsonPropertyName("packingCasesPerHour")]
            public int PackingCasesPerHour { get; set; }

            [JsonPropertyName("colour")]
            public string Colour { get; set; } = string.Empty;

            [JsonPropertyName("hasPinching")]
            public bool HasPinching { get; set; }

            [JsonPropertyName("daysToPinch")]
            public int DaysToPinch { get; set; }

            [JsonPropertyName("defaultStickingCrewSize")]
            public int? DefaultStickingCrewSize { get; set; }
        }

        public class HarvestingOrderVariety
        {
            [JsonPropertyName("name")]
            public string Name { get; set; } = string.Empty;

            [JsonPropertyName("cuttings")]
            public int Cuttings { get; set; }

            [JsonPropertyName("pots")]
            public int Pots { get; set; }

            [JsonPropertyName("cases")]
            public int Cases { get; set; }

            [JsonPropertyName("comment")]
            public string? Comment { get; set; }
        }

        public class HarvestingOrderZone
        {
            [JsonPropertyName("name")]
            public string Name { get; set; } = string.Empty;

            [JsonPropertyName("tables")]
            public int Tables { get; set; }

            [JsonPropertyName("_id")]
            public string Id { get; set; } = string.Empty;
        }
    }
}
