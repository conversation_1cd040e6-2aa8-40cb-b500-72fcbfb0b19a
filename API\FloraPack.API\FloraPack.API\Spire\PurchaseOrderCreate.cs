﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Spire;

public class PurchaseOrderCreate
{
    [JsonPropertyName("vendor")]
    public PurchaseOrderVendor Vendor { get; set; } = new();

    [JsonPropertyName("items")]
    public List<PurchaseOrderItem> Items { get; set; } = new();

    public class PurchaseOrderVendor
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }
    }

    public class PurchaseOrderItem
    {
        [JsonPropertyName("orderQty")]
        public int OrderQty { get; set; }

        [JsonPropertyName("inventory")]
        public PurchaseOrderItemInventory Inventory { get; set; } = new();
    }

    public class PurchaseOrderItemInventory
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }
    }
}

