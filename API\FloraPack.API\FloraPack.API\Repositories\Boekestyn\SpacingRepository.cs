﻿using Dapper;
using FloraPack.API.Repositories.Boekestyn.Entities;
using System.Data;

namespace FloraPack.API.Repositories.Boekestyn;

public class SpacingRepository(IConfiguration configuration) : BoekestynRepository(configuration)
{
    public async Task<IEnumerable<SpacingLine>> GetSpacingLines() =>
        await GetConnection().QueryAsync<SpacingLine>("SELECT * FROM spacing_lines");

    public async Task<IEnumerable<SpacingSchedule>> GetSpacingSchedules(DateTime date)
    {
        var multi = await GetConnection().QueryMultipleAsync(@"
SELECT id, to_char(date, 'yyyy-MM-dd') date, line_id FROM spacing_schedules WHERE date = @date;
SELECT o.* FROM spacing_schedules s join spacing_work_orders o on s.id = o.schedule_id WHERE s.date = @date ORDER BY o.sort_order;
SELECT v.* FROM spacing_schedules s join spacing_work_orders o on s.id = o.schedule_id join spacing_work_order_varieties v on o.id = v.work_order_id WHERE s.date = @date;", new { date });

        var schedules = (await multi.ReadAsync<SpacingSchedule>()).ToList();
        var orders = (await multi.ReadAsync<SpacingWorkOrder>()).ToLookup(o => o.ScheduleId);
        var varieties = (await multi.ReadAsync<SpacingWorkOrderVariety>()).ToLookup(v => v.WorkOrderId);

        foreach (var line in schedules.Where(l => orders.Contains(l.Id))) {
            var workOrders = orders[line.Id].ToList();
            foreach (var order in workOrders) {
                order.Varieties.AddRange(varieties[order.Id]);
            }
            line.WorkOrders.AddRange(workOrders);
        }

        return schedules;
    }

    public async Task AddSpacingOrderToSpacingSchedule(
    int crewSize,
    int potsToSpace,
    string fromSpaceType,
    string toSpaceType,
    bool requiresPinching,
    string robotProgram,
    string? comments,
        SpacingSchedule schedule, SpacingOrder order)
    {
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var scheduleId = schedule.Id;

        if (scheduleId <= 0) {
            scheduleId = await connection.QuerySingleAsync<int>(@"
INSERT INTO spacing_schedules (date, line_id) VALUES (@date, @lineId) 
ON CONFLICT (date, line_id) 
DO UPDATE SET date = EXCLUDED.date, line_id = EXCLUDED.line_id
RETURNING id;", new { scheduleId, lineId = schedule.LineId, date = DateTime.Parse(schedule.Date) }, transaction: tx);
        }

        var sortOrder = await connection.QuerySingleAsync<int>("SELECT coalesce(max(sort_order), 0) + 1 FROM spacing_work_orders WHERE schedule_id = @scheduleId", new { scheduleId }, transaction: tx);
        var workOrderId = await connection.ExecuteScalarAsync<int>(@"
INSERT INTO spacing_work_orders (
    schedule_id,
    sort_order,
    order_id,
    order_number,
    plant_size,
    plant_crop,
    plant_has_pinching,
    spacing_pots_per_hour,
    cuttings_per_pot,
    cuttings_per_table_tight,
    cuttings_per_table_partially_spaced,
    cuttings_per_table_spaced,
    customer,
    order_pots,
    order_cases,
    zone,
    estimated_hours,
    pots_to_space,
    crew_size,
    from_space_type,
    to_space_type,
    requires_pinching,
    robot_program,
    order_comments,
    spacing_comments
) 
VALUES (@scheduleId, @sortOrder, @orderId, @orderNumber, @size, @crop, @hasPinching, @spacingPotsPerHour, @cuttingsPerPot, @cuttingsPerTableTight, @cuttingsPerTablePartiallySpaced, @cuttingsPerTableSpaced,
    @customer, @orderPots, @orderCases, @zone, @spacingHours, @potsToSpace,
    @crewSize, @fromSpaceType, @toSpaceType, @requiresPinching, @robotProgram, @notes, @comments)
RETURNING spacing_work_orders.id;",
            new { scheduleId, sortOrder, OrderId = order.Id, order.OrderNumber, order.Plant.Size, order.Plant.Crop, order.Plant.HasPinching,
                order.Plant.SpacingPotsPerHour,
                order.Plant.CuttingsPerPot,
                order.Plant.CuttingsPerTableTight,
                order.Plant.CuttingsPerTablePartiallySpaced,
                order.Plant.CuttingsPerTableSpaced,
                Customer = order.Customer.Name, 
                OrderPots = order.Pots, OrderCases = order.Cases, Zone = order.SpaceZone.Name, order.SpacingHours, potsToSpace, crewSize, fromSpaceType, toSpaceType, 
                requiresPinching, robotProgram, order.Notes, comments },
            transaction: tx);
        await connection.ExecuteAsync(@"
UPDATE spacing_work_orders SET sort_order = u.sort_order
FROM (SELECT id, row_number() over (order by sort_order, id) sort_order from spacing_work_orders u where schedule_id = @scheduleId) u
where u.id = spacing_work_orders.id;", new { scheduleId }, transaction: tx);

        await connection.ExecuteAsync(@"
INSERT INTO spacing_work_order_varieties (work_order_id, name, pots, cases, comment) 
VALUES (@workOrderId, @name, @pots, @cases, @comment);",
            order.Varieties.Select(v => new { workOrderId, v.Name, v.Pots, v.Cases, v.Comment }),
            transaction: tx);

        await tx.CommitAsync();
    }

    public async Task<IEnumerable<SpacingWorkOrderItem>> GetSpacingWorkOrders(DateTime date)
    {
        await using var connection = GetConnection();

        var parameters = new DynamicParameters();
        parameters.Add("schedule_date_param", date, DbType.Date);
        var workOrders = (await connection.QueryAsync<SpacingWorkOrderItem>("SELECT * FROM boekestyn_spacing_work_orders(@schedule_date_param);", parameters)).ToList();
        var ids = workOrders.Count == 0 ? "-1" : string.Join(", ", workOrders.Select(w => w.Id));
        var varieties = (await connection.QueryAsync<SpacingWorkOrderVariety>($"SELECT * FROM spacing_work_order_varieties WHERE work_order_id IN ({ids})")).ToLookup(v => v.WorkOrderId);
        var labour = (await connection.QueryAsync<SpacingWorkOrderLabour>(@$"
SELECT id, work_order_id, crew_size, to_char(cast(start_time at time zone 'UTC' at time zone 'EDT' as timestamp), 'yyyy-MM-dd HH24:MI:SS') start_time, 
to_char(cast(end_time at time zone 'UTC' at time zone 'EDT' as timestamp), 'yyyy-MM-dd HH24:MI:SS') end_time, comments, final_labour 
FROM spacing_work_order_labour WHERE work_order_id IN ({ids})")).ToLookup(l => l.WorkOrderId);

        foreach (var order in workOrders) {
            if (varieties.Contains(order.Id)) {
                order.Varieties.AddRange(varieties[order.Id]);
            }

            if (labour.Contains(order.Id)) {
                order.Labour.AddRange(labour[order.Id]);
            }
        }

        return workOrders;
    }

    public async Task SortSpacingWorkOrders(IEnumerable<SpacingWorkOrderSort> workOrders) =>
        await GetConnection().ExecuteAsync("UPDATE spacing_work_orders SET sort_order = @sortOrder WHERE id = @workOrderId;", workOrders);

    public async Task UpdateSpacingWorkOrderComment(int id, string? comment) =>
        await GetConnection().ExecuteAsync("UPDATE spacing_work_orders SET spacing_comments = @comment WHERE id = @id;", new { id, comment });

    public async Task DeleteSpacingWorkOrder(int id)
    {
        await using var connection = GetConnection();
        var scheduleId = await connection.QuerySingleAsync<int>("SELECT schedule_id from spacing_work_orders WHERE id = @id", new { id });
        await connection.ExecuteAsync(@"
DELETE FROM spacing_work_orders WHERE id = @id;
update spacing_work_orders set sort_order = u.sort_order
from (select id, row_number() over (order by sort_order, id) sort_order from spacing_work_orders u where schedule_id = @scheduleId) u
where u.id = spacing_work_orders.id;", new { id, scheduleId });
    }

    public async Task StartSpacingLabour(int workOrderId, int crewSize)
    {
        await StopSpacingLabour(workOrderId, crewSize);
        await GetConnection().ExecuteAsync("INSERT INTO spacing_work_order_labour (work_order_id, crew_size) values (@workOrderId, @crewSize);", new { workOrderId, crewSize });
    }

    public async Task StopSpacingLabour(int workOrderId, int crewSize, string? comments = null, bool finalLabour = false) =>
        await GetConnection().ExecuteAsync("UPDATE spacing_work_order_labour SET end_time = current_timestamp, comments = coalesce(@comments, comments), final_labour = @finalLabour WHERE work_order_id = @workOrderId AND end_time IS NULL;",
            new { workOrderId, crewSize, comments, finalLabour });

    public async Task<IEnumerable<SpacingWorkOrderHistory>> GetSpacingWorkOrderHistory(string[] orderIds) =>
        await GetConnection().QueryAsync<SpacingWorkOrderHistory>("SELECT * FROM spacing_work_order_history(@work_orders_param);", new { work_orders_param = orderIds });

    public async Task<IEnumerable<SpacingLabourReportItem>> GetSpacingLabourReportItems(DateTime start, DateTime end) =>
        await GetConnection().QueryAsync<SpacingLabourReportItem>(@"
select
  s.date schedule_date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer,
  sum(wo.pots_to_space) as pots,
  from_space_type,
  to_space_type,
  wo.requires_pinching,
  sum(wo.estimated_hours) estimated_hours,
  coalesce(sum(ROUND(EXTRACT(EPOCH FROM (l.end_time - l.start_time)) / 3600.0, 2)), 0) as actual_hours,
  coalesce(string_agg(nullif(trim(l.comments), ''), '\n'), '') comments
from
  spacing_schedules s
  join spacing_work_orders wo on s.id = wo.schedule_id
  left join (select * from spacing_work_order_labour where end_time is not null) l on wo.id = l.work_order_id
  left join (select * from spacing_work_order_labour where final_labour = true) f on wo.id = f.work_order_id
where
  s.date between @start and @end
group by
  s.date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer,
  from_space_type,
  to_space_type,
  wo.requires_pinching
order by
  s.date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer;", new { start, end });


    public class SpacingWorkOrderSort
    {
        public int WorkOrderId { get; init; }
        public int SortOrder { get; init; }
    }

    public class SpacingWorkOrderHistory
    {
        public string OrderId { get; set; }
        public int PotsPartiallySpaced { get; set; }
        public int PotsFullySpaced { get; set; }
    }
}
