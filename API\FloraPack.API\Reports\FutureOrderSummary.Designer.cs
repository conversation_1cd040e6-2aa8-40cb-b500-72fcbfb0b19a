namespace FloraPack.API.Reports
{
    /// <summary>
    /// Summary description for FutureOrderSummary.
    /// </summary>
    partial class FutureOrderSummary
    {
        private GrapeCity.ActiveReports.SectionReportModel.PageHeader pageHeader;
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;
        private GrapeCity.ActiveReports.SectionReportModel.PageFooter pageFooter;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing) {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FutureOrderSummary));
            this.pageHeader = new GrapeCity.ActiveReports.SectionReportModel.PageHeader();
            this.picLogo = new GrapeCity.ActiveReports.SectionReportModel.Picture();
            this.txtFutureOrderNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.lblFutureOrderNumber = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label4 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox4 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPhytos = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.lblSpecialLabels = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtSpecialLabels = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtSpecialLabelDetails = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.srptItems = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.lblCustomer = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtCustomer = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.lblShipTo = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtShipTo = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.llbPONumber = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtPONumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label1 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtArrivalDate = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label2 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox2 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label3 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox3 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label6 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox6 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label7 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox7 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label9 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label10 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label11 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label12 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label13 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label16 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label17 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label18 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label19 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label20 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblComment = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblFreight = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtFreight = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.pageFooter = new GrapeCity.ActiveReports.SectionReportModel.PageFooter();
            this.infoPageNumber = new GrapeCity.ActiveReports.SectionReportModel.ReportInfo();
            this.reportInfo1 = new GrapeCity.ActiveReports.SectionReportModel.ReportInfo();
            this.lblShipmentDetails = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtShipmentDetails = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.lblInternalNotes = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtInternalNotes = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.reportHeader = new GrapeCity.ActiveReports.SectionReportModel.ReportHeader();
            this.reportFooter = new GrapeCity.ActiveReports.SectionReportModel.ReportFooter();
            this.futureOrderHeader = new GrapeCity.ActiveReports.SectionReportModel.GroupHeader();
            this.futureOrderFooter = new GrapeCity.ActiveReports.SectionReportModel.GroupFooter();
            this.line2 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.txtTotalCases = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtTotalPrice = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.line1 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.textBox8 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFutureOrderNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFutureOrderNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhytos)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblSpecialLabels)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSpecialLabels)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSpecialLabelDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblCustomer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblShipTo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShipTo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.llbPONumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPONumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtArrivalDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblComment)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFreight)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFreight)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.infoPageNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.reportInfo1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblShipmentDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShipmentDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblInternalNotes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInternalNotes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTotalCases)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTotalPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // pageHeader
            // 
            this.pageHeader.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.picLogo,
            this.txtFutureOrderNumber,
            this.lblFutureOrderNumber,
            this.label4,
            this.textBox4,
            this.txtPhytos,
            this.lblSpecialLabels,
            this.txtSpecialLabels,
            this.txtSpecialLabelDetails});
            this.pageHeader.Height = 0.6888889F;
            this.pageHeader.Name = "pageHeader";
            // 
            // picLogo
            // 
            this.picLogo.Height = 0.5F;
            this.picLogo.ImageBase64String = resources.GetString("picLogo.ImageBase64String");
            this.picLogo.Left = 0F;
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeMode = GrapeCity.ActiveReports.SectionReportModel.SizeModes.Zoom;
            this.picLogo.Top = 0F;
            this.picLogo.Width = 11F;
            // 
            // txtFutureOrderNumber
            // 
            this.txtFutureOrderNumber.DataField = "Id";
            this.txtFutureOrderNumber.Height = 0.2F;
            this.txtFutureOrderNumber.Left = 1F;
            this.txtFutureOrderNumber.Name = "txtFutureOrderNumber";
            this.txtFutureOrderNumber.OutputFormat = "00000";
            this.txtFutureOrderNumber.Style = "font-weight: bold";
            this.txtFutureOrderNumber.Text = "Future Order #";
            this.txtFutureOrderNumber.Top = 0.09999999F;
            this.txtFutureOrderNumber.Width = 2F;
            // 
            // lblFutureOrderNumber
            // 
            this.lblFutureOrderNumber.Height = 0.2F;
            this.lblFutureOrderNumber.HyperLink = null;
            this.lblFutureOrderNumber.Left = 0F;
            this.lblFutureOrderNumber.Name = "lblFutureOrderNumber";
            this.lblFutureOrderNumber.Style = "";
            this.lblFutureOrderNumber.Text = "Future Order #:";
            this.lblFutureOrderNumber.Top = 0.09999999F;
            this.lblFutureOrderNumber.Width = 1F;
            // 
            // label4
            // 
            this.label4.Height = 0.2F;
            this.label4.HyperLink = null;
            this.label4.Left = 8F;
            this.label4.Name = "label4";
            this.label4.Style = "text-align: left";
            this.label4.Text = "Spire Order #:";
            this.label4.Top = 0.09999999F;
            this.label4.Width = 1F;
            // 
            // textBox4
            // 
            this.textBox4.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.textBox4.DataField = "SpireSalesOrderNumber";
            this.textBox4.Height = 0.2F;
            this.textBox4.Left = 9F;
            this.textBox4.Name = "textBox4";
            this.textBox4.Style = "font-weight: bold; text-align: center";
            this.textBox4.Text = null;
            this.textBox4.Top = 0.09999999F;
            this.textBox4.Width = 2F;
            // 
            // txtPhytos
            // 
            this.txtPhytos.Height = 0.2F;
            this.txtPhytos.Left = 1F;
            this.txtPhytos.Name = "txtPhytos";
            this.txtPhytos.OutputFormat = "00000";
            this.txtPhytos.Style = "font-weight: bold";
            this.txtPhytos.Text = "NOTE: THIS ORDER REQUIRES PHYTOS";
            this.txtPhytos.Top = 0.325F;
            this.txtPhytos.Width = 2.824F;
            // 
            // lblSpecialLabels
            // 
            this.lblSpecialLabels.Height = 0.2F;
            this.lblSpecialLabels.HyperLink = null;
            this.lblSpecialLabels.Left = 8F;
            this.lblSpecialLabels.Name = "lblSpecialLabels";
            this.lblSpecialLabels.Style = "";
            this.lblSpecialLabels.Text = "Special Labels:";
            this.lblSpecialLabels.Top = 0.4F;
            this.lblSpecialLabels.Width = 1F;
            // 
            // txtSpecialLabels
            // 
            this.txtSpecialLabels.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtSpecialLabels.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtSpecialLabels.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtSpecialLabels.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtSpecialLabels.Height = 0.2F;
            this.txtSpecialLabels.Left = 9F;
            this.txtSpecialLabels.Name = "txtSpecialLabels";
            this.txtSpecialLabels.Style = "font-weight: bold";
            this.txtSpecialLabels.Text = null;
            this.txtSpecialLabels.Top = 0.4F;
            this.txtSpecialLabels.Width = 0.2F;
            // 
            // txtSpecialLabelDetails
            // 
            this.txtSpecialLabelDetails.DataField = "SpecialLabelDetails";
            this.txtSpecialLabelDetails.Height = 0.2F;
            this.txtSpecialLabelDetails.Left = 9.25F;
            this.txtSpecialLabelDetails.Name = "txtSpecialLabelDetails";
            this.txtSpecialLabelDetails.Style = "font-size: 9pt; font-weight: bold; ddo-char-set: 1";
            this.txtSpecialLabelDetails.Text = "Special Label Details";
            this.txtSpecialLabelDetails.Top = 0.4F;
            this.txtSpecialLabelDetails.Width = 1.75F;
            // 
            // detail
            // 
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.srptItems,
            this.lblCustomer,
            this.txtCustomer,
            this.lblShipTo,
            this.txtShipTo,
            this.llbPONumber,
            this.txtPONumber,
            this.label1,
            this.txtArrivalDate,
            this.label2,
            this.textBox2,
            this.label3,
            this.textBox3,
            this.label6,
            this.textBox6,
            this.label7,
            this.textBox7,
            this.label9,
            this.label10,
            this.label11,
            this.label12,
            this.label13,
            this.label16,
            this.label17,
            this.label18,
            this.label19,
            this.label20,
            this.lblComment,
            this.lblFreight,
            this.txtFreight});
            this.detail.Height = 1F;
            this.detail.Name = "detail";
            // 
            // srptItems
            // 
            this.srptItems.CloseBorder = false;
            this.srptItems.Height = 0.1F;
            this.srptItems.Left = 0F;
            this.srptItems.Name = "srptItems";
            this.srptItems.Report = null;
            this.srptItems.ReportName = "";
            this.srptItems.Top = 0.9F;
            this.srptItems.Width = 11F;
            // 
            // lblCustomer
            // 
            this.lblCustomer.Height = 0.2F;
            this.lblCustomer.HyperLink = null;
            this.lblCustomer.Left = 0F;
            this.lblCustomer.Name = "lblCustomer";
            this.lblCustomer.Style = "";
            this.lblCustomer.Text = "Customer:";
            this.lblCustomer.Top = 0F;
            this.lblCustomer.Width = 1F;
            // 
            // txtCustomer
            // 
            this.txtCustomer.DataField = "CustomerName";
            this.txtCustomer.Height = 0.2F;
            this.txtCustomer.Left = 1F;
            this.txtCustomer.Name = "txtCustomer";
            this.txtCustomer.Style = "font-weight: bold";
            this.txtCustomer.Text = "Customer";
            this.txtCustomer.Top = 0F;
            this.txtCustomer.Width = 3.25F;
            // 
            // lblShipTo
            // 
            this.lblShipTo.Height = 0.2F;
            this.lblShipTo.HyperLink = null;
            this.lblShipTo.Left = 0F;
            this.lblShipTo.Name = "lblShipTo";
            this.lblShipTo.Style = "";
            this.lblShipTo.Text = "Ship To:";
            this.lblShipTo.Top = 0.2F;
            this.lblShipTo.Width = 1F;
            // 
            // txtShipTo
            // 
            this.txtShipTo.DataField = "ShipToName";
            this.txtShipTo.Height = 0.2F;
            this.txtShipTo.Left = 1F;
            this.txtShipTo.Name = "txtShipTo";
            this.txtShipTo.Style = "font-weight: bold";
            this.txtShipTo.Text = "Ship To";
            this.txtShipTo.Top = 0.2F;
            this.txtShipTo.Width = 3.25F;
            // 
            // llbPONumber
            // 
            this.llbPONumber.Height = 0.2F;
            this.llbPONumber.HyperLink = null;
            this.llbPONumber.Left = 0F;
            this.llbPONumber.Name = "llbPONumber";
            this.llbPONumber.Style = "";
            this.llbPONumber.Text = "PO #:";
            this.llbPONumber.Top = 0.4F;
            this.llbPONumber.Width = 1F;
            // 
            // txtPONumber
            // 
            this.txtPONumber.DataField = "CustomerPurchaseOrderNumber";
            this.txtPONumber.Height = 0.2F;
            this.txtPONumber.Left = 1F;
            this.txtPONumber.Name = "txtPONumber";
            this.txtPONumber.Style = "font-weight: bold";
            this.txtPONumber.Text = "PO Number";
            this.txtPONumber.Top = 0.4F;
            this.txtPONumber.Width = 3.25F;
            // 
            // label1
            // 
            this.label1.Height = 0.2F;
            this.label1.HyperLink = null;
            this.label1.Left = 4.25F;
            this.label1.Name = "label1";
            this.label1.Style = "";
            this.label1.Text = "Arrival Date:";
            this.label1.Top = 0F;
            this.label1.Width = 1F;
            // 
            // txtArrivalDate
            // 
            this.txtArrivalDate.DataField = "ArrivalDate";
            this.txtArrivalDate.Height = 0.2F;
            this.txtArrivalDate.Left = 5.25F;
            this.txtArrivalDate.Name = "txtArrivalDate";
            this.txtArrivalDate.Style = "font-weight: bold";
            this.txtArrivalDate.Text = "Arrival Date";
            this.txtArrivalDate.Top = 0F;
            this.txtArrivalDate.Width = 1.5F;
            // 
            // label2
            // 
            this.label2.Height = 0.2F;
            this.label2.HyperLink = null;
            this.label2.Left = 8F;
            this.label2.Name = "label2";
            this.label2.Style = "";
            this.label2.Text = "Load Date:";
            this.label2.Top = 0F;
            this.label2.Width = 1F;
            // 
            // textBox2
            // 
            this.textBox2.DataField = "RequiredDate";
            this.textBox2.Height = 0.2F;
            this.textBox2.Left = 8.999999F;
            this.textBox2.Name = "textBox2";
            this.textBox2.OutputFormat = "MMM d, yyyy";
            this.textBox2.Style = "font-weight: bold";
            this.textBox2.Text = "Required Date";
            this.textBox2.Top = 0F;
            this.textBox2.Width = 2.000002F;
            // 
            // label3
            // 
            this.label3.Height = 0.2F;
            this.label3.HyperLink = null;
            this.label3.Left = 4.25F;
            this.label3.Name = "label3";
            this.label3.Style = "";
            this.label3.Text = "Box Code:";
            this.label3.Top = 0.4F;
            this.label3.Width = 1F;
            // 
            // textBox3
            // 
            this.textBox3.DataField = "BoxCode";
            this.textBox3.Height = 0.2F;
            this.textBox3.Left = 5.25F;
            this.textBox3.Name = "textBox3";
            this.textBox3.Style = "font-weight: bold";
            this.textBox3.Text = "Box Code";
            this.textBox3.Top = 0.4F;
            this.textBox3.Width = 1.5F;
            // 
            // label6
            // 
            this.label6.Height = 0.2F;
            this.label6.HyperLink = null;
            this.label6.Left = 4.25F;
            this.label6.Name = "label6";
            this.label6.Style = "";
            this.label6.Text = "Salesperson:";
            this.label6.Top = 0.2F;
            this.label6.Width = 1F;
            // 
            // textBox6
            // 
            this.textBox6.DataField = "SalespersonName";
            this.textBox6.Height = 0.2F;
            this.textBox6.Left = 5.25F;
            this.textBox6.Name = "textBox6";
            this.textBox6.Style = "font-weight: bold";
            this.textBox6.Text = "Salesperson";
            this.textBox6.Top = 0.2F;
            this.textBox6.Width = 1.5F;
            // 
            // label7
            // 
            this.label7.Height = 0.2F;
            this.label7.HyperLink = null;
            this.label7.Left = 8F;
            this.label7.Name = "label7";
            this.label7.Style = "";
            this.label7.Text = "Truck:";
            this.label7.Top = 0.1999999F;
            this.label7.Width = 1F;
            // 
            // textBox7
            // 
            this.textBox7.DataField = "ShipViaName";
            this.textBox7.Height = 0.2F;
            this.textBox7.Left = 8.999999F;
            this.textBox7.Name = "textBox7";
            this.textBox7.Style = "font-weight: bold";
            this.textBox7.Text = "Truck";
            this.textBox7.Top = 0.1999999F;
            this.textBox7.Width = 2.000002F;
            // 
            // label9
            // 
            this.label9.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label9.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label9.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label9.Height = 0.2F;
            this.label9.HyperLink = null;
            this.label9.Left = 0F;
            this.label9.Name = "label9";
            this.label9.Style = "font-size: 9pt; text-align: center";
            this.label9.Text = "Cases";
            this.label9.Top = 0.7000002F;
            this.label9.Width = 0.5F;
            // 
            // label10
            // 
            this.label10.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label10.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label10.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label10.Height = 0.2F;
            this.label10.HyperLink = null;
            this.label10.Left = 0.4999999F;
            this.label10.Name = "label10";
            this.label10.Style = "font-size: 9pt; text-align: center";
            this.label10.Text = "Description";
            this.label10.Top = 0.7000002F;
            this.label10.Width = 2F;
            // 
            // label11
            // 
            this.label11.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label11.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label11.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label11.Height = 0.2F;
            this.label11.HyperLink = null;
            this.label11.Left = 2.5F;
            this.label11.Name = "label11";
            this.label11.Style = "font-size: 9pt; text-align: center";
            this.label11.Text = "Pack";
            this.label11.Top = 0.7000002F;
            this.label11.Width = 0.5F;
            // 
            // label12
            // 
            this.label12.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label12.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label12.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label12.Height = 0.2F;
            this.label12.HyperLink = null;
            this.label12.Left = 3F;
            this.label12.Name = "label12";
            this.label12.Style = "font-size: 9pt; text-align: center";
            this.label12.Text = "PC";
            this.label12.Top = 0.7000002F;
            this.label12.Width = 1F;
            // 
            // label13
            // 
            this.label13.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label13.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label13.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label13.Height = 0.2F;
            this.label13.HyperLink = null;
            this.label13.Left = 4F;
            this.label13.Name = "label13";
            this.label13.Style = "font-size: 9pt; text-align: center";
            this.label13.Text = "$ Case";
            this.label13.Top = 0.7000002F;
            this.label13.Width = 0.75F;
            // 
            // label16
            // 
            this.label16.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label16.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label16.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label16.Height = 0.2F;
            this.label16.HyperLink = null;
            this.label16.Left = 4.75F;
            this.label16.Name = "label16";
            this.label16.Style = "font-size: 9pt; text-align: center";
            this.label16.Text = "UPC";
            this.label16.Top = 0.7000002F;
            this.label16.Width = 1F;
            // 
            // label17
            // 
            this.label17.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label17.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label17.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label17.Height = 0.2F;
            this.label17.HyperLink = null;
            this.label17.Left = 5.75F;
            this.label17.Name = "label17";
            this.label17.Style = "font-size: 9pt; text-align: center";
            this.label17.Text = "Date Code";
            this.label17.Top = 0.7000002F;
            this.label17.Width = 0.75F;
            // 
            // label18
            // 
            this.label18.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label18.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label18.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label18.Height = 0.2F;
            this.label18.HyperLink = null;
            this.label18.Left = 6.5F;
            this.label18.Name = "label18";
            this.label18.Style = "font-size: 9pt; text-align: center";
            this.label18.Text = "Retail";
            this.label18.Top = 0.7000002F;
            this.label18.Width = 0.75F;
            // 
            // label19
            // 
            this.label19.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label19.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label19.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label19.Height = 0.2F;
            this.label19.HyperLink = null;
            this.label19.Left = 7.25F;
            this.label19.Name = "label19";
            this.label19.Style = "font-size: 9pt; text-align: center";
            this.label19.Text = "W & M\'s";
            this.label19.Top = 0.7000002F;
            this.label19.Width = 0.5F;
            // 
            // label20
            // 
            this.label20.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label20.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label20.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.label20.Height = 0.2F;
            this.label20.HyperLink = null;
            this.label20.Left = 7.75F;
            this.label20.Name = "label20";
            this.label20.Style = "font-size: 9pt; text-align: center";
            this.label20.Text = "Vendor";
            this.label20.Top = 0.7000002F;
            this.label20.Width = 1.5F;
            // 
            // lblComment
            // 
            this.lblComment.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.lblComment.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.lblComment.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.lblComment.Height = 0.2F;
            this.lblComment.HyperLink = null;
            this.lblComment.Left = 9.25F;
            this.lblComment.Name = "lblComment";
            this.lblComment.Style = "font-size: 9pt; text-align: center";
            this.lblComment.Text = "Comment";
            this.lblComment.Top = 0.7000002F;
            this.lblComment.Width = 1.750001F;
            // 
            // lblFreight
            // 
            this.lblFreight.Height = 0.2F;
            this.lblFreight.HyperLink = null;
            this.lblFreight.Left = 8F;
            this.lblFreight.Name = "lblFreight";
            this.lblFreight.Style = "";
            this.lblFreight.Text = "Freight:";
            this.lblFreight.Top = 0.4F;
            this.lblFreight.Width = 1F;
            // 
            // txtFreight
            // 
            this.txtFreight.DataField = "FreightDisplay";
            this.txtFreight.Height = 0.2F;
            this.txtFreight.Left = 8.999999F;
            this.txtFreight.Name = "txtFreight";
            this.txtFreight.Style = "font-size: 10pt; font-weight: bold";
            this.txtFreight.Text = "Freight";
            this.txtFreight.Top = 0.4F;
            this.txtFreight.Width = 2.000002F;
            // 
            // pageFooter
            // 
            this.pageFooter.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.infoPageNumber,
            this.reportInfo1,
            this.lblShipmentDetails,
            this.txtShipmentDetails,
            this.lblInternalNotes,
            this.txtInternalNotes});
            this.pageFooter.Height = 0.6F;
            this.pageFooter.Name = "pageFooter";
            this.pageFooter.Format += new System.EventHandler(this.OnPageFooterFormat);
            // 
            // infoPageNumber
            // 
            this.infoPageNumber.FormatString = "Page {PageNumber} of {PageCount}";
            this.infoPageNumber.Height = 0.2F;
            this.infoPageNumber.Left = 0F;
            this.infoPageNumber.Name = "infoPageNumber";
            this.infoPageNumber.Style = "font-size: 9pt";
            this.infoPageNumber.Top = -7.450581E-09F;
            this.infoPageNumber.Width = 3F;
            // 
            // reportInfo1
            // 
            this.reportInfo1.FormatString = "{RunDateTime:MMM d, yyyy h:mm tt}";
            this.reportInfo1.Height = 0.2F;
            this.reportInfo1.Left = 8F;
            this.reportInfo1.Name = "reportInfo1";
            this.reportInfo1.Style = "font-size: 9pt; text-align: right";
            this.reportInfo1.Top = 0F;
            this.reportInfo1.Width = 3F;
            // 
            // lblShipmentDetails
            // 
            this.lblShipmentDetails.Height = 0.2F;
            this.lblShipmentDetails.HyperLink = null;
            this.lblShipmentDetails.Left = 0F;
            this.lblShipmentDetails.Name = "lblShipmentDetails";
            this.lblShipmentDetails.Style = "";
            this.lblShipmentDetails.Text = "Shipment Details:";
            this.lblShipmentDetails.Top = 0.2F;
            this.lblShipmentDetails.Width = 3.25F;
            // 
            // txtShipmentDetails
            // 
            this.txtShipmentDetails.DataField = "SpireNotes";
            this.txtShipmentDetails.Height = 0.2F;
            this.txtShipmentDetails.Left = 0F;
            this.txtShipmentDetails.Name = "txtShipmentDetails";
            this.txtShipmentDetails.Style = "font-size: 9pt; font-weight: normal";
            this.txtShipmentDetails.Text = "Spire Notes";
            this.txtShipmentDetails.Top = 0.4000001F;
            this.txtShipmentDetails.Width = 3.25F;
            // 
            // lblInternalNotes
            // 
            this.lblInternalNotes.Height = 0.2F;
            this.lblInternalNotes.HyperLink = null;
            this.lblInternalNotes.Left = 7.75F;
            this.lblInternalNotes.Name = "lblInternalNotes";
            this.lblInternalNotes.Style = "";
            this.lblInternalNotes.Text = "Internal Notes:";
            this.lblInternalNotes.Top = 0.2F;
            this.lblInternalNotes.Width = 3.250001F;
            // 
            // txtInternalNotes
            // 
            this.txtInternalNotes.DataField = "InternalNotes";
            this.txtInternalNotes.Height = 0.2F;
            this.txtInternalNotes.Left = 7.75F;
            this.txtInternalNotes.Name = "txtInternalNotes";
            this.txtInternalNotes.Style = "font-size: 9pt; font-weight: normal";
            this.txtInternalNotes.Text = "Internal Notes";
            this.txtInternalNotes.Top = 0.4000001F;
            this.txtInternalNotes.Width = 3.25F;
            // 
            // reportHeader
            // 
            this.reportHeader.Height = 0F;
            this.reportHeader.Name = "reportHeader";
            // 
            // reportFooter
            // 
            this.reportFooter.CanShrink = true;
            this.reportFooter.Height = 0F;
            this.reportFooter.Name = "reportFooter";
            // 
            // futureOrderHeader
            // 
            this.futureOrderHeader.DataField = "Id";
            this.futureOrderHeader.Height = 0F;
            this.futureOrderHeader.Name = "futureOrderHeader";
            // 
            // futureOrderFooter
            // 
            this.futureOrderFooter.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.line2,
            this.txtTotalCases,
            this.txtTotalPrice,
            this.line1,
            this.textBox8});
            this.futureOrderFooter.Height = 0.3341852F;
            this.futureOrderFooter.Name = "futureOrderFooter";
            // 
            // line2
            // 
            this.line2.Height = 0F;
            this.line2.Left = 0F;
            this.line2.LineWeight = 1F;
            this.line2.Name = "line2";
            this.line2.Top = 0F;
            this.line2.Width = 11F;
            this.line2.X1 = 0F;
            this.line2.X2 = 11F;
            this.line2.Y1 = 0F;
            this.line2.Y2 = 0F;
            // 
            // txtTotalCases
            // 
            this.txtTotalCases.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtTotalCases.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtTotalCases.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtTotalCases.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtTotalCases.DataField = "TotalOrderQuantity";
            this.txtTotalCases.Height = 0.2F;
            this.txtTotalCases.Left = 0F;
            this.txtTotalCases.Name = "txtTotalCases";
            this.txtTotalCases.OutputFormat = "#,##0";
            this.txtTotalCases.Style = "font-size: 9pt; font-weight: bold; text-align: center";
            this.txtTotalCases.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.GrandTotal;
            this.txtTotalCases.Text = "Cases";
            this.txtTotalCases.Top = 0F;
            this.txtTotalCases.Width = 0.5F;
            // 
            // txtTotalPrice
            // 
            this.txtTotalPrice.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtTotalPrice.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtTotalPrice.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtTotalPrice.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtTotalPrice.DataField = "TotalPrice";
            this.txtTotalPrice.Height = 0.2F;
            this.txtTotalPrice.Left = 4F;
            this.txtTotalPrice.Name = "txtTotalPrice";
            this.txtTotalPrice.OutputFormat = "$#,##0.00";
            this.txtTotalPrice.Padding = new GrapeCity.ActiveReports.PaddingEx(0, 0, 5, 0);
            this.txtTotalPrice.Style = "font-size: 9pt; font-weight: bold; text-align: right";
            this.txtTotalPrice.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.GrandTotal;
            this.txtTotalPrice.Text = "$";
            this.txtTotalPrice.Top = 0F;
            this.txtTotalPrice.Width = 0.75F;
            // 
            // line1
            // 
            this.line1.Height = 0F;
            this.line1.Left = 0F;
            this.line1.LineWeight = 2F;
            this.line1.Name = "line1";
            this.line1.Top = 0F;
            this.line1.Width = 11F;
            this.line1.X1 = 0F;
            this.line1.X2 = 11F;
            this.line1.Y1 = 0F;
            this.line1.Y2 = 0F;
            // 
            // textBox8
            // 
            this.textBox8.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.textBox8.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.textBox8.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.textBox8.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.textBox8.Height = 0.2F;
            this.textBox8.Left = 0.5F;
            this.textBox8.Name = "textBox8";
            this.textBox8.OutputFormat = "#,##0";
            this.textBox8.Padding = new GrapeCity.ActiveReports.PaddingEx(5, 0, 0, 0);
            this.textBox8.Style = "font-size: 9pt; font-weight: bold; text-align: left";
            this.textBox8.Text = "Total";
            this.textBox8.Top = 0F;
            this.textBox8.Width = 2F;
            // 
            // FutureOrderSummary
            // 
            this.MasterReport = false;
            this.CompatibilityMode = GrapeCity.ActiveReports.Document.CompatibilityModes.CrossPlatform;
            this.PageSettings.Margins.Bottom = 0.5F;
            this.PageSettings.Margins.Left = 0.5F;
            this.PageSettings.Margins.Right = 0.5F;
            this.PageSettings.Margins.Top = 0.5F;
            this.PageSettings.Orientation = GrapeCity.ActiveReports.Document.Section.PageOrientation.Landscape;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 11F;
            this.Sections.Add(this.reportHeader);
            this.Sections.Add(this.pageHeader);
            this.Sections.Add(this.futureOrderHeader);
            this.Sections.Add(this.detail);
            this.Sections.Add(this.futureOrderFooter);
            this.Sections.Add(this.pageFooter);
            this.Sections.Add(this.reportFooter);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; color: Black; fon" +
            "t-family: \"Calibri\"; font-size: 11pt; ddo-char-set: 186", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-weight: bold; ddo-char-set: 186; font-size: 24pt", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: italic; font-family: \"Calibri\"; d" +
            "do-char-set: 186", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 186", "Heading3", "Normal"));
            ((System.ComponentModel.ISupportInitialize)(this.picLogo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFutureOrderNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFutureOrderNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhytos)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblSpecialLabels)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSpecialLabels)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSpecialLabelDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblCustomer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblShipTo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShipTo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.llbPONumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPONumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtArrivalDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblComment)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFreight)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFreight)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.infoPageNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.reportInfo1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblShipmentDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShipmentDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblInternalNotes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInternalNotes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTotalCases)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTotalPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion

        private GrapeCity.ActiveReports.SectionReportModel.Picture picLogo;
        private GrapeCity.ActiveReports.SectionReportModel.ReportInfo infoPageNumber;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblCustomer;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtCustomer;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtFutureOrderNumber;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblFutureOrderNumber;
        private GrapeCity.ActiveReports.SectionReportModel.ReportHeader reportHeader;
        private GrapeCity.ActiveReports.SectionReportModel.ReportFooter reportFooter;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport srptItems;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblShipTo;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtShipTo;
        private GrapeCity.ActiveReports.SectionReportModel.Label llbPONumber;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPONumber;
        private GrapeCity.ActiveReports.SectionReportModel.Label label1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtArrivalDate;
        private GrapeCity.ActiveReports.SectionReportModel.Label label2;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label3;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox3;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox4;
        private GrapeCity.ActiveReports.SectionReportModel.Label label4;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblSpecialLabels;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSpecialLabels;
        private GrapeCity.ActiveReports.SectionReportModel.Label label6;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox6;
        private GrapeCity.ActiveReports.SectionReportModel.Label label7;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox7;
        private GrapeCity.ActiveReports.SectionReportModel.Label label9;
        private GrapeCity.ActiveReports.SectionReportModel.Label label10;
        private GrapeCity.ActiveReports.SectionReportModel.Label label11;
        private GrapeCity.ActiveReports.SectionReportModel.Label label12;
        private GrapeCity.ActiveReports.SectionReportModel.Label label13;
        private GrapeCity.ActiveReports.SectionReportModel.Label label16;
        private GrapeCity.ActiveReports.SectionReportModel.Label label18;
        private GrapeCity.ActiveReports.SectionReportModel.Label label19;
        private GrapeCity.ActiveReports.SectionReportModel.Label label20;
        private GrapeCity.ActiveReports.SectionReportModel.Label label17;
        private GrapeCity.ActiveReports.SectionReportModel.ReportInfo reportInfo1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPhytos;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblComment;
        private GrapeCity.ActiveReports.SectionReportModel.GroupHeader futureOrderHeader;
        private GrapeCity.ActiveReports.SectionReportModel.GroupFooter futureOrderFooter;
        private GrapeCity.ActiveReports.SectionReportModel.Line line2;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtTotalCases;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtTotalPrice;
        private GrapeCity.ActiveReports.SectionReportModel.Line line1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox8;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblShipmentDetails;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtShipmentDetails;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblInternalNotes;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtInternalNotes;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblFreight;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtFreight;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSpecialLabelDetails;
    }
}
