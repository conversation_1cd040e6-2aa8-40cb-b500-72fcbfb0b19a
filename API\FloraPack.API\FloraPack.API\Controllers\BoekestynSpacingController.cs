﻿using System.Text;
using FloraPack.API.Reports.Boekestyns;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.Boekestyn.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Boekestyns/Spacing")]
public class BoekestynSpacingController(IConfiguration configuration, SpacingRepository spacingRepository, CouchRepository couchRepository) : FloraPackControllerBase(configuration)
{
    [HttpGet("")]
    public async Task<IActionResult> Spacing()
    {
        var lines = await spacingRepository.GetSpacingLines();

        var response = new SpacingResponse(lines);

        return Ok(response);
    }

    [HttpGet("Orders")]
    public async Task<IActionResult> SpacingOrders([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var plants = (await couchRepository.GetPlants()).ToDictionary(p => p.Id);
        var spacingOrders = await couchRepository.GetSpacingOrders(startDate, endDate);
        var orders = spacingOrders
            .Where(o => o.HasSpacing)
            .ToList();

        var ids = orders
            .Select(o => o.Id)
            .ToArray();

        var history = (await spacingRepository.GetSpacingWorkOrderHistory(ids)).ToDictionary(h => h.OrderId);
        
        foreach (var order in orders) {
            if (history.ContainsKey(order.Id)) {
                order.PotsPartiallySpaced = history[order.Id].PotsPartiallySpaced;
                order.PotsFullySpaced = history[order.Id].PotsFullySpaced;
            }

            if (plants.TryGetValue(order.Plant.Id, out var plant)) {
                order.Plant.Name = plant.Name;
                order.Plant.Abbreviation = plant.Abbreviation;
                order.Plant.Crop = plant.Crop;
                order.Plant.Size = plant.Size;
                order.Plant.CuttingsPerPot = plant.CuttingsPerPot ?? 0;
                order.Plant.CuttingsPerTableTight = plant.CuttingsPerTableTight ?? 0;
                order.Plant.CuttingsPerTableSpaced = plant.CuttingsPerTableSpaced ?? 0;
                order.Plant.CuttingsPerTablePartiallySpaced = plant.CuttingsPerTablePartiallySpaced ?? 0;
                order.Plant.PotsPerCase = plant.PotsPerCase ?? 0;
                order.Plant.StickingCuttingsPerHour = plant.StickingCuttingsPerHour ?? 0;
                order.Plant.SpacingPotsPerHour = plant.SpacingPotsPerHour ?? 0;
                order.Plant.PackingCasesPerHour = plant.PackingCasesPerHour ?? 0;
                order.Plant.Colour = plant.Colour ?? string.Empty;
                order.Plant.HasPinching = plant.HasPinching;
                order.Plant.DaysToPinch = plant.DaysToPinch;
            }
        }

        var response = new SpacingOrderResponse(orders);

        return Ok(response);
    }

    [HttpGet("Schedules")]
    public async Task<IActionResult> SpacingSchedules([FromQuery] DateTime date)
    {
        var schedules = await spacingRepository.GetSpacingSchedules(date);

        var response = new SpacingSchedulesResponse(schedules);

        return Ok(response);
    }

    [HttpGet("WorkOrders")]
    public async Task<IActionResult> GetSpacingWorkOrders([FromQuery] DateTime date)
    {
        var workOrders = await spacingRepository.GetSpacingWorkOrders(date);
        var response = new SpacingWorkOrdersResponse(workOrders);
        return Ok(response);
    }

    [HttpPost("WorkOrders")]
    public async Task<IActionResult> AddWorkOrderToSpacingSchedule([FromBody] AddWorkOrderToSpacingScheduleModel model)
    {
        await spacingRepository.AddSpacingOrderToSpacingSchedule(model.CrewSize,
            model.PotsToSpace,
            model.FromSpaceType,
            model.ToSpaceType,
            model.RequiresPinching,
            model.RobotProgram,
            model.Comments,
            model.Schedule, model.Order);
        return Ok();
    }

    [HttpPost("WorkOrders/Sort")]
    public async Task<IActionResult> SortSpacingWorkOrders([FromBody] SortSpacingWorkOrdersModel model)
    {
        await spacingRepository.SortSpacingWorkOrders(model.WorkOrders);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Comment")]
    public async Task<IActionResult> UpdateComment(int id, [FromBody] UpdateCommentModel model)
    {
        await spacingRepository.UpdateSpacingWorkOrderComment(id, model.Comment);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Remove")]
    public async Task<IActionResult> DeleteSpacingWorkOrder(int id, [FromBody] DeleteSpacingWorkOrderModel model)
    {
        await spacingRepository.DeleteSpacingWorkOrder(id);

        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Start")]
    public async Task<IActionResult> StartSpacingWorkOrderLabour(int id, [FromBody] StartSpacingWorkOrderLabourModel model)
    {
        await spacingRepository.StartSpacingLabour(id, model.CrewSize);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Pause")]
    public async Task<IActionResult> PauseSpacingWorkOrderLabour(int id, [FromBody] StopSpacingWorkOrderLabourModel model)
    {
        await spacingRepository.StopSpacingLabour(id, model.CrewSize, model.Comments);

        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Stop")]
    public async Task<IActionResult> StopSpacingWorkOrderLabour(int id, [FromBody] StopSpacingWorkOrderLabourModel model)
    {
        await spacingRepository.StopSpacingLabour(id, model.CrewSize, model.Comments, true);

        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("Labour/Download")]
    public async Task<IActionResult> DownloadLabour([FromQuery] DateTime start, [FromQuery] DateTime end)
    {
        var items = await spacingRepository.GetSpacingLabourReportItems(start, end);
        var report = SpacingLabourReportFactory.CreateReport(start, end, items);
        Response.Headers.Append("Content-Disposition", "attachment;filename=SpacingLabour.xlsx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    private record SpacingResponse(IEnumerable<SpacingLine> Lines);

    private record SpacingOrderResponse(IEnumerable<SpacingOrder> Orders);

    private record SpacingSchedulesResponse(IEnumerable<SpacingSchedule> Schedules);

    private record SpacingWorkOrdersResponse(IEnumerable<SpacingWorkOrderItem> Orders);

    public class AddWorkOrderToSpacingScheduleModel
    {
        public int CrewSize { get; set; }
        public int PotsToSpace { get; set; }
        public string FromSpaceType { get; set; } = "Tight";
        public string ToSpaceType { get; set; } = "Full";
        public bool RequiresPinching { get; set; }
        public string RobotProgram { get; set; } = string.Empty;
        public string? Comments { get; set; }
        public SpacingSchedule Schedule { get; set; } = new();
        public SpacingOrder Order { get; set; } = new();
    }

    public class DeleteSpacingWorkOrderModel
    {
        public string OrderId { get; set; } = string.Empty;
    }

    public class StartSpacingWorkOrderLabourModel
    {
        public int CrewSize { get; set; }
    }

    public class StopSpacingWorkOrderLabourModel
    {
        public int CrewSize { get; set; }
        public string? Comments { get; set; }
    }

    public class SortSpacingWorkOrdersModel
    {
        public List<SpacingRepository.SpacingWorkOrderSort> WorkOrders { get; init; } = [];
    }

    public class UpdateCommentModel
    {
        public string? Comment { get; init; }
    }

    public class UpdateCrewSizeModel
    {
        public int CrewSize { get; init; }
    }
}
