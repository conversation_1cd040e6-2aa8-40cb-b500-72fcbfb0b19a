﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class BoekestynProductionOrder
{
    [JsonPropertyName("_id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("_rev")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Revision { get; set; }

    [JsonPropertyName("type")]
    public string Type => "order";

    [JsonPropertyName("cases")]
    public int Cases { get; set; }

    [JsonPropertyName("flowerDate")]
    public string? FlowerDate { get; set; }

    public ProductionOrderCustomer? Customer { get; set; }

    public Plant? Plant { get; set; }

    public Zone? FullSpaceZone { get; set; }

    public List<SalesWeek>? SalesWeeks { get; set; }

    public class ProductionOrderCustomer
    {
        [JsonPropertyName("_id")]
        public string Id { get; set; } = string.Empty;
        public string Abbreviation { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class SalesWeek
    {
        public string Id { get; set; } = string.Empty;
        public string Week { get; set; } = string.Empty;
        public int Cases { get; set; }
    }
}