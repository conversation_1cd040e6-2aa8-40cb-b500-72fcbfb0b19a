﻿namespace FloraPack.API.Repositories.Prebooks;

public class PrebookEmail : IPendingPrebookEmail
{
    public PrebookEmail()
    {
        PrebookDate = DateTime.Today;
    }

    public PrebookEmail(PendingPrebookEmail pending)
    {
        PrebookId = pending.PrebookId;
        VendorName = pending.VendorName;
        PrebookDate = pending.PrebookDate;
        PreviousPrebookDate = pending.PreviousPrebookDate;
        RequiredDate = pending.RequiredDate;
        Comments = pending.Comments;
        Items = pending.Items.Select(i => new PrebookEmailItem(i)).ToList();
    }

    public int Id { get; set; }
    public int PrebookId { get; set; }
    public string VendorName { get; set; } = string.Empty;
    public DateTime PrebookDate { get; set; }
    public DateTime? PreviousPrebookDate { get; set; }
    public DateTime? RequiredDate { get; set; }
    public string? Comments { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string To { get; set; } = string.Empty;
    public string? Cc { get; set; }
    public string? Bcc { get; set; }
    public string From { get; set; } = string.Empty;

    public List<PrebookEmailItem> Items { get; set; } = new();
}

public class PrebookEmailItem : PendingPrebookEmailItem
{
    public PrebookEmailItem()
    {

    }

    public PrebookEmailItem(PendingPrebookEmailItem pending)
    {
        PrebookItemId = pending.PrebookItemId;
        SpirePartNumber = pending.SpirePartNumber;
        Description = pending.Description;
        OrderQuantity = pending.OrderQuantity;
        PreviousOrderQuantity = pending.PreviousOrderQuantity;
        PackQuantity = pending.PackQuantity;
        BoxCode = pending.BoxCode;
        DateCode = pending.DateCode;
        PotCover = pending.PotCover;
        Upc = pending.Upc;
        WeightsAndMeasures = pending.WeightsAndMeasures;
        Retail = pending.Retail;
        Comments = pending.Comments;
    }

    public int Id { get; set; }
    public int PrebookEmailId { get; set; }
}