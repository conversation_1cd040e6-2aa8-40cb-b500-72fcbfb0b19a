﻿namespace FloraPack.API.Repositories.Holidays;

public class HolidayRepository
{
    public async Task<List<Holiday>> GetHolidays(int year)
    {
        var client = new HttpClient();
        var response = await client.GetFromJsonAsync<HolidayResponse>($"https://canada-holidays.ca/api/v1/provinces/ON?year={year}");
        if (response == null) {
            throw new ApplicationException("Could not retrieve holidays");
        }

        return response.Province.Holidays;
    }
}