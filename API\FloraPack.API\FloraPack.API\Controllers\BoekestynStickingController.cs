﻿using FloraPack.API.Reports.Boekestyns;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.Boekestyn.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Boekestyns/Sticking")]
public class BoekestynStickingController(IConfiguration configuration, StickingRepository stickingRepository, CouchRepository couchRepository) : FloraPackControllerBase(configuration)
{
    [HttpGet("")]
    public async Task<IActionResult> Sticking()
    {
        var lines = await stickingRepository.GetStickingLines();

        var response = new StickingResponse(lines);

        return Ok(response);
    }

    [HttpGet("Orders")]
    public async Task<IActionResult> StickingOrders([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var plants = (await couchRepository.GetPlants()).ToDictionary(p => p.Id);
        var stickingOrders = await couchRepository.GetStickingOrders(startDate, endDate);
        var orders = stickingOrders
            .Where(o => string.Equals(o.StickZone.Name, "P3", StringComparison.InvariantCultureIgnoreCase))
            .ToList();

        var ids = orders
            .Select(o => o.Id)
            .ToArray();

        var history = (await stickingRepository.GetStickingWorkOrderHistory(ids)).ToDictionary(h => h.OrderId);

        orders.ForEach(order => {
            order.StickingScheduled = history.ContainsKey(order.Id);

            if (plants.TryGetValue(order.Plant.Id, out var plant)) {
                order.Plant.Name = plant.Name;
                order.Plant.Abbreviation = plant.Abbreviation;
                order.Plant.Crop = plant.Crop;
                order.Plant.Size = plant.Size;
                order.Plant.CuttingsPerPot = plant.CuttingsPerPot ?? 0;
                order.Plant.CuttingsPerTableTight = plant.CuttingsPerTableTight ?? 0;
                order.Plant.CuttingsPerTableSpaced = plant.CuttingsPerTableSpaced ?? 0;
                order.Plant.CuttingsPerTablePartiallySpaced = plant.CuttingsPerTablePartiallySpaced ?? 0;
                order.Plant.PotsPerCase = plant.PotsPerCase ?? 0;
                order.Plant.StickingCuttingsPerHour = plant.StickingCuttingsPerHour ?? 0;
                order.Plant.SpacingPotsPerHour = plant.SpacingPotsPerHour ?? 0;
                order.Plant.PackingCasesPerHour = plant.PackingCasesPerHour ?? 0;
                order.Plant.Colour = plant.Colour ?? string.Empty;
                order.Plant.HasPinching = plant.HasPinching;
                order.Plant.DaysToPinch = plant.DaysToPinch;
                order.Plant.DefaultStickingCrewSize = plant.DefaultStickingCrewSize;
            }
        });

        orders = orders
            .OrderBy(o => o.StickingScheduled ? 1 : 0)
            .ThenBy(o => o.StickDate)
            .ThenBy(o => o.OrderNumber)
            .ToList();

        var response = new StickingOrderResponse(orders);

        return Ok(response);
    }

    [HttpGet("Schedules")]
    public async Task<IActionResult> StickingSchedules([FromQuery] DateTime date)
    {
        var schedules = await stickingRepository.GetStickingSchedules(date);

        var response = new StickingSchedulesResponse(schedules);

        return Ok(response);
    }

    [HttpGet("WorkOrders")]
    public async Task<IActionResult> GetStickingWorkOrders([FromQuery] DateTime date)
    {
        var workOrders = await stickingRepository.GetStickingWorkOrders(date);
        var response = new StickingWorkOrdersResponse(workOrders);
        return Ok(response);
    }

    [HttpPost("WorkOrders")]
    public async Task<IActionResult> AddWorkOrderToStickingSchedule([FromBody] AddWorkOrderToStickingScheduleModel model)
    {
        await stickingRepository.AddStickingOrderToStickingSchedule(model.Schedule, model.Order);
        await couchRepository.MarkOrderAsStickingScheduled(model.Order.Id, true);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}")]
    public async Task<IActionResult> UpdateStickingWorkOrder(int id, [FromBody] StickingWorkOrderUpdateModel model)
    {
        await stickingRepository.UpdateStickingWorkOrder(id, model);
        return Ok();
    }

    [HttpPost("WorkOrders/Sort")]
    public async Task<IActionResult> SortStickingWorkOrders([FromBody] SortStickingWorkOrdersModel model)
    {
        await stickingRepository.SortStickingWorkOrders(model.WorkOrders);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Comment")]
    public async Task<IActionResult> UpdateComment(int id, [FromBody] UpdateCommentModel model)
    {
        await stickingRepository.UpdateStickingWorkOrderComment(id, model.Comment);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/CrewSize")]
    public async Task<IActionResult> UpdateCrewSize(int id, [FromBody] UpdateCrewSizeModel model)
    {
        await stickingRepository.UpdateStickingWorkOrderCrewSize(id, model.CrewSize);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Remove")]
    public async Task<IActionResult> DeleteStickingWorkOrder(int id, [FromBody] DeleteStickingWorkOrderModel model)
    {
        await stickingRepository.DeleteStickingWorkOrder(id);
        await couchRepository.MarkOrderAsStickingScheduled(model.OrderId, false);

        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Start")]
    public async Task<IActionResult> StartStickingWorkOrderLabour(int id, [FromBody] StartStickingWorkOrderLabourModel model)
    {
        await stickingRepository.StartStickingLabour(id, model.CrewSize);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Pause")]
    public async Task<IActionResult> PauseStickingWorkOrderLabour(int id, [FromBody] PauseStickingWorkOrderLabourModel model)
    {
        await stickingRepository.StopStickingLabour(id, model.CrewSize, model.Comments);

        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Stop")]
    public async Task<IActionResult> StopStickingWorkOrderLabour(int id, [FromBody] StopStickingWorkOrderLabourModel model)
    {

        await stickingRepository.StopStickingLabour(id, model.CrewSize, model.Comments, true, model.VarietyQuantities);

        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("Labour/Download")]
    public async Task<IActionResult> DownloadLabour([FromQuery] DateTime start, [FromQuery] DateTime end)
    {
        var items = await stickingRepository.GetStickingLabourReportItems(start, end);
        var report = StickingLabourReportFactory.CreateReport(start, end, items);
        Response.Headers.Append("Content-Disposition", "attachment;filename=StickingLabour.xlsx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    private record StickingResponse(IEnumerable<StickingLine> Lines);

    private record StickingOrderResponse(IEnumerable<StickingOrder> Orders);

    private record StickingSchedulesResponse(IEnumerable<StickingSchedule> Schedules);

    private record StickingWorkOrdersResponse(IEnumerable<StickingWorkOrderItem> Orders);

    public class AddWorkOrderToStickingScheduleModel
    {
        public StickingSchedule Schedule { get; set; } = new();
        public StickingOrder Order { get; set; } = new();
    }

    public class StickingWorkOrderUpdateModel
    {
        public int CrewSize { get; set; }
        public DateTime FlowerDate { get; set; }
        public List<StickingWorkOrderVariety> Varieties { get; set; } = [];
    }

    public class DeleteStickingWorkOrderModel
    {
        public string OrderId { get; set; } = string.Empty;
    }

    public class StartStickingWorkOrderLabourModel
    {
        public int CrewSize { get; set; }
    }

    public class PauseStickingWorkOrderLabourModel
    {
        public int CrewSize { get; set; }
        public string? Comments { get; set; }
    }

    public class StopStickingWorkOrderLabourModel
    {
        public int CrewSize { get; set; }
        public string? Comments { get; set; }
        public List<LabourVarietyQuantity> VarietyQuantities { get; set; } = [];

    }

    public class LabourVarietyQuantity
    {
        public int Id { get; init; }
        public int Quantity { get; init; }
    }

    public class SortStickingWorkOrdersModel
    {
        public List<StickingRepository.StickingWorkOrderSort> WorkOrders { get; init; } = [];
    }

    public class UpdateCommentModel
    {
        public string? Comment { get; init; }
    }

    public class UpdateCrewSizeModel
    {
        public int CrewSize { get; init; }
    }
}