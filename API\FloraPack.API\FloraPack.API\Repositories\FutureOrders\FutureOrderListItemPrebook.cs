﻿namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderListItemPrebook
{
    public int Id { get; set; }
    public int FutureOrderId { get; set; }
    public string Vendor { get; set; } = string.Empty;
    public long CaseCount { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime Modified { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime? Sent { get; set; }
    public string? SentBy { get; set; }
    public DateTime? Confirmed { get; set; }
    public string? ConfirmedBy { get; set; }
}