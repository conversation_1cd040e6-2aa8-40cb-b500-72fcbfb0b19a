﻿namespace FloraPack.API.Repositories.Settings;

public class ProductDefaultBoekestynProductOverride
{
    public int Id { get; set; }
    public int SpireInventoryId { get; set; }
    public int StartWeek { get; set; }
    public int EndWeek { get; set; }
    public string BoekestynPlantId { get; set; } = string.Empty;
    public string? BoekestynCustomerAbbreviation { get; set; }
    public int QuantityPerFinishedItem { get; set; }
}