namespace FloraPack.API.Reports
{
    /// <summary>
    /// Summary description for HeinensLabels.
    /// </summary>
    partial class HeinensLabels
    {
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing) {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Resources.ResourceManager resources = new System.Resources.ResourceManager(typeof(HeinensLabels));
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.txtDescription = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtLabelCount = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtStoreName = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtStoreNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPartNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtDeliveryDate = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPurchaseOrderNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtFloraPack = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtUPC = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLabelCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPartNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDeliveryDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseOrderNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFloraPack)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUPC)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // detail
            // 
            this.detail.CanGrow = false;
            this.detail.ColumnCount = 2;
            this.detail.ColumnSpacing = 0.5F;
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.txtDescription,
            this.txtLabelCount,
            this.txtStoreName,
            this.txtStoreNumber,
            this.txtPartNumber,
            this.txtDeliveryDate,
            this.txtPurchaseOrderNumber,
            this.txtFloraPack,
            this.txtUPC});
            this.detail.Height = 3.45F;
            this.detail.KeepTogether = true;
            this.detail.Name = "detail";
            this.detail.RepeatToFill = true;
            // 
            // txtDescription
            // 
            this.txtDescription.CanGrow = false;
            this.txtDescription.DataField = "DescriptionDisplay";
            this.txtDescription.Height = 1F;
            this.txtDescription.Left = 0F;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Style = "font-size: 20pt; font-weight: normal; text-align: center; vertical-align: top";
            this.txtDescription.Text = "Description";
            this.txtDescription.Top = 1.554F;
            this.txtDescription.Width = 3.5F;
            // 
            // txtLabelCount
            // 
            this.txtLabelCount.DataField = "LabelNumberDisplay";
            this.txtLabelCount.Height = 0.2709999F;
            this.txtLabelCount.HyperLink = null;
            this.txtLabelCount.Left = 2.196F;
            this.txtLabelCount.Name = "txtLabelCount";
            this.txtLabelCount.Style = "color: Black; font-size: 10pt; font-weight: normal; text-align: right; vertical-a" +
    "lign: middle; ddo-font-vertical: none";
            this.txtLabelCount.Text = "X of Y";
            this.txtLabelCount.Top = 3.179F;
            this.txtLabelCount.Width = 1.2F;
            // 
            // txtStoreName
            // 
            this.txtStoreName.CanGrow = false;
            this.txtStoreName.Height = 0.2F;
            this.txtStoreName.Left = 0F;
            this.txtStoreName.Name = "txtStoreName";
            this.txtStoreName.Style = "font-size: 12pt; font-weight: bold; text-align: left";
            this.txtStoreName.Text = "Heinen";
            this.txtStoreName.Top = 0.404F;
            this.txtStoreName.Width = 0.85F;
            // 
            // txtStoreNumber
            // 
            this.txtStoreNumber.CanGrow = false;
            this.txtStoreNumber.DataField = "StoreNumber";
            this.txtStoreNumber.Height = 0.58F;
            this.txtStoreNumber.Left = 0F;
            this.txtStoreNumber.Name = "txtStoreNumber";
            this.txtStoreNumber.Style = "font-size: 32pt; font-weight: normal; text-align: left; vertical-align: bottom";
            this.txtStoreNumber.Text = "Store #";
            this.txtStoreNumber.Top = 0.604F;
            this.txtStoreNumber.Width = 0.85F;
            // 
            // txtPartNumber
            // 
            this.txtPartNumber.CanGrow = false;
            this.txtPartNumber.DataField = "ProductNumberDisplay";
            this.txtPartNumber.Height = 0.625F;
            this.txtPartNumber.Left = 0F;
            this.txtPartNumber.Name = "txtPartNumber";
            this.txtPartNumber.Style = "font-size: 20pt; font-weight: bold; text-align: center; vertical-align: middle";
            this.txtPartNumber.Text = "Product Number";
            this.txtPartNumber.Top = 2.554F;
            this.txtPartNumber.Width = 3.5F;
            // 
            // txtDeliveryDate
            // 
            this.txtDeliveryDate.CanGrow = false;
            this.txtDeliveryDate.DataField = "ReceivingDate";
            this.txtDeliveryDate.Height = 0.3F;
            this.txtDeliveryDate.Left = 2.65F;
            this.txtDeliveryDate.Name = "txtDeliveryDate";
            this.txtDeliveryDate.OutputFormat = "yyyy-MM-dd";
            this.txtDeliveryDate.Style = "font-size: 12pt; font-weight: bold; text-align: center; vertical-align: middle";
            this.txtDeliveryDate.Text = "Delivery Date";
            this.txtDeliveryDate.Top = 0.604F;
            this.txtDeliveryDate.Width = 0.85F;
            // 
            // txtPurchaseOrderNumber
            // 
            this.txtPurchaseOrderNumber.CanGrow = false;
            this.txtPurchaseOrderNumber.DataField = "PurchaseOrderNumberDisplay";
            this.txtPurchaseOrderNumber.Height = 0.3F;
            this.txtPurchaseOrderNumber.Left = 0F;
            this.txtPurchaseOrderNumber.Name = "txtPurchaseOrderNumber";
            this.txtPurchaseOrderNumber.Style = "font-size: 12pt; font-weight: bold; text-align: center; vertical-align: middle";
            this.txtPurchaseOrderNumber.Text = "Purchase Order Number";
            this.txtPurchaseOrderNumber.Top = 1.254F;
            this.txtPurchaseOrderNumber.Width = 3.5F;
            // 
            // txtFloraPack
            // 
            this.txtFloraPack.CanGrow = false;
            this.txtFloraPack.Height = 0.2F;
            this.txtFloraPack.Left = 2.65F;
            this.txtFloraPack.Name = "txtFloraPack";
            this.txtFloraPack.OutputFormat = "yyyy-MM-dd";
            this.txtFloraPack.Style = "font-size: 12pt; font-weight: bold; text-align: center; vertical-align: middle";
            this.txtFloraPack.Text = "Flora Pack";
            this.txtFloraPack.Top = 0.404F;
            this.txtFloraPack.Width = 0.85F;
            // 
            // txtUPC
            // 
            this.txtUPC.CanGrow = false;
            this.txtUPC.DataField = "UPC";
            this.txtUPC.Height = 0.78F;
            this.txtUPC.Left = 0.8500001F;
            this.txtUPC.Name = "txtUPC";
            this.txtUPC.Style = "font-family: UPC-A; font-size: 60pt; font-weight: normal; text-align: center; ver" +
    "tical-align: bottom";
            this.txtUPC.Text = "UPC";
            this.txtUPC.Top = 0.404F;
            this.txtUPC.Width = 1.8F;
            // 
            // HeinensLabels
            // 
            this.MasterReport = false;
            this.CompatibilityMode = GrapeCity.ActiveReports.Document.CompatibilityModes.CrossPlatform;
            this.PageSettings.DefaultPaperSize = false;
            this.PageSettings.Margins.Bottom = 0.25F;
            this.PageSettings.Margins.Left = 0.5F;
            this.PageSettings.Margins.Right = 0.5F;
            this.PageSettings.Margins.Top = 0.25F;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 8F;
            this.Sections.Add(this.detail);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; color: Black; fon" +
            "t-family: \"Calibri\"; font-size: 11pt; ddo-char-set: 186", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-weight: bold; ddo-char-set: 186; font-size: 24pt", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: italic; font-family: \"Calibri\"; d" +
            "do-char-set: 186", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 186", "Heading3", "Normal"));
            this.ReportStart += new System.EventHandler(this.OnReportStart);
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLabelCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPartNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDeliveryDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseOrderNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFloraPack)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUPC)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion

        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDescription;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtStoreNumber;
        private GrapeCity.ActiveReports.SectionReportModel.Label txtLabelCount;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtStoreName;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPartNumber;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDeliveryDate;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPurchaseOrderNumber;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtFloraPack;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtUPC;
    }
}
