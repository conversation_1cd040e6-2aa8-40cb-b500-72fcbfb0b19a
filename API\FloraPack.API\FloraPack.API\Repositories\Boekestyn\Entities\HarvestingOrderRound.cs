﻿namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class HarvestingOrderRound
{
    public int WorkOrderId { get; init; }
    public string OrderId { get; init; } = string.Empty;
    public int RoundNumber { get; init; }
    public int Pots { get; init; }
    public int Cases { get; init; }
    public string Date { get; init; } = string.Empty;
    public string? NextHarvestDate { get; init; }

    public List<HarvestingOrderRoundVariety> Varieties { get; set; } = [];
}