﻿using System.Text.RegularExpressions;

namespace FloraPack.API.Utilities;

public static class PackQuantityParser
{
    public static int? Parse(string? description)
    {
        var packQuantityRegex = new Regex(@"[xX] *(\d+)$");
        int? packQuantity = default;
        var match = packQuantityRegex.Match(description ?? string.Empty);
        if (match.Success && match.Groups.Count > 1) {
            var group = match.Groups[1].Value;
            if (int.TryParse(group, out var qty)) {
                packQuantity = qty;
            }
        }

        return packQuantity;
    }
}