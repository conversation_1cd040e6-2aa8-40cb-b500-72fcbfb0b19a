﻿using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using Dapper;
using FloraPack.API.Repositories;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Repositories.Prebooks;

namespace FloraPack.API.Spire;

public class SpireRepository(IHttpClientFactory httpClientFactory, IConfiguration configuration)
    : RepositoryBase(configuration)
{
    private readonly HttpClient _httpClient = httpClientFactory.CreateClient("Spire");

    private HttpClient GetSpireHttpClient() => _httpClient;

    private JsonSerializerOptions JsonSerializerOptions => new() {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        DictionaryKeyPolicy = JsonNamingPolicy.CamelCase
    };

    public async Task<IEnumerable<InventoryItem>> GetInventoryItems(string? search = null)
    {
        var client = GetSpireHttpClient();

        var filterList = new List<Filter> { new("status", "0") };
        if (!string.IsNullOrWhiteSpace(search)) {
            var partNoValue = Filters.CreateILikeValue(search);
            var partNoFilter = Filters.CreatePropertyValue("partNo", partNoValue);
            var descriptionValue = Filters.CreateILikeValue(search);
            var descriptionFilter = Filters.CreatePropertyValue("description", descriptionValue);
            var orFilter = Filters.CreateOrValue(partNoFilter, descriptionFilter);
            filterList.Add(orFilter);
        }

        var filter = Filters.CreateFilter(filterList.ToArray());
        var url = $"inventory/items/?limit=0&filter={filter}&sort=description";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<InventoryItem>>(url);
        return results?.Records ?? new List<InventoryItem>();
    }

    public async Task<IEnumerable<Vendor>> GetVendors()
    {
        var client = GetSpireHttpClient();
        var filter = Filters.CreateFilter(new Filter("status", "A"), new Filter("hold", false));
        var url = $"vendors/?limit=0&filter={filter}&sort=name";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<Vendor>>(url);
        return results?.Records ?? new List<Vendor>();
    }

    public async Task<VendorDetail?> GetVendorDetail(int id)
    {
        try {
            var client = GetSpireHttpClient();
            var url = $"vendors/{id}";
            var results = await client.GetFromJsonAsync<VendorDetail>(url);
            return results;

        } catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.NotFound) {
            return null;
        }
    }

    public async Task<VendorDetail?> GetVendorByNumber(string number)
    {
        var client = GetSpireHttpClient();
        var filter = Filters.CreateFilter(new Filter("vendorNo", number));
        var url = $"vendors/?filter={filter}";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<VendorDetail>>(url);
        var vendors = results?.Records ?? new List<VendorDetail>();
        var vendor = vendors.FirstOrDefault();
        return vendor;
    }

    public async Task<List<string>> GetUpgradeVendorEmailAddresses()
    {
        var client = GetSpireHttpClient();
        var filter = Filters.CreateFilter(new Filter("vendorNo", "UPGRADES"));
        var url = $"vendors/?limit=1&filter={filter}";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<Vendor>>(url);
        var firstVendor = results?.Records?.FirstOrDefault();

        if (firstVendor != null) {
            url = $"vendors/{firstVendor.Id}";
            try {
                var detailResults = await client.GetFromJsonAsync<VendorDetail>(url);
                var contacts = detailResults?.Address?.Contacts?
                    .Where(a => !string.IsNullOrWhiteSpace(a.Email) && a.ContactType?.Name?.StartsWith("Upgrade Notification") == true)
                    .Select(e => e.Email.ToLower())
                    .Distinct()
                    .ToList() ?? [];

                return contacts;

            } catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.NotFound) {
                return [];
            }
        }

        return [];
    }

    public async Task<IEnumerable<Customer>> GetCustomers()
    {
        var client = GetSpireHttpClient();
        var filter = Filters.CreateFilter(new Filter("status", "A"), new Filter("hold", false));
        var url = $"customers/?limit=0&filter={filter}&sort=name";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<Customer>>(url);
        return results?.Records ?? new List<Customer>();
    }

    public async Task<IEnumerable<ShippingMethod>> GetShippingMethods()
    {
        var client = GetSpireHttpClient();
        var url = "shipping_methods/?limit=0&sort=description";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<ShippingMethod>>(url);
        return results?.Records ?? new List<ShippingMethod>();
    }

    public async Task<IEnumerable<InventoryComment>> GetInventoryComments()
    {
        var client = GetSpireHttpClient();
        var url = "inventory/comments/?limit=0&sort=description";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<InventoryComment>>(url);
        return results?.Records ?? new List<InventoryComment>();
    }

    public async Task<CustomerDetail?> GetCustomerDetail(int id)
    {
        if (id == 0) {
            return null;
        }

        try {
            var client = GetSpireHttpClient();
            var url = $"customers/{id}";
            var results = await client.GetFromJsonAsync<CustomerDetail>(url);

            if (results != null) {
                var addressPriceLevels = await GetAddressPriceLevelsForCustomer(results.CustomerNo);
                results.ShippingAddresses.ForEach(a => {
                    a.PriceLevel = addressPriceLevels.FirstOrDefault(p => p.ShipId == a.ShipId)?.PriceLevel;
                });
            }

            return results;

        } catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.NotFound) {
            return null;
        }
    }

    public async Task<IEnumerable<Salesperson>> GetSalespeople()
    {
        var client = GetSpireHttpClient();
        var url = "salespeople/?limit=0&sort=name";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<Salesperson>>(url);
        return results?.Records ?? new List<Salesperson>();
    }

    public async Task<IEnumerable<CustomerAddress>> GetCustomerAddresses(string? search = null)
    {
        var client = GetSpireHttpClient();

        var filterList = new List<Filter> { new("linkType", "CUST"), new("type", "S") };
        if (!string.IsNullOrWhiteSpace(search)) {
            var nameValue = Filters.CreateILikeValue(search);
            var nameFilter = Filters.CreatePropertyValue("name", nameValue);
            var shipIdValue = Filters.CreateILikeValue(search);
            var shipIdFilter = Filters.CreatePropertyValue("shipId", shipIdValue);
            var orFilter = Filters.CreateOrValue(nameFilter, shipIdFilter);
            filterList.Add(orFilter);
        }

        var filter = Filters.CreateFilter(filterList.ToArray());
        var url = $"addresses/?limit=0&filter={filter}&sort=name";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<CustomerAddress>>(url);
        return results?.Records ?? new List<CustomerAddress>();
    }

    public async Task<(CustomerDetail Customer, CustomerShippingAddress ShipTo)?> GetAddressDetail(int id)
    {
        if (id == 0) {
            return null;
        }

        var client = GetSpireHttpClient();

        /* HACK: This is returning a 404, so we need to first find the customer, then get the Shipping Address
        var url = $"addresses/{id}";
        var results = await client.GetFromJsonAsync<CustomerShippingAddress>(url);
        */

        var filter = Filters.CreateFilter(new Filter("id", id));
        var url = $"addresses/?limit=1&filter={filter}";
        var addressResults = await client.GetFromJsonAsync<SpireCollectionResponse<CustomerAddress>>(url);
        if (addressResults?.Records?.Count != 1) {
            return null;
        }

        var firstAddress = addressResults.Records.First();
        filter = Filters.CreateFilter(new Filter("customerNo", firstAddress.LinkNo));
        url = $"customers/?limit=1&filter={filter}";
        var customerListResults = await client.GetFromJsonAsync<SpireCollectionResponse<Customer>>(url);
        if (customerListResults?.Records?.Count != 1) {
            return null;
        }

        var firstCustomer = customerListResults.Records.First();
        url = $"customers/{firstCustomer.Id}";
        try {
            var customer = await client.GetFromJsonAsync<CustomerDetail>(url);

            if (customer == null) {
                return null;
            }

            var shipTo = customer.ShippingAddresses.FirstOrDefault(a => a.Id == id);
            if (shipTo == null) {
                return null;
            }

            var addressPriceLevels = await GetAddressPriceLevelsForCustomer(customer.CustomerNo);
            shipTo.PriceLevel = addressPriceLevels.FirstOrDefault(p => p.ShipId == shipTo.ShipId)?.PriceLevel;

            return (customer, shipTo);

        } catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.NotFound) {
            return null;
        }
    }

    public async Task<IEnumerable<EmailTemplate>> EmailTemplates(string? search)
    {
        var client = GetSpireHttpClient();

        var filterParam = string.Empty;
        if (!string.IsNullOrWhiteSpace(search)) {
            var filterValue = Filters.CreateILikeValue(search);
            var filter = Filters.CreatePropertyValue("name", filterValue);
            filterParam = $"&filter={filter}";
        }

        var url = $"email/templates/?limit=0{filterParam}&sort=name";
        var results = await client.GetFromJsonAsync<SpireCollectionResponse<EmailTemplate>>(url);
        return results?.Records ?? new List<EmailTemplate>();
    }

    public async Task<PurchaseOrder?> CreatePurchaseOrder(PrebookDetail prebook)
    {
        if (!prebook.VendorId.HasValue) {
            throw new ArgumentOutOfRangeException(nameof(prebook), "Prebook must have a vendor");
        }

        var po = new PurchaseOrderCreate {
            Vendor = new PurchaseOrderCreate.PurchaseOrderVendor {
                Id = prebook.VendorId.Value
            },
            Items = prebook.Items.Select(i => new PurchaseOrderCreate.PurchaseOrderItem {
                OrderQty = i.OrderQuantity,
                Inventory = new PurchaseOrderCreate.PurchaseOrderItemInventory {
                    Id = i.SpireInventoryId
                }
            }).ToList()
        };

        var client = GetSpireHttpClient();
        var url = "purchasing/orders/";
        var json = JsonSerializer.Serialize(po, JsonSerializerOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await client.PostAsync(url, content);

        response.EnsureSuccessStatusCode();
        var location = response.Headers.Location;
        if (location == null) {
            throw new ApplicationException("Did not receive Location header from Spire API.");
        }

        try {
            var created = await client.GetFromJsonAsync<PurchaseOrder>(location);
            return created;

        } catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.NotFound) {
            return null;
        }
    }

    public async Task<SalesOrder?> CreateSalesOrder(FutureOrderDetail order, List<PrebookDetail> prebooks)
    {
        if (!order.CustomerId.HasValue) {
            throw new ArgumentOutOfRangeException(nameof(order), "Future Order must have a customer");
        }

        if (!order.SalespersonId.HasValue) {
            throw new ArgumentOutOfRangeException(nameof(order), "Future Order must have a salesperson");
        }

        if (!order.ShipToId.HasValue) {
            throw new ArgumentOutOfRangeException(nameof(order), "Future Order must have a ship-to");
        }

        if (!order.RequiredDate.HasValue) {
            throw new ArgumentOutOfRangeException(nameof(order), "Future Order must have a required date");
        }

        if (!order.ArrivalDate.HasValue) {
            throw new ArgumentOutOfRangeException(nameof(order), "Future Order must have an arrival date");
        }

        var items = order.Items.Where(i => i.OrderQuantity > 0).ToList();
        if (items.Any(i => !i.VendorId.HasValue)) {
            throw new ArgumentOutOfRangeException(nameof(order), "All items must have a vendor.");
        }

        var client = GetSpireHttpClient();
        var url = $"customers/{order.CustomerId}";
        var customer = await client.GetFromJsonAsync<CustomerDetail>(url);
        var shipTo = customer?.ShippingAddresses.FirstOrDefault(a => a.Id == order.ShipToId.Value);
        if (string.IsNullOrWhiteSpace(shipTo?.ShipId)) {
            throw new ArgumentOutOfRangeException($"Ship To {order.ShipToName} ({order.ShipToId}) was not found");
        }

        string? salespersonCode = null;
        if (order.SalespersonId.HasValue) {
            url = "salespeople/?limit=0";
            var salespeople = await client.GetFromJsonAsync<SpireCollectionResponse<Salesperson>>(url);
            var salesperson =
                (salespeople?.Records ?? new List<Salesperson>()).FirstOrDefault(s =>
                    s.Id == order.SalespersonId.Value);
            salespersonCode = salesperson?.Code;
        }

        var filter = Filters.CreateFilter(new Filter("status", "A"), new Filter("hold", false));
        url = $"vendors/?limit=0&filter={filter}&sort=name";
        var vendorResponse = await client.GetFromJsonAsync<SpireCollectionResponse<Vendor>>(url);
        var vendors = (vendorResponse?.Records ?? new List<Vendor>()).ToDictionary(v => v.Id, v => v.VendorNo);
        if (order.Items.Any(i => !vendors.ContainsKey(i.VendorId.GetValueOrDefault()))) {
            throw new ArgumentOutOfRangeException(nameof(order), "All items must have a valid vendor.");
        }

        string? shipViaCode = null;
        if (order.ShipViaId.HasValue) {
            url = "shipping_methods/?limit=0&sort=description";
            var shippingMethodResults = await client.GetFromJsonAsync<SpireCollectionResponse<ShippingMethod>>(url);
            var shippingMethods = shippingMethodResults?.Records ?? new List<ShippingMethod>();
            shipViaCode = shippingMethods.FirstOrDefault(m => m.Id == order.ShipViaId)?.Code;
        }

        var prebooksByFutureOrderItemId = prebooks.Aggregate(new Dictionary<int, int>(), (list, prebook) => {
            foreach (var prebookItem in prebook.Items) {
                if (prebookItem.FutureOrderItemId.HasValue && !list.ContainsKey(prebookItem.FutureOrderItemId.Value)) {
                    list.Add(prebookItem.FutureOrderItemId.Value, prebook.Id);
                }
            }

            return list;
        });

        var salesOrderCreateItems = new List<SalesOrderCreate.SalesOrderItem>();
        // HACK: the sequence is to set the reference # via the database directly
        var sequence = 1;
        items.ForEach(i => {
            var comments = i.Comments?.Replace(Constants.OverAndAboveBlanketComment, "") ?? string.Empty;

            var prebookId = default(int?);
            if (prebooksByFutureOrderItemId.ContainsKey(i.Id)) {
                prebookId = prebooksByFutureOrderItemId[i.Id];
            }

            var headerGrowerItemNotes = order.GrowerItemNotes ?? string.Empty;
            var itemGrowerItemNotes = i.GrowerItemNotes ?? string.Empty;
            var growerItemNotes = headerGrowerItemNotes +
                                  (string.IsNullOrEmpty(headerGrowerItemNotes) ||
                                   string.IsNullOrEmpty(itemGrowerItemNotes)
                                      ? string.Empty
                                      : "\n") +
                                  itemGrowerItemNotes;

            var item = new SalesOrderCreate.SalesOrderItem(i.Id, sequence, order.CustomerId.Value, i.SpireInventoryId,
                i.OrderQuantity, vendors[i.VendorId.GetValueOrDefault()],
                comments, i.PotCover, i.Upc, i.Retail, i.DateCode, i.WeightsAndMeasures, i.UnitPrice,
                i.CustomerItemCode, i.SpecialPrice, prebookId, growerItemNotes);
            salesOrderCreateItems.Add(item);
            sequence++;
        });

        var standardComments = order.Comments
            .Where(c => c.IsStandardComment)
            .ToList();

        standardComments.ForEach(comment => {
            if (!string.IsNullOrWhiteSpace(comment.Comments)) {
                var item = new SalesOrderCreate.SalesOrderItem(sequence, comment.Comments);
                salesOrderCreateItems.Add(item);
                sequence++;
            }
        });

        var salesOrder = new SalesOrderCreate(order.CustomerId.Value, shipTo.ShipId, salespersonCode ?? string.Empty,
            shipViaCode, order.RequiredDate.Value, order.ArrivalDate.Value,
            order.CustomerPurchaseOrderNumber, order.SpireNotes, salesOrderCreateItems);

        url = "sales/orders/";
        var json = JsonSerializer.Serialize(salesOrder, JsonSerializerOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await client.PostAsync(url, content);

        if (!response.IsSuccessStatusCode) {
            var jsonContent = await response.Content.ReadAsStringAsync();

            try {
                var error = JsonSerializer.Deserialize<SpireError>(jsonContent);
                if (!string.IsNullOrWhiteSpace(error?.Message)) {
                    throw new ApplicationException($"Could not create Spire Order. Error: {error.Message}");
                }
            } catch { /* just let it fall through */
            }

            throw new ApplicationException($"Could not create Spire Order. Error: {jsonContent}.");
        }

        var location = response.Headers.Location;
        if (location == null) {
            throw new ApplicationException("Did not receive Location header from Spire API.");
        }

        var created = await client.GetFromJsonAsync<SalesOrder>(location);

        // HACK: hopefully this can be done via the API at some point
        if (created != null) {
            await UpdateReferenceNumbers(salesOrder, created);
        }


        return created;
    }

    public async Task DeleteSalesOrder(int id)
    {
        var client = GetSpireHttpClient();
        var url = $"sales/orders/{id}";
        var response = await client.DeleteAsync(url);

        // if it didn't exist, we'll get a 404, which is fine
        if (!response.IsSuccessStatusCode && response.StatusCode != HttpStatusCode.NotFound) {
            var jsonContent = await response.Content.ReadAsStringAsync();

            try {
                var error = JsonSerializer.Deserialize<SpireError>(jsonContent);
                if (!string.IsNullOrWhiteSpace(error?.Message)) {
                    throw new ApplicationException($"Could not delete Spire Order. Error: {error.Message}");
                }
            } catch { /* just let it fall through */
            }

            throw new ApplicationException($"Could not create Spire Order. Error: {jsonContent}.");
        }
    }

    public async Task<SalesOrderDetail?> SalesOrderDetail(int id)
    {
        try {
            var client = GetSpireHttpClient();
            var url = $"sales/orders/{id}";
            var results = await client.GetFromJsonAsync<SalesOrderDetail>(url);
            return results;
        } catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.NotFound) {
            return null;
        }
    }

    public async Task<decimal?> GetItemPrice(int id, int? customerId, int? shipToId, DateTime? requiredDate)
    {
        decimal? price = null;

        var client = GetSpireHttpClient();

        var filterList = new List<Filter> { new("inventory.id", id) };
        var filter = Filters.CreateFilter(filterList.ToArray());
        var url = $"inventory/sell_prices/?filter={filter}&limit=0&sort=priceLevel.id&sort=effectiveDate";

        var results = await client.GetFromJsonAsync<SpireCollectionResponse<InventorySellPrice>>(url);

        if (results?.Records?.Any() == true) {

            var sellPrices = results.Records;

            if (customerId != null && shipToId != null) {
                url = $"customers/{customerId}";
                var customer = await client.GetFromJsonAsync<CustomerDetailSellLevel>(url);
                if (customer != null) {
                    var shipTo = customer.ShippingAddresses.FirstOrDefault(a => a.Id == shipToId);
                    if (shipTo != null) {
                        // if there's a required date, only pull prices effective before that date
                        var sellPrice = sellPrices
                                            .Where(p => p.PriceLevel.Id == shipTo.SellLevel &&
                                                        (requiredDate == null || p.EffectiveDate <= requiredDate))
                                            .MaxBy(p => p.EffectiveDate) ??
                                        sellPrices.FirstOrDefault(p =>
                                            p.PriceLevel.Id == shipTo.SellLevel && p.EffectiveDate == null);

                        if (sellPrice != null) {

                            price = sellPrice.Price;
                        }
                    }
                }
            }

            if (price == null) {
                filterList = new List<Filter> { new("main", true) };
                filter = Filters.CreateFilter(filterList.ToArray());
                url = $"inventory/price_levels/?limit=1&filter={filter}";
                var priceLevelResponse = await client.GetFromJsonAsync<SpireCollectionResponse<PriceLevel>>(url);
                var mainPriceLevel = priceLevelResponse?.Records?.FirstOrDefault();

                if (mainPriceLevel != null) {

                    var sellPrice = sellPrices
                                        .Where(p => p.PriceLevel.Id == mainPriceLevel.Id &&
                                                    (requiredDate == null || p.EffectiveDate <= requiredDate))
                                        .MaxBy(p => p.EffectiveDate) ??
                                    sellPrices.FirstOrDefault(p =>
                                        p.PriceLevel.Id == mainPriceLevel.Id && p.EffectiveDate == null);
                    if (sellPrice != null) {
                        price = sellPrice.Price;
                    }
                }
            }
        }

        return price;
    }

    public async Task<List<SpireItemPrice>> GetItemPrices(int? customerId, int? shipToId, DateTime? requiredDate,
        List<int> ids)
    {
        var list = new List<SpireItemPrice>();

        if (ids.Any()) {

            if (ids.Count == 1) {
                var id = ids.First();
                var price = await GetItemPrice(id, customerId, shipToId, requiredDate);
                if (price.GetValueOrDefault() > 0) {
                    list.Add(new SpireItemPrice(id, price.GetValueOrDefault()));
                }
            } else {
                var client = GetSpireHttpClient();

                var filterList = new List<Filter>();
                var filters = ids.Select(id => Filters.CreatePropertyValue("inventory.id", id) as JsonNode).ToArray();
                var or = Filters.CreateOrValue(filters);
                filterList.Add(or);
                var filter = Filters.CreateFilter(filterList.ToArray());

                var url = $"inventory/sell_prices/?limit=0&filter={filter}";

                var results = await client.GetFromJsonAsync<SpireCollectionResponse<InventorySellPrice>>(url);

                var sellPrices = results?.Records ?? new List<InventorySellPrice>();

                if (customerId.HasValue && shipToId.HasValue) {
                    url = $"customers/{customerId}";
                    var customer = await client.GetFromJsonAsync<CustomerDetailSellLevel>(url);

                    if (customer != null) {

                        var shippingAddress = customer.ShippingAddresses.FirstOrDefault(a => a.Id == shipToId);
                        if (shippingAddress != null) {

                            foreach (var inventoryId in ids) {
                                var sellPrice = sellPrices
                                                    .Where(p => p.Inventory.Id == inventoryId &&
                                                                p.PriceLevel.Id == shippingAddress.SellLevel &&
                                                                (requiredDate == null ||
                                                                 p.EffectiveDate <= requiredDate))
                                                    .MaxBy(p => p.EffectiveDate) ??
                                                sellPrices.FirstOrDefault(p =>
                                                    p.Inventory.Id == inventoryId &&
                                                    p.PriceLevel.Id == shippingAddress.SellLevel &&
                                                    p.EffectiveDate == null);
                                if (sellPrice != null) {
                                    list.Add(new SpireItemPrice(inventoryId, sellPrice.Price));
                                }
                            }
                        }
                    }
                }

                var missing = ids.Where(i => !list.Any(l => l.SpireInventoryItemId == i)).ToList();

                if (missing.Any()) {

                    filterList = new List<Filter> { new("main", true) };
                    filter = Filters.CreateFilter(filterList.ToArray());
                    url = $"inventory/price_levels/?limit=1&filter={filter}";
                    var priceLevelResponse = await client.GetFromJsonAsync<SpireCollectionResponse<PriceLevel>>(url);
                    var mainPriceLevel = priceLevelResponse?.Records?.FirstOrDefault();

                    if (mainPriceLevel != null) {

                        foreach (var id in missing) {
                            var sellPrice = sellPrices
                                                .Where(p => p.Inventory.Id == id &&
                                                            p.PriceLevel.Id == mainPriceLevel.Id &&
                                                            (requiredDate == null || p.EffectiveDate <= requiredDate))
                                                .MaxBy(p => p.EffectiveDate) ??
                                            sellPrices.FirstOrDefault(p =>
                                                p.Inventory.Id == id && p.PriceLevel.Id == mainPriceLevel.Id &&
                                                p.EffectiveDate == null);
                            if (sellPrice != null) {
                                list.Add(new SpireItemPrice(id, sellPrice.Price));
                            }
                        }
                    }
                }
            }
        }

        return list;
    }

    public async Task<List<TaskListPurchaseOrder>> BoekestynPurchaseOrderTaskList(DateTime? start, DateTime? end)
    {
        await using var spireConnection = GetSpireConnection();
        await using var connection = GetConnection();
        var client = GetSpireHttpClient();
        var filters = new List<Filter> {
            Filters.CreateOrValue(
                Filters.CreatePropertyValue("vendor.id", Constants.BoekestynVendorId),
                Filters.CreatePropertyValue("vendor.id", Constants.UpgradesVendorId)
            )
        };

        if (start.HasValue && end.HasValue && start < end) {
            var and = Filters.CreateAndValue(
                Filters.CreateGreaterThanOrEqualToValue("requiredDate", start.Value.ToString("yyyy-MM-dd")),
                Filters.CreateLessThanOrEqualToValue("requiredDate", end.Value.ToString("yyyy-MM-dd")));
            filters.Add(and);
        } else if (start.HasValue && end == null) {
            filters.Add(Filters.CreateGreaterThanOrEqualToFilter("requiredDate", start.Value.ToString("yyyy-MM-dd")));
        } else if (end.HasValue && start == null) {
            filters.Add(Filters.CreateLessThanOrEqualToFilter("requiredDate", end.Value.ToString("yyyy-MM-dd")));
        } else if (start.HasValue && end.HasValue && start == end) {
            filters.Add(new Filter("requiredDate", start.Value.ToString("yyyy-MM-dd")));
        }

        var filter = Filters.CreateFilter(filters.ToArray());
        var url = $"purchasing/orders/?limit=0&filter={filter}";
        var response = await client.GetFromJsonAsync<SpireCollectionResponse<PurchaseOrder>>(url);
        var orders = response?.Records ?? new();

        var taskList = new List<TaskListPurchaseOrder>();

        if (orders.Any()) {
            var orderNumbers = string.Join(", ", orders.Select(o => $"'{o.Number}'"));

            var sql =
                $@"select poi.id, po.po_number, poi.description, poi.order_qty, coalesce(soi.comment, '') as comment,
soi.part_no as spire_part_number,
coalesce(a.udf_data::json ->>'ship_to_box_code', '') AS box_code,
coalesce(cast(poi.udf_data::json ->> 'priority' as boolean), False) as priority,
cast(case when length(poi.udf_data::json ->> 'prep_start') > 0 then poi.udf_data::json ->> 'prep_start' else null end as timestamp without time zone) as prep_start,
cast(case when length(poi.udf_data::json ->> 'prep_complete') > 0 then poi.udf_data::json ->> 'prep_complete' else null end as timestamp without time zone) as prep_complete,
cast(case when length(poi.udf_data::json ->> 'pack_start') > 0 then poi.udf_data::json ->> 'pack_start' else null end as timestamp without time zone) as pack_start,
cast(case when length(poi.udf_data::json ->> 'pack_complete') > 0 then poi.udf_data::json ->> 'pack_complete' else null end as timestamp without time zone) as pack_complete,
cast(case when length(poi.udf_data::json ->> 'upc_complete') > 0 then poi.udf_data::json ->> 'upc_complete' else null end as timestamp without time zone) as upc_complete,
case when a.udf_data::json ->> 'check_skids' = 'Y' then True else False end as good_skids,
cast(case when length(soi.udf_data::json ->> 'prebook_id') > 0 then soi.udf_data::json ->> 'prebook_id' else null end as integer) as prebook_id
from purchase_order_items poi
join purchase_orders po on poi.po_number = po.po_number
left join inventory_requisitions r on poi.guid = r.targ_guid
left join sales_order_items soi on r.src_guid = soi.guid
left join sales_orders so on soi.order_no = so.order_no
left join addresses a on so.cust_no = a.link_no AND so.ship_id = a.ship_id AND a.addr_type = 'S' and a.link_table = 'CUST'
where poi.po_number in ({orderNumbers});";
            var items = (await spireConnection.QueryAsync<TaskListPurchaseOrderItem>(sql)).ToList();

            sql = "select i.prebook_id, i.spire_part_number, i.boek_priority, i.upc_printed " +
                  "from prebook_items i join prebooks p on i.prebook_id = p.id " +
                  "where (p.vendor_id = @boekVendorId OR p.vendor_id = @upgradesVendorId) and p.required_date between @start and @end and (boek_priority = True or upc_printed is not null);";
            var prebookItems = (await connection.QueryAsync<BoekestynPurchaseOrderItemPrebookItem>(sql,
                new {
                    BoekVendorId = Constants.BoekestynVendorId, UpgradesVendorId = Constants.UpgradesVendorId, start,
                    end
                })).ToList();

            foreach (var order in orders) {
                var orderItems = items.Where(i => i.PoNumber == order.Number).ToList();

                // check whether these were set as prebooks
                foreach (var item in orderItems.Where(i => !i.Priority && i.UpcComplete == null)) {
                    if (!item.Priority) {
                        if (prebookItems.Any(i =>
                                i.BoekPriority && i.SpirePartNumber == item.SpirePartNumber &&
                                i.PrebookId == item.PrebookId)) {
                            await SetPurchaseOrderItemPriority(item.Id, true);
                            item.Priority = true;
                        }
                    }

                    if (item.UpcComplete == null) {
                        var hasUpcComplete = prebookItems.Any(i =>
                            i.UpcPrinted.HasValue && i.SpirePartNumber == item.SpirePartNumber &&
                            i.PrebookId == item.PrebookId);
                        if (hasUpcComplete) {
                            await SetPurchaseOrderItemUpcComplete(item.Id);
                            item.UpcComplete = DateTime.Now;
                        }
                    }
                }

                var task = new TaskListPurchaseOrder(order.Id, order.Number, order.RequiredDate?.ToString("yyyy-MM-dd"),
                    orderItems);
                taskList.Add(task);
            }
        }

        return taskList;
    }

    public async Task<TaskListPurchaseOrder?> BoekestynPurchaseOrderTask(int id)
    {
        await using var connection = GetSpireConnection();

        var sql =
            @"select poi.id, po.po_number, poi.description, poi.order_qty, coalesce(soi.comment, '') as comment,
soi.part_no as spire_part_number,
coalesce(a.udf_data::json ->>'ship_to_box_code', '') AS box_code,
coalesce(cast(poi.udf_data::json ->> 'priority' as boolean), False) as priority,
cast(case when length(poi.udf_data::json ->> 'prep_start') > 0 then poi.udf_data::json ->> 'prep_start' else null end as timestamp without time zone) as prep_start,
cast(case when length(poi.udf_data::json ->> 'prep_complete') > 0 then poi.udf_data::json ->> 'prep_complete' else null end as timestamp without time zone) as prep_complete,
cast(case when length(poi.udf_data::json ->> 'pack_start') > 0 then poi.udf_data::json ->> 'pack_start' else null end as timestamp without time zone) as pack_start,
cast(case when length(poi.udf_data::json ->> 'pack_complete') > 0 then poi.udf_data::json ->> 'pack_complete' else null end as timestamp without time zone) as pack_complete,
cast(case when length(poi.udf_data::json ->> 'upc_complete') > 0 then poi.udf_data::json ->> 'upc_complete' else null end as timestamp without time zone) as upc_complete,
case when a.udf_data::json ->> 'check_skids' = 'Y' then True else False end as good_skids,
cast(case when length(soi.udf_data::json ->> 'prebook_id') > 0 then soi.udf_data::json ->> 'prebook_id' else null end as integer) as prebook_id
from purchase_order_items poi
join purchase_orders po on poi.po_number = po.po_number
left join inventory_requisitions r on poi.guid = r.targ_guid
left join sales_order_items soi on r.src_guid = soi.guid
left join sales_orders so on soi.order_no = so.order_no
left join addresses a on so.cust_no = a.link_no AND so.ship_id = a.ship_id AND a.addr_type = 'S' and a.link_table = 'CUST'
where poi.id = @id;";
        var item = await connection.QueryFirstOrDefaultAsync<TaskListPurchaseOrderItem>(sql, new { id });

        if (item != null) {

            var client = GetSpireHttpClient();

            var filter = Filters.CreateFilter(new Filter("number", item.PoNumber));
            var url = $"purchasing/orders/?limit=1&filter={filter}";
            var response = await client.GetFromJsonAsync<SpireCollectionResponse<PurchaseOrder>>(url);
            var order = response?.Records?.FirstOrDefault();

            if (order != null) {
                var task = new TaskListPurchaseOrder(order.Id, order.Number,
                    order.RequiredDate?.ToString("yyyy-MM-dd"),
                    new List<TaskListPurchaseOrderItem> { item });

                return task;
            }
        }

        return null;
    }

    public async Task<byte[]?> BoekestynPurchaseOrderReportData(int id)
    {
        var client = GetSpireHttpClient();
        var url = $"purchasing/orders/{id}";
        var order = await client.GetFromJsonAsync<PurchaseOrderDetail>(url);
        if (order == null) {
            return null;
        }

        var linkTableFilter = new Filter("source.linkTable", "PORD");
        var linkNoFilter = new Filter("source.linkNo", order.Number);
        var filter = Filters.CreateFilter(linkTableFilter, linkNoFilter);
        url = $"purchasing/orders/{order.Id}/email_messages/?limit=0&filter={filter}";
        var emailMessages = await client.GetFromJsonAsync<SpireCollectionResponse<EmailMessage>>(url);
        var latest = emailMessages?.Records?.MaxBy(m => m.Created);
        if (!string.IsNullOrWhiteSpace(latest?.Links.Self)) {
            url = $"{latest.Links.Self}/attachments/";
            var attachments = await client.GetFromJsonAsync<SpireCollectionResponse<EmailAttachment>>(url);
            var pdf = attachments?.Records?.FirstOrDefault(a =>
                a.MimeType == "application/pdf" && a.Filename.StartsWith("Purchase Order"));
            if (pdf != null) {
                var href = pdf.Links.Data;
                if (!string.IsNullOrWhiteSpace(href)) {
                    var response = await client.GetAsync(href);
                    if (response.IsSuccessStatusCode) {
                        var bytes = await response.Content.ReadAsByteArrayAsync();
                        return bytes;
                    }
                }
            }
        }

        return null;
    }

    public async Task SetPurchaseOrderItemPriority(int id, bool priority) =>
        await GetSpireConnection()
            .ExecuteAsync(
                $"update purchase_order_items set udf_data = jsonb_set(udf_data, '{{priority}}', '{priority.ToString().ToLower()}', true) where id = @id;",
                new { id });

    public async Task SetPurchaseOrderItemUpcComplete(int id) =>
        await GetSpireConnection()
            .ExecuteAsync(
                "update purchase_order_items set udf_data = jsonb_set(udf_data, '{upc_complete}', cast('\"' || to_char(current_timestamp, 'yyyy-MM-dd HH24:MI') || '\"' as jsonb), true) where id = @id;",
                new { id });

    public async Task SetPurchaseOrderItemPrepStart(int id) =>
        await GetSpireConnection()
            .ExecuteAsync(
                "update purchase_order_items set udf_data = jsonb_set(udf_data, '{prep_start}', cast('\"' || to_char(current_timestamp, 'yyyy-MM-dd HH24:MI') || '\"' as jsonb), true) where id = @id;",
                new { id });

    public async Task SetPurchaseOrderItemPrepComplete(int id) =>
        await GetSpireConnection()
            .ExecuteAsync(
                "update purchase_order_items set udf_data = jsonb_set(udf_data, '{prep_complete}', cast('\"' || to_char(current_timestamp, 'yyyy-MM-dd HH24:MI') || '\"' as jsonb), true) where id = @id;",
                new { id });

    public async Task SetPurchaseOrderItemPackingStart(int id) =>
        await GetSpireConnection()
            .ExecuteAsync(
                "update purchase_order_items set udf_data = jsonb_set(udf_data, '{pack_start}', cast('\"' || to_char(current_timestamp, 'yyyy-MM-dd HH24:MI') || '\"' as jsonb), true) where id = @id;",
                new { id });

    public async Task SetPurchaseOrderItemPackingComplete(int id) =>
        await GetSpireConnection()
            .ExecuteAsync(
                "update purchase_order_items set udf_data = jsonb_set(udf_data, '{pack_complete}', cast('\"' || to_char(current_timestamp, 'yyyy-MM-dd HH24:MI') || '\"' as jsonb), true) where id = @id;",
                new { id });

    public async Task<List<PriceLevel>> GetPriceLevels() =>
        (await GetSpireHttpClient().GetFromJsonAsync<SpireCollectionResponse<PriceLevel>>("inventory/price_levels/?limit=0&sort=code"))?.Records ?? [];

    private async Task<List<AddressPriceLevel>> GetAddressPriceLevelsForCustomer(string customerNumber) =>
        (await GetSpireConnection().QueryAsync<AddressPriceLevel>(
            "select a.ship_id, p.code price_level from addresses a join inventory_price_levels p on a.price_level_id = p.id where link_table = 'CUST' and addr_type = 'S' and link_no = @customerNumber;",
            new { customerNumber })).ToList();

    private async Task UpdateReferenceNumbers(SalesOrderCreate futureOrder, SalesOrder spireOrder)
    {
        await using var spireConnection = GetSpireConnection();
        await using var connection = GetConnection();
        foreach (var futureOrderItem in futureOrder.Items) {
            var spireOrderItem = spireOrder.Items.FirstOrDefault(i => i.Sequence == futureOrderItem.Sequence);
            if (spireOrderItem != null) {
                await spireConnection.ExecuteAsync(
                    "UPDATE sales_order_items SET ref_no = @referenceNumber WHERE id = @id",
                    new { futureOrderItem.ReferenceNumber, spireOrderItem.Id });
                await connection.ExecuteAsync(
                    "UPDATE future_order_items SET spire_sales_order_item_id = @spireOrderItemId WHERE id = @id",
                    new { SpireOrderItemId = spireOrderItem.Id, futureOrderItem.Id });
            }
        }
    }

    public async Task<int> UpdateSaleOrderUdfs() =>
        (await GetSpireConnection().QueryAsync<string>(@"
with shipping as (
  select
    link_no,
    ship_id,
    coalesce(udf_data->>'ship_to_box_code', '') box_code
  from
    addresses
  where
    link_table = 'CUST' and addr_type = 'S'
),
billing as (
  select
    link_no,
    ship_id,
    coalesce(udf_data->>'ship_to_box_code', '') box_code
  from
    addresses
  where
    link_table = 'CUST' and addr_type = 'B'
),
sales as (
  select
    *
  from
    sales_orders so
  where
    so._deleted is null
),
box_codes as (
  select 
    so.order_no,
    a.box_code
  from
    sales so
    join shipping a on so.ship_id = a.ship_id and so.cust_no = a.link_no
  union select
    so.order_no,
    b.box_code
  from
    sales so
    join billing b on so.ship_id = b.ship_id and so.cust_no = b.link_no
    left join shipping a on so.ship_id = a.ship_id and so.cust_no = a.link_no
  where
    a.ship_id is null
)
update
  sales_orders so
set
  udf_data = jsonb_set(
    so.udf_data,
    '{ship_to_box_code}',
    to_jsonb(box_code),
    true
  )
from
  box_codes bc
where
  so.order_no = bc.order_no
  and so.udf_data->>'ship_to_box_code' is distinct from bc.box_code
  and so._deleted is null
returning so.order_no;")
        ).Count();

    public record SpireItemPrice(int SpireInventoryItemId, decimal Price);

    public record TaskListPurchaseOrder(
        int Id,
        string PurchaseOrderNumber,
        string? RequiredDate,
        List<TaskListPurchaseOrderItem> Items);

    public class TaskListPurchaseOrderItem
    {
        private readonly Regex _packQuantityRegex = new(@"[Xx]\s+(\d+)$");

        public int Id { get; set; }
        public string PoNumber { get; set; } = string.Empty;
        public int OrderQty { get; set; }
        public string SpirePartNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string BoxCode { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        public bool Priority { get; set; }
        public DateTime? PrepStart { get; set; }
        public DateTime? PrepComplete { get; set; }
        public DateTime? PackStart { get; set; }
        public DateTime? PackComplete { get; set; }
        public DateTime? UpcComplete { get; set; }
        public bool GoodSkids { get; set; }
        public int? PrebookId { get; set; }

        public int? PackQuantity
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(Description)) {
                    var match = _packQuantityRegex.Match(Description);
                    if (match.Success && match.Groups.Count > 0) {
                        var packQuantityValue = match.Groups[1].Value;
                        if (int.TryParse(packQuantityValue, out var packQuantity)) {
                            return packQuantity;
                        }
                    }
                }

                return null;
            }
        }
    }

    public class BoekestynPurchaseOrderItemPrebookItem
    {
        public int PrebookId { get; set; }
        public string SpirePartNumber { get; set; } = string.Empty;
        public bool BoekPriority { get; set; }
        public DateTime? UpcPrinted { get; set; }
    }
}