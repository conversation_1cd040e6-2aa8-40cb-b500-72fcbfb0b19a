﻿namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderCreate
{
    public DateTime? RequiredDate { get; set; }
    public DateTime? ArrivalDate { get; set; }
    public string? SeasonName { get; set; }
    public string? BoxCode { get; set; }
    public int? SalespersonId { get; set; }
    public string? SalespersonName { get; set; }
    public int? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public int? ShipToId { get; set; }
    public string? ShipToName { get; set; }
    public int? ShipViaId { get; set; }
    public string? ShipViaName { get; set; }
    public string? CustomerPurchaseOrderNumber { get; set; }
    public decimal? FreightPerCase { get; set; }
    public decimal? FreightPerLoad { get; set; }
    public bool FreightIsActual { get; set; }
    public bool RequiresLabels { get; set; }
    public string? SpireNotes { get; set; }
    public string? GrowerItemNotes { get; set; }

    public List<FutureOrderCreateItem> Items { get; set; } = new();
    public List<FutureOrderCreateComment> Comments { get; set; } = new();
}

public class FutureOrderCreateItem
{
    public int SortOrder { get; set; }
    public int? VendorId { get; set; }
    public string? VendorName { get; set; }
    public int SpireInventoryId { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int OrderQuantity { get; set; }
    public bool HasPotCover { get; set; }
    public string? PotCover { get; set; }
    public string? DateCode { get; set; }
    public string? Upc { get; set; }
    public bool WeightsAndMeasures { get; set; }
    public string? Retail { get; set; }
    public string? Comments { get; set; }
    public bool IsApproximate { get; set; }
    public bool CreatePrebook { get; set; }
    public bool IsBlanket { get; set; }
    public int? BlanketItemId { get; set; }
    public decimal? UnitPrice { get; set; }
    public bool UseAvailabilityPricing { get; set; }
    public string? CustomerItemCode { get; set; }
    public bool UpgradeSheet { get; set; }
    public bool PhytoRequired { get; set; }
    public string? BoekestynPlantId { get; set; }
    public string? BoekestynCustomerAbbreviation { get; set; }
    public decimal UpgradeLabourHours { get; set; }
    public int? QuantityPerFinishedItem { get; set; }
    public decimal? SpecialPrice { get; set; }
    public string? GrowerItemNotes { get; set; }

    public List<FutureOrderCreateItemBoekestynProduct> BoekestynProducts { get; set; } = new();
}

public class FutureOrderCreateItemBoekestynProduct
{
    public string BoekestynPlantId { get; set; } = string.Empty;
    public string? BoekestynCustomerAbbreviation { get; set; }
    public int QuantityPerFinishedItem { get; set; }
}