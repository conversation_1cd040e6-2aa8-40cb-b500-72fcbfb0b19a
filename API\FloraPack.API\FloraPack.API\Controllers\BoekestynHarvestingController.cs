using FloraPack.API.Reports.Boekestyns;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.Boekestyn.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Boekestyns/Harvesting")]
public class BoekestynHarvestingController(IConfiguration configuration, HarvestingRepository harvestingRepository, CouchRepository couchRepository, StickingRepository stickingRepository) : FloraPackControllerBase(configuration)
{
    [HttpGet("")]
    public async Task<IActionResult> Harvesting()
    {
        var lines = await harvestingRepository.GetHarvestingLines();

        var response = new HarvestingResponse(lines);

        return Ok(response);
    }

    [HttpGet("Orders")]
    public async Task<IActionResult> HarvestingOrders([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var stickingWorkOrders = (await harvestingRepository.GetAdminOrderItems(startDate, endDate)).ToList();

        var ids = stickingWorkOrders
            .Select(o => o.OrderId)
            .ToArray();

        var rounds = (await harvestingRepository.GetHarvestingOrderRounds(ids)).ToLookup(h => h.OrderId);

        var harvestingOrders = (await couchRepository.BulkGet<HarvestingOrder>(ids.ToList())).Where(o => o != null).ToDictionary(o => o.Id);

        foreach (var order in stickingWorkOrders) {
            if (rounds.Contains(order.OrderId)) {
                order.Rounds.AddRange(rounds[order.OrderId]);
            }
            if(harvestingOrders.TryGetValue(order.OrderId, out var harvestingOrder)) {
                order.Varieties.AddRange(harvestingOrder.Varieties);
            }
        }

        var response = new HarvestingOrderResponse(stickingWorkOrders);

        return Ok(response);
    }

    [HttpGet("Schedules")]
    public async Task<IActionResult> HarvestingSchedules([FromQuery] DateTime date)
    {
        var schedules = await harvestingRepository.GetHarvestingSchedules(date);

        var response = new HarvestingSchedulesResponse(schedules);

        return Ok(response);
    }

    [HttpGet("Schedules/{orderId}")]
    public async Task<IActionResult> HarvestingSchedulesForOrderNumber(string orderId)
    {
        var order = await harvestingRepository.GetAdminOrderItem(orderId);
        if (order == null) {
            return NotFound();
        }

        var harvestingOrder = await couchRepository.GetHarvestingOrder(orderId);
        if (harvestingOrder == null) {
            return NotFound();
        }

        var rounds = await harvestingRepository.GetHarvestingOrderRounds([orderId]);

        order.Varieties.AddRange(harvestingOrder.Varieties);
        order.Rounds.AddRange(rounds);

        var schedules = await harvestingRepository.GetSchedulesForOrderId(orderId);

        var response = new HarvestingSchedulesForOrderResponse(schedules, order);

        return Ok(response);
    }

    [HttpGet("WorkOrders")]
    public async Task<IActionResult> GetHarvestingWorkOrders([FromQuery] DateTime date)
    {
        var workOrders = await harvestingRepository.GetHarvestingWorkOrders(date);
        var response = new HarvestingWorkOrdersResponse(workOrders);
        return Ok(response);
    }

    [HttpPost("WorkOrders")]
    public async Task<IActionResult> CreateWorkOrders(
        [FromBody] CreateWorkOrdersModel model)
    {
        await harvestingRepository.CreateWorkOrders(model);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Remove")]
    public async Task<IActionResult> RemoveHarvestingWorkOrder(int id)
    {
        await harvestingRepository.DeleteHarvestingWorkOrder(id);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Comment")]
    public async Task<IActionResult> UpdateHarvestingWorkOrderComment(int id, [FromBody] string? comment)
    {
        await harvestingRepository.UpdateHarvestingWorkOrderComment(id, comment);
        return Ok();
    }

    [HttpPost("WorkOrders/Sort")]
    public async Task<IActionResult> SortHarvestingWorkOrders([FromBody] SortHarvestingWorkOrdersModel model)
    {
        await harvestingRepository.SortHarvestingWorkOrders(model.WorkOrders);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Start")]
    public async Task<IActionResult> StartHarvestingWorkOrderLabour(int id, [FromBody] StartHarvestingWorkOrderLabourModel model)
    {
        await harvestingRepository.StartHarvestingLabour(id, model.CrewSize);
        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Pause")]
    public async Task<IActionResult> PauseHarvestingWorkOrderLabour(int id, [FromBody] StopHarvestingWorkOrderLabourModel model)
    {
        await harvestingRepository.StopHarvestingLabour(id, model.CrewSize, model.Comments, false);

        return Ok();
    }

    [HttpPost("WorkOrders/{id:int}/Labour/Stop")]
    public async Task<IActionResult> StopHarvestingWorkOrderLabour(int id, [FromBody] StopHarvestingWorkOrderLabourModel model)
    {
        await harvestingRepository.StopHarvestingLabour(id, model.CrewSize, model.Comments, model.HarvestComplete, model.LabourVarieties);

        if (model.HarvestComplete) {
            if (model.RemainderThrownOut) {
                await harvestingRepository.ThrowOutRemainder(id);
            } else if (model.RemainderSentBack) {
                await harvestingRepository.SendBackRemainder(id);
            } else if (model.RemainderNumberTwos) {
                await harvestingRepository.RemainderNumberTwos(id);
            }
        } else {
            await harvestingRepository.StartHarvestingLabour(id, model.CrewSize);
        }

        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("Labour/Download")]
    public async Task<IActionResult> DownloadLabour([FromQuery] DateTime start, [FromQuery] DateTime end)
    {
        var items = await harvestingRepository.GetHarvestingLabourReportItems(start, end);
        var report = HarvestingLabourReportFactory.CreateReport(start, end, items);
        Response.Headers.Append("Content-Disposition", "attachment;filename=SpacingLabour.xlsx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpGet("Labour")]
    public async Task<IActionResult> GetHarvestingLabour([FromQuery] string orderId)
    {
        var labour = await harvestingRepository.GetHarvestingWorkOrderLabour(orderId);
        var response = new HarvestingLabourResponse(labour);
        return Ok(response);
    }

    [HttpPost("TakeOut")]
    public async Task<IActionResult> TakeOutHarvesting([FromBody] HarvestTakeoutModel model)
    {
        await harvestingRepository.TakeOutHarvesting(model);
        return Ok();
    }

    private record HarvestingResponse(IEnumerable<HarvestingLine> Lines);
    private record HarvestingOrderResponse(IEnumerable<HarvestingAdminOrderItem> Orders);
    private record HarvestingSchedulesResponse(IEnumerable<HarvestingSchedule> Schedules);
    private record HarvestingSchedulesForOrderResponse(IEnumerable<HarvestingSchedule> Schedules, HarvestingAdminOrderItem Order);
    private record HarvestingWorkOrdersResponse(IEnumerable<HarvestingWorkOrderItem> Orders);
    private record HarvestingLabourResponse(IEnumerable<HarvestingWorkOrderLabourVarietyItem> Labour);
}

public class CreateWorkOrdersModel
{
    public string OrderId { get; set; } = string.Empty;
    public List<HarvestOrderToScheduleModel> Schedules { get; set; } = [];
}

public class HarvestOrderToScheduleModel
{
    public int Id { get; set; }
    public int LineId { get; set; }
    public DateTime Date { get; set; }
    
    public List<HarvestOrderToScheduleOrderModel> WorkOrders { get; set; } = [];
}

public class HarvestOrderToScheduleOrderModel
{
    public int Id { get; set; }
    public HarvestingAdminOrderItem Order { get; set; } = new();
    public int CrewSize { get; set; }
    public string? HarvestingComments { get; set; }
    public int DefaultExpectedHarvestPercentage { get; set; }
    public bool FinalRound { get; set; }
    public int StickingWorkOrderId { get; set; }

    public List<HarvestOrderToScheduleVarietyModel> Varieties { get; set; } = [];
}

public class HarvestOrderToScheduleVarietyModel
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Pots { get; set; }
    public int ExpectedHarvestPercentage { get; set; }
    public string? Comment { get; set; }
}

public class StartHarvestingWorkOrderLabourModel
{
    public int CrewSize { get; set; }
}

public class HarvestLabourVarieties
{
    public string VarietyName { get; set; } = string.Empty;
    public int Harvested { get; set; }
    public int ThrownOut { get; set; }
    public int NumberTwos { get; set; }
}

public class StopHarvestingWorkOrderLabourModel
{
    public int WorkOrderId { get; set; }
    public int CrewSize { get; set; }
    public string? Comments { get; set; }
    public List<HarvestLabourVarieties> LabourVarieties { get; set; } = [];
    public bool HarvestComplete { get; set; }
    public bool RemainderThrownOut { get; set; }
    public bool RemainderSentBack { get; set; }
    public bool RemainderNumberTwos { get; set; }
}

public class SortHarvestingWorkOrdersModel
{
    public List<HarvestingRepository.HarvestingWorkOrderSort> WorkOrders { get; init; } = [];
}

public class HarvestTakeoutModel
{
    public string LotNumber { get; set; } = String.Empty;
    public int Quantity { get; set; }
}