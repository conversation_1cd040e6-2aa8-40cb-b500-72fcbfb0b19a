﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Repositories.Holidays;

public class HolidayResponse
{
    public HolidayResponseProvince Province { get; set; } = new();

    public class HolidayResponseProvince
    {
        public string Id { get; set; } = string.Empty;
        [JsonPropertyName("nameEn")]
        public string Name { get; set; } = string.Empty;

        public string SourceLink { get; set; } = string.Empty;

        public List<Holiday> Holidays { get; set; } = [];
    }
}