﻿namespace FloraPack.API.Repositories.Phytos;

public class PhytoEmail
{
    public int Id { get; set; }
    public int FutureOrderId { get; set; }
    public DateTime RequiredDate { get; set; }
    public string? BoxCode { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string From { get; set; } = string.Empty;

    public List<PhytoEmailItem> Items { get; set; } = new();
}