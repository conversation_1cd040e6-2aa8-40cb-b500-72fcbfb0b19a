﻿using System.Data;
using Dapper;
using FloraPack.API.Repositories.Boekestyn;

namespace FloraPack.API.Repositories.Prebooks;

public class PrebookRepository(IConfiguration configuration) : RepositoryBase(configuration)
{
    public async Task<IEnumerable<PrebookListItem>> List(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);
        return await GetConnection().QueryAsync<PrebookListItem>("SELECT * FROM prebook_list(@required_start_date, @required_end_date)", parameters);
    }

    public async Task<IEnumerable<PrebookSummaryItem>> ItemSummary(DateTime? startDate, DateTime? endDate, bool groupByCustomer,
        bool groupByShipTo, bool groupByVendor, bool groupBySalesperson, bool groupBySeason, bool groupByIsBlanket, string dateGrouping)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);
        parameters.Add("group_by_customer", groupByCustomer, DbType.Boolean);
        parameters.Add("group_by_ship_to", groupByShipTo, DbType.Boolean);
        parameters.Add("group_by_vendor", groupByVendor, DbType.Boolean);
        parameters.Add("group_by_salesperson", groupBySalesperson, DbType.Boolean);
        parameters.Add("group_by_season", groupBySeason, DbType.Boolean);
        parameters.Add("group_by_is_blanket", groupByIsBlanket, DbType.Boolean);
        parameters.Add("date_grouping", dateGrouping, DbType.String);
        return await GetConnection().QueryAsync<PrebookSummaryItem>("SELECT * FROM prebook_item_summary(" +
                                                                    "@required_start_date, @required_end_date, @group_by_customer, @group_by_ship_to, " +
                                                                    "@group_by_vendor, @group_by_salesperson, @group_by_season, @group_by_is_blanket, " +
                                                                    "@date_grouping)", parameters);
    }

    public async Task<IEnumerable<BlanketItemListItem>> BlanketList(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);
        var items = (await GetConnection().QueryAsync<BlanketItemListItem>("SELECT * FROM blanket_item_list(@required_start_date, @required_end_date)", parameters)).ToList();
        var bookingItems = (await GetConnection().QueryAsync<BlanketItemListBookingItem>("SELECT * FROM blanket_item_list_bookings(@required_start_date, @required_end_date)", parameters)).ToLookup(i => i.BlanketItemId);

        foreach (var item in items.Where(i => bookingItems.Contains(i.Id)))
        {
            item.BookingItems.AddRange(bookingItems[item.Id]);
        }
        return items;
    }

    public async Task<IEnumerable<PrebookBlanketItem>> BlanketItems(int? id = null)
    {
        var parameters = new DynamicParameters();
        parameters.Add("param_prebook_id", id, DbType.Int32);

        return await GetConnection().QueryAsync<PrebookBlanketItem>("SELECT * FROM prebook_blanket_items(@param_prebook_id)", parameters);
    }

    public async Task<PrebookDetail?> Detail(int id)
    {
        await using var multi = await GetConnection().QueryMultipleAsync(@"
            SELECT * FROM prebooks WHERE id = @id;
            SELECT pi.*, fi.future_order_id FROM prebook_items pi LEFT JOIN future_order_items fi ON pi.future_order_item_id = fi.id WHERE pi.prebook_id = @id ORDER BY pi.id;
            SELECT o.* FROM prebook_items i JOIN prebook_item_blanket_options o ON i.id = o.prebook_item_id WHERE i.prebook_id = @id;
            SELECT b.* FROM prebook_items i JOIN prebook_item_boekestyn_products b ON i.id = b.prebook_item_id WHERE i.prebook_id = @id;
        ", new { id });
        var detail = await multi.ReadFirstOrDefaultAsync<PrebookDetail>();
        if (detail != null) {
            detail.Items.AddRange(await multi.ReadAsync<PrebookDetailItem>());
            var options = (await multi.ReadAsync<PrebookDetailItemBlanketOption>()).ToLookup(i => i.PrebookItemId);
            if (detail.IsBlanket) {
                foreach (var item in detail.Items.Where(i => options.Contains(i.Id))) {
                    item.BlanketOptions.AddRange(options[item.Id]);
                }
            }

            var boekestynProducts =
                (await multi.ReadAsync<PrebookItemBoekestynProduct>()).ToLookup(i => i.PrebookItemId);
            foreach (var item in detail.Items.Where(i => boekestynProducts.Contains(i.Id))) {
                item.BoekestynProducts.AddRange(boekestynProducts[item.Id]);
            }
        }

        return detail;
    }

    public async Task<IEnumerable<PrebookItemWithPrebookId>> ItemDetailForFutureOrderItemId(int id) =>
        await GetConnection().QueryAsync<PrebookItemWithPrebookId>("SELECT * FROM prebook_items WHERE future_order_item_id = @id;", new { id });

    public async Task Confirm(int id, string who) =>
        await GetConnection().ExecuteAsync("UPDATE prebooks SET confirmed = @now, confirmed_by = @who, modified = @now, modified_by = @who WHERE id = @id",
            new { id, now = DateTime.Now, who });

    public async Task Unconfirm(int id, string who) =>
        await GetConnection().ExecuteAsync("UPDATE prebooks SET confirmed = null, confirmed_by = null, modified = @now, modified_by = @who WHERE id = @id",
            new { id, now = DateTime.Now, who });


    public async Task SetSpirePurchaseOrder(int id, int spirePurchaseOrderId, string spirePurchaseOrderNumber) =>
        await GetConnection().ExecuteAsync(
                "UPDATE prebooks SET spire_purchase_order_id = @spirePurchaseOrderId, spire_purchase_order_po_number = @spirePurchaseOrderNumber WHERE id = @id;",
            new { id, spirePurchaseOrderId, spirePurchaseOrderNumber });

    public async Task<int> Create(PrebookCreate prebook, string? who)
    {
        prebook.Who = who;
        var now = DateTime.Now;
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();
        var sql =
            @"INSERT INTO prebooks (name, required_date, is_blanket, blanket_start_date, blanket_is_closed, season_name, box_code, vendor_id, vendor_name,
                        salesperson_id, salesperson_name, customer_id, customer_name, ship_to_id, ship_to_name, comments, grower_item_notes, created, created_by, modified, modified_by)
                    VALUES (@name, @requiredDate, @isBlanket, @blanketStartDate, @blanketIsClosed, @seasonName, @boxCode, @vendorId, @vendorName,
                        @salespersonId, @salespersonName, @customerId, @customerName, @shipToId, @shipToName, @comments, @growerItemNotes, @now, @who, @now, @who)
                RETURNING id;";
        var prebookParams = new {
            prebook.Name,
            prebook.RequiredDate,
            prebook.IsBlanket,
            prebook.BlanketStartDate,
            prebook.BlanketIsClosed,
            prebook.SeasonName,
            prebook.BoxCode,
            prebook.VendorId,
            prebook.VendorName,
            prebook.SalespersonId,
            prebook.SalespersonName,
            prebook.CustomerId,
            prebook.CustomerName,
            prebook.ShipToId,
            prebook.ShipToName,
            prebook.Comments,
            prebook.GrowerItemNotes,
            now,
            who
        };
        var id = await connection.ExecuteScalarAsync<int>(sql, prebookParams, transaction: tx);

        var itemSql =
            @"INSERT INTO prebook_items(prebook_id, spire_inventory_id, spire_part_number, description, order_quantity,
                        has_pot_cover, pot_cover, date_code, upc, weights_and_measures, retail, comments, is_approximate, blanket_item_id, blanket_week_id,
                        upgrade_sheet, boekestyn_plant_id, boekestyn_customer_abbreviation, upgrade_labour_hours, upgrade_product_coming_from,
                        boekestyn_quantity_per_finished_item, special_price, grower_item_notes)
                    VALUES (@id, @spireInventoryId, @spirePartNumber, @description, @orderQuantity,
                        @hasPotCover, @potCover, @dateCode, @upc, @weightsAndMeasures, @retail, @comments, @isApproximate, @blanketItemId, @blanketWeekId,
                        @upgradeSheet, @boekestynPlantId, @boekestynCustomerAbbreviation, @upgradeLabourHours, @vendorName, @quantityPerFinishedItem, @specialPrice, @growerItemNotes)
                    RETURNING id;";
        var boekestynProductSql =
            @"INSERT INTO prebook_item_boekestyn_products (prebook_item_id, boekestyn_plant_id, boekestyn_customer_abbreviation, quantity_per_finished_item) 
                VALUES (@itemId, @boekestynPlantId, @boekestynCustomerAbbreviation, @quantityPerFinishedItem);";
        var blanketSql =
            @"INSERT INTO prebook_item_blanket_options(prebook_item_id, spire_inventory_id, spire_part_number, description)
                                    VALUES (@itemId, @spireInventoryId, @spirePartNumber, @description);";

        foreach (var item in prebook.Items) {
            // don't include the over & above comment to boeks
            var itemComments = item.Comments;
            if (prebook.VendorId == Constants.BoekestynVendorId) {
                itemComments = itemComments?.Replace(Constants.OverAndAboveBlanketComment, "");
            }
            var itemParams = new {
                id,
                item.SpireInventoryId,
                item.SpirePartNumber,
                item.Description,
                item.OrderQuantity,
                item.HasPotCover,
                item.PotCover,
                item.DateCode,
                item.Upc,
                item.WeightsAndMeasures,
                item.Retail,
                comments = itemComments,
                item.IsApproximate,
                item.BlanketItemId,
                item.BlanketWeekId,
                item.UpgradeSheet,
                item.BoekestynPlantId,
                item.BoekestynCustomerAbbreviation,
                item.UpgradeLabourHours,
                prebook.VendorName,
                item.QuantityPerFinishedItem,
                item.SpecialPrice,
                item.GrowerItemNotes
            };
            var itemId = await connection.ExecuteScalarAsync<int>(itemSql, itemParams, transaction: tx);

            if (item.BoekestynProducts.Any()) {
                await connection.ExecuteAsync(boekestynProductSql,
                    item.BoekestynProducts.Select(p => new
                        { itemId, p.BoekestynPlantId, p.BoekestynCustomerAbbreviation, p.QuantityPerFinishedItem }),
                    transaction: tx);
            }

            if (item.BlanketOptions.Any()) {
                var optionParams = item.BlanketOptions.Select(option => new
                    { itemId, option.SpireInventoryId, option.SpirePartNumber, option.Description });
                await connection.ExecuteAsync(blanketSql, optionParams, transaction: tx);
            }
        }

        await tx.CommitAsync();

        if (prebook.ShipToId.HasValue && !prebook.IsBlanket) {
            var defaultParams = prebook.Items
                .Select(i => new {
                    spireinventoryid = i.SpireInventoryId,
                    shiptoid = prebook.ShipToId,
                    haspotcover = i.HasPotCover,
                    potcover = i.PotCover,
                    upc = i.Upc,
                    weightsandmeasures = i.WeightsAndMeasures,
                    retail = i.Retail
                });
            await connection.ExecuteAsync(
                "SELECT set_product_ship_to_defaults(@spireinventoryid, @shiptoid, @haspotcover, @potcover, @upc, @weightsandmeasures, @retail)",
                defaultParams);
        }

        return id;
    }

    public async Task Update(PrebookDetail prebook, string? who, Dictionary<int, int>? futureOrderItemMap = null)
    {
        string sql;
        var now = DateTime.Now;
        var inserts = prebook.Items.Where(i => i.Id <= 0).ToList();
        var updates = prebook.Items.Where(i => i.Id > 0).ToList();

        var updateParams = updates.Select(item => new {
            item.Id,
            item.SpireInventoryId,
            item.SpirePartNumber,
            item.Description,
            item.OrderQuantity,
            item.HasPotCover,
            item.PotCover,
            item.DateCode,
            item.Upc,
            item.WeightsAndMeasures,
            item.Retail,
            item.Comments,
            item.IsApproximate,
            item.BlanketItemId,
            item.BlanketWeekId,
            item.UpgradeSheet,
            item.BoekestynPlantId,
            item.BoekestynCustomerAbbreviation,
            item.UpgradeLabourHours,
            item.QuantityPerFinishedItem,
            item.SpecialPrice,
            item.GrowerItemNotes
        });

        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        if (updates.Any()) {
            var itemIds = string.Join(", ", updates.Select(i => i.Id));
            sql = $"DELETE FROM prebook_item_blanket_options WHERE prebook_item_id IN ({itemIds});\n" +
                  $"DELETE FROM prebook_item_boekestyn_products WHERE prebook_item_id IN ({itemIds});";
            await connection.ExecuteAsync(sql, transaction: tx);

            // if anything has changed, un-confirm it
            sql = @"UPDATE prebook_items
                SET
                    upgrade_confirmed = null, upgrade_confirmed_by = null
                WHERE
                    id = @id
                    AND (
                        coalesce(spire_inventory_id, 0) <> coalesce(@spireInventoryId, 0) OR 
                        coalesce(spire_part_number, '') <> coalesce(@spirePartNumber, '') OR 
                        coalesce(description, '') <> coalesce(@description, '') OR 
                        coalesce(order_quantity, 0) <> coalesce(@orderQuantity, 0) OR 
                        has_pot_cover <> @hasPotCover OR 
                        coalesce(pot_cover, '') <> coalesce(@potCover, '') OR 
                        coalesce(comments, '') <> coalesce(@comments, '') OR 
                        upgrade_sheet <> @upgradeSheet OR
                        coalesce(upgrade_labour_hours, 0) <> @upgradeLabourHours OR
                        coalesce(boekestyn_quantity_per_finished_item, 1) <> coalesce(@quantityPerFinishedItem, 1)
                    );";
            await connection.ExecuteAsync(sql, updateParams, transaction: tx);

            // also un-confirm it if the required date has changed
            sql = @"UPDATE prebook_items
                SET
                    upgrade_confirmed = null, upgrade_confirmed_by = null
                FROM
                    prebooks p
                WHERE
                    p.id = @id
                    AND prebook_items.prebook_id = p.id
                    AND p.required_date <> @requiredDate;";
            var updated = await connection.ExecuteAsync(sql, new { prebook.Id, prebook.RequiredDate }, transaction: tx);
        }

        sql = @"UPDATE prebooks
                    SET
                        name = @name, required_date = @requiredDate, is_blanket = @isBlanket, blanket_start_date = @blanketStartDate, blanket_is_closed = @blanketIsClosed,
                        season_name = @seasonName, box_code = @boxCode, vendor_id = @vendorId, vendor_name = @vendorName,
                        salesperson_id = @salespersonId, salesperson_name = @salespersonName, customer_id = @customerId, customer_name = @customerName,
                        ship_to_id = @shipToId, ship_to_name = @shipToName, comments = @comments, grower_item_notes = @growerItemNotes, modified = @now, modified_by = @who
                    WHERE
                        id = @id;";
        var prebookParams = new {
            prebook.Id,
            prebook.Name,
            prebook.RequiredDate,
            prebook.IsBlanket,
            prebook.BlanketStartDate,
            prebook.BlanketIsClosed,
            prebook.SeasonName,
            prebook.BoxCode,
            prebook.VendorId,
            prebook.VendorName,
            prebook.SalespersonId,
            prebook.SalespersonName,
            prebook.CustomerId,
            prebook.CustomerName,
            prebook.ShipToId,
            prebook.ShipToName,
            prebook.Comments,
            prebook.GrowerItemNotes,
            now,
            who
        };
        await connection.ExecuteAsync(sql, prebookParams, transaction: tx);

        sql = "DELETE FROM prebook_items WHERE prebook_id = @id";
        if (updates.Any()) {
            var submitted = string.Join(", ", updates.Select(i => i.Id));
            sql += $" AND id NOT IN ({submitted})";
        }

        sql += ";";
        var deleteParams = new { prebook.Id };
        await connection.ExecuteAsync(sql, deleteParams, transaction: tx);

        var itemSql =
            @"INSERT INTO prebook_items(prebook_id, spire_inventory_id, spire_part_number, description, order_quantity, has_pot_cover, pot_cover,
                        date_code, upc, weights_and_measures, retail, comments, is_approximate, blanket_item_id, blanket_week_id, upgrade_sheet, future_order_item_id,
                        boekestyn_plant_id, boekestyn_customer_abbreviation, upgrade_labour_hours, upgrade_product_coming_from,
                        boekestyn_quantity_per_finished_item, special_price, grower_item_notes)
                VALUES (@id, @spireInventoryId, @spirePartNumber, @description, @orderQuantity, @hasPotCover, @potCover,
                    @dateCode, @upc, @weightsAndMeasures, @retail, @comments, @isApproximate, @blanketItemId, @blanketWeekId, @upgradeSheet, @futureOrderItemId,
                    @boekestynPlantId, @boekestynCustomerAbbreviation, @upgradeLabourHours, @vendorName, @quantityPerFinishedItem, @specialPrice, @growerItemNotes) RETURNING id;";
        var boekestynProductSql =
            @"INSERT INTO prebook_item_boekestyn_products (prebook_item_id, boekestyn_plant_id, boekestyn_customer_abbreviation, quantity_per_finished_item) 
                VALUES (@itemId, @boekestynPlantId, @boekestynCustomerAbbreviation, @quantityPerFinishedItem);";
        var blanketSql =
            @"INSERT INTO prebook_item_blanket_options(prebook_item_id, spire_inventory_id, spire_part_number, description)
                                VALUES (@itemId, @spireInventoryId, @spirePartNumber, @description);";

        if (inserts.Any()) {
            foreach (var item in inserts) {

                var futureOrderItemId = item.FutureOrderItemId == null
                    ? default(int?)
                    : futureOrderItemMap?.ContainsKey(item.FutureOrderItemId.Value) == true
                        ? futureOrderItemMap[item.FutureOrderItemId.Value]
                        : default;

                // don't include the over & above comment to boeks
                var itemComments = item.Comments;
                if (prebook.VendorId == Constants.BoekestynVendorId) {
                    itemComments = itemComments?.Replace(Constants.OverAndAboveBlanketComment, "");
                }

                var insertParam = new {
                    prebook.Id,
                    item.SpireInventoryId,
                    item.SpirePartNumber,
                    item.Description,
                    item.OrderQuantity,
                    item.HasPotCover,
                    item.PotCover,
                    item.DateCode,
                    item.Upc,
                    item.WeightsAndMeasures,
                    item.Retail,
                    comments = itemComments,
                    item.IsApproximate,
                    item.BlanketItemId,
                    item.BlanketWeekId,
                    item.UpgradeSheet,
                    futureOrderItemId,
                    item.BoekestynPlantId,
                    item.BoekestynCustomerAbbreviation,
                    item.UpgradeLabourHours,
                    prebook.VendorName,
                    item.QuantityPerFinishedItem,
                    item.SpecialPrice,
                    item.GrowerItemNotes
                };
                var itemId = await connection.ExecuteScalarAsync<int>(itemSql, insertParam, transaction: tx);

                if (item.BoekestynProducts.Any()) {
                    var boekestynParams = item.BoekestynProducts.Select(p => new
                        { itemId, p.BoekestynPlantId, p.BoekestynCustomerAbbreviation, p.QuantityPerFinishedItem });
                    await connection.ExecuteAsync(boekestynProductSql, boekestynParams, transaction: tx);
                }

                if (item.BlanketOptions.Any()) {
                    var blanketParams = item.BlanketOptions.Select(option => new
                        { itemId, option.SpireInventoryId, option.SpirePartNumber, option.Description });
                    await connection.ExecuteAsync(blanketSql, blanketParams, transaction: tx);
                }
            }
        }

        if (updates.Any()) {
            sql = @"UPDATE prebook_items
                SET
                    spire_inventory_id = @spireInventoryId, spire_part_number = @spirePartNumber, description = @description, order_quantity = @orderQuantity,
                    has_pot_cover = @hasPotCover, pot_cover = @potCover, date_code = @dateCode, upc = @upc, weights_and_measures = @weightsAndMeasures, retail = @retail,
                    comments = @comments, is_approximate = @isApproximate, blanket_item_id = @blanketItemId, blanket_week_id = @blanketWeekId, upgrade_sheet = @upgradeSheet, 
                    boekestyn_plant_id = @boekestynPlantId, boekestyn_customer_abbreviation = @boekestynCustomerAbbreviation,
                    upgrade_labour_hours = @upgradeLabourHours, boekestyn_quantity_per_finished_item = @quantityPerFinishedItem, special_price = @specialPrice, grower_item_notes = @growerItemNotes
                WHERE
                    id = @id;";

            await connection.ExecuteAsync(sql, updateParams, transaction: tx);
        }

        var boekestynProducts = updates
            .Aggregate(new List<PrebookItemBoekestynProduct>(), (list, item) => {
                list.AddRange(item.BoekestynProducts);
                return list;
            });

        if (boekestynProducts.Any()) {
            var boekestynParams = boekestynProducts.Select(product => new {
                product.PrebookItemId,
                product.BoekestynPlantId,
                product.BoekestynCustomerAbbreviation,
                product.QuantityPerFinishedItem
            });
            var boekestynSql =
                @"INSERT INTO prebook_item_boekestyn_products (prebook_item_id, boekestyn_plant_id, boekestyn_customer_abbreviation, quantity_per_finished_item) 
                VALUES (@prebookItemId, @boekestynPlantId, @boekestynCustomerAbbreviation, @quantityPerFinishedItem);";

            await connection.ExecuteAsync(boekestynSql, boekestynParams, transaction: tx);
        }

        var blanketOptions = updates
            .Aggregate(new List<PrebookDetailItemBlanketOption>(), (list, item) => {
                list.AddRange(item.BlanketOptions);
                return list;
            });

        if (blanketOptions.Any()) {
            var blanketParams = blanketOptions.Select(option => new {
                option.PrebookItemId,
                option.SpireInventoryId,
                option.SpirePartNumber,
                option.Description
            });
            sql =
                @"INSERT INTO prebook_item_blanket_options (prebook_item_id, spire_inventory_id, spire_part_number, description) 
                        VALUES (@prebookItemId, @spireInventoryId, @spirePartNumber, @description);";
            await connection.ExecuteAsync(sql, blanketParams, transaction: tx);
        }

        await tx.CommitAsync();

        if (prebook.ShipToId.HasValue && !prebook.IsBlanket) {
            var defaultParams = prebook.Items
                .Select(i => new {
                    spireinventoryid = i.SpireInventoryId,
                    shiptoid = prebook.ShipToId,
                    haspotcover = i.HasPotCover,
                    potcover = i.PotCover,
                    upc = i.Upc,
                    weightsandmeasures = i.WeightsAndMeasures,
                    retail = i.Retail
                });
            await connection.ExecuteAsync(
                "SELECT set_product_ship_to_defaults(@spireinventoryid, @shiptoid, @haspotcover, @potcover, @upc, @weightsandmeasures, @retail)",
                defaultParams);
        }
    }

    public async Task<List<PrebookDetail>> DetailsForFutureOrder(int futureOrderId)
    {
        await using var connection = GetConnection();
        await using var spireConnection = GetSpireConnection();
        var prebooks = (await connection.QueryAsync<PrebookDetail>(
            "SELECT * FROM prebooks WHERE future_order_id = @futureOrderId ORDER BY vendor_name;",
            new { futureOrderId })).ToList();

        // get the prebooks for split orders. the future_order_id of the prebook won't match this future order, but the future_order_item_id will match an item.
        var splitPrebooks = (await connection.QueryAsync<PrebookDetail>(
                       "select p.* from prebooks p join prebook_items pi on p.id = pi.prebook_id join future_order_items fi on pi.future_order_item_id = fi.id where fi.future_order_id = @futureOrderId;",
                                  new { futureOrderId })).ToList();
        
        splitPrebooks
            .Aggregate(new List<PrebookDetail>(), (list, p) => {
                if (!prebooks.Any(p2 => p2.Id == p.Id) && !list.Any(p2 => p2.Id == p.Id)) {
                    list.Add(p);
                }
                return list;
            })
            .ForEach(prebooks.Add);

        foreach (var prebook in prebooks) {
            await using var multi = await connection.QueryMultipleAsync(@"
        SELECT pi.*, fi.future_order_id FROM prebook_items pi LEFT JOIN future_order_items fi ON pi.future_order_item_id = fi.id WHERE pi.prebook_id = @id ORDER BY pi.id;
        SELECT o.* FROM prebook_items i JOIN prebook_item_blanket_options o ON i.id = o.prebook_item_id WHERE i.prebook_id = @id;
        SELECT b.* FROM prebook_items i JOIN prebook_item_boekestyn_products b ON i.id = b.prebook_item_id WHERE i.prebook_id = @id;
    ", new { prebook.Id });
            prebook.Items.AddRange(await multi.ReadAsync<PrebookDetailItem>());
            var options = (await multi.ReadAsync<PrebookDetailItemBlanketOption>()).ToLookup(i => i.PrebookItemId);
            if (prebook.IsBlanket) {

                foreach (var item in prebook.Items.Where(i => options.Contains(i.Id))) {
                    item.BlanketOptions.AddRange(options[item.Id]);
                }
            }

            var boekestynProducts =
                (await multi.ReadAsync<PrebookItemBoekestynProduct>()).ToLookup(i => i.PrebookItemId);
            foreach (var item in prebook.Items.Where(i => boekestynProducts.Contains(i.Id))) {
                item.BoekestynProducts.AddRange(boekestynProducts[item.Id]);
            }

            if (prebook.VendorId == Constants.BoekestynVendorId || prebook.VendorId == Constants.UpgradesVendorId) {
                var purchaseOrderItems = (await spireConnection.QueryAsync<PrebookPurchaseOrderItem>(
                    @"select
    cast(soi.udf_data::json ->> 'prebook_id' as integer) as prebook_id,
    coalesce(cast(poi.udf_data::json ->> 'priority' as boolean), False) as priority,
    case when length(poi.udf_data::json ->> 'upc_complete') > 0 then poi.udf_data::json ->> 'upc_complete' else null end as upc_printed,
    poi.part_no as spire_part_number,
    poi.id as purchase_order_item_id
from
    sales_order_items soi
    join inventory_requisitions ir on soi.guid = ir.src_guid
    join purchase_order_items poi on ir.targ_guid = poi.guid
    join purchase_orders po on poi.po_number = po.po_number
where
    length(soi.udf_data::json ->> 'prebook_id') > 0
    and cast(soi.udf_data::json ->> 'prebook_id' as integer) = @id;", new { prebook.Id })).ToList();
                foreach (var boekestynItem in prebook.Items) {
                    var priority = purchaseOrderItems.Any(p => p.SpirePartNumber == boekestynItem.SpirePartNumber && p.Priority);
                    if (priority && !boekestynItem.BoekPriority) {
                        await connection.ExecuteAsync("UPDATE prebook_items SET boek_priority = True WHERE id = @id", new { boekestynItem.Id });
                        boekestynItem.BoekPriority = priority;
                    }

                    var upcPrinted = purchaseOrderItems.FirstOrDefault(p => p.SpirePartNumber == boekestynItem.SpirePartNumber)?.UpcPrinted;
                    // if the PO was printed, and the item hasn't been previously printed, set it as printed.
                    if (upcPrinted != null && boekestynItem.UpcPrinted == null && boekestynItem.UpcPrintedPrev == null) {
                        await connection.ExecuteAsync("UPDATE prebook_items SET upc_printed = @upcPrinted WHERE id = @id", new { boekestynItem.Id, upcPrinted });
                        boekestynItem.UpcPrinted = upcPrinted;
                    }
                }
            }
        }

        return prebooks;
    }

    public async Task Delete(int id, string who, bool deleteItems = false)
    {
        await GetConnection().ExecuteAsync("UPDATE prebooks SET deleted = @now, deleted_by = @who WHERE id = @id",
            new { id, now = DateTime.Now, who });

        if (deleteItems) {
            await GetConnection().ExecuteAsync("DELETE FROM prebook_items WHERE prebook_id = @id", new { id });
        }
    }

    public async Task<IEnumerable<Season>> GetSeasons() =>
        await GetConnection().QueryAsync<Season>("SELECT name, to_char(season_date, 'YYYY-MM-DD') AS SeasonDate FROM seasons ORDER BY name;");

    public async Task CreateSeason(string season, DateTime seasonDate) =>
        await GetConnection().ExecuteAsync("INSERT INTO seasons (name, season_date) VALUES (@season, @seasonDate);", new { season, seasonDate });

    public async Task UpdateUpgradeItemProperty(int id, string propertyName, string? value) =>
        await GetConnection().ExecuteAsync($"UPDATE prebook_items SET {propertyName} = @value WHERE id = @id;",
            new { id, value });

    public async Task UpdateUpgradeItemProperty(int id, string propertyName, decimal? value) =>
        await GetConnection().ExecuteAsync($"UPDATE prebook_items SET {propertyName} = @value WHERE id = @id;",
            new { id, value });

    public async Task UpdateUpgradeItemProperty(int id, string propertyName, bool value) =>
        await GetConnection().ExecuteAsync($"UPDATE prebook_items SET {propertyName} = @value WHERE id = @id;",
            new { id, value });

    public async Task ConfirmUpgradeItem(int id, string? who) =>
        await GetConnection().ExecuteAsync(
            "UPDATE prebook_items " +
            "SET upgrade_confirmed_by = @who, upgrade_confirmed = @now " +
            "WHERE id = @id;", new { id, who, now = DateTime.Now });

    public async Task<IEnumerable<OverAndAboveItem>> OverAndAbove(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);
        return await GetConnection().QueryAsync<OverAndAboveItem>("SELECT * FROM over_and_above_blankets(" +
                                                                  "@required_start_date, @required_end_date)", parameters);
    }

    public async Task SetPrebookItemPriority(int id, bool priority) =>
        await GetConnection().ExecuteAsync($"update prebook_items set boek_priority = @priority where id = @id;", new { id, priority });
    

    public async Task SetPrebookItemUpcComplete(int id) =>
        await GetConnection().ExecuteAsync("update prebook_items set upc_printed = current_timestamp where id = @id;", new { id });

    public async Task SetPrebookItemUpcUncomplete(int id) =>
        await GetConnection().ExecuteAsync("update prebook_items set upc_printed = null where id = @id;", new { id });

    public async Task SetPrebookItemUpcOverride(int id) =>
        await GetConnection().ExecuteAsync("update prebook_items set upc_printed_prev = upc_printed, upc_printed = null where id = @id;", new { id });

    public async Task SetPrebookItemUpcOverrideMultiple(IEnumerable<int> prebookItemIds) =>
        await GetConnection()
            .ExecuteAsync("update prebook_items set upc_printed_prev = upc_printed, upc_printed = null where id = @id;",
                prebookItemIds.Select(id => new { id }));

    public class PrebookItemWithPrebookId : PrebookDetailItem
    {
        public int PrebookId { get; set; }
    }

    public async Task<IEnumerable<PrebookItemAttachment>> AttachmentList(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);

        return await GetConnection().QueryAsync<PrebookItemAttachment>(
            "SELECT * FROM prebook_item_attachment_list(@required_start_date, @required_end_date);", parameters);
    }

    public async Task<IEnumerable<PrebookItemAttachment>> GetAttachmentsForPrebook(int id) =>
        await GetConnection().QueryAsync<PrebookItemAttachment>(
            "SELECT a.* FROM prebook_item_attachments a JOIN prebook_items i on a.prebook_item_id = i.id WHERE i.prebook_id = @id;",
            new { id });

    public async Task<IEnumerable<PrebookItemAttachment>> CreateAttachment(string filename, string fileType,
        List<int> prebookItemIds)
    {
        await using var connection = GetConnection();
        var attachments = new List<PrebookItemAttachment>();
        foreach (var id in prebookItemIds) {
            var created = await connection.QueryFirstAsync<PrebookItemAttachment>(
                "INSERT INTO prebook_item_attachments (prebook_item_id, filename, filetype) VALUES (@id, @filename, @fileType) returning *;",
                new { filename, fileType, id });
            attachments.Add(created);
        }

        return attachments;
    }

    public async Task<PrebookItemAttachment?> GetAttachment(int id) =>
        await GetConnection().QueryFirstOrDefaultAsync<PrebookItemAttachment>("SELECT * FROM prebook_item_attachments WHERE id = @id;", new { id });

    public async Task DeleteAttachment(int id) =>
        await GetConnection().ExecuteAsync("DELETE FROM prebook_item_attachments WHERE id = @id;", new { id });
}