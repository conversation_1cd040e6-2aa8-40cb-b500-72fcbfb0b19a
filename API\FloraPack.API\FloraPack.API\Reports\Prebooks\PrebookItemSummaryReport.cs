﻿using ClosedXML.Excel;
using static FloraPack.API.Controllers.PrebooksController;

namespace FloraPack.API.Reports.Prebooks;

public static class PrebookItemSummaryReport
{
    public static MemoryStream Create(ItemSummaryReportModel model)
    {
        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 1;
        var col = 1;
        sheet.Cell(row, col).SetValue("Part No");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Description");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        if (model.DateGrouping == "Year")
        {
            sheet.Cell(row, col).SetValue("Year");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }
        else if (model.DateGrouping == "Month")
        {
            sheet.Cell(row, col).SetValue("Month");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }
        else if (model.DateGrouping == "Week")
        {
            sheet.Cell(row, col).SetValue("Week");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }
        else if (model.DateGrouping == "Date")
        {
            sheet.Cell(row, col).SetValue("Date");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        if (model.GroupByVendor)
        {
            sheet.Cell(row, col).SetValue("Vendor");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        if (model.GroupBySalesperson)
        {
            sheet.Cell(row, col).SetValue("Salesperson");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        if (model.GroupByCustomer)
        {
            sheet.Cell(row, col).SetValue("Customer");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        if (model.GroupByShipTo)
        {
            sheet.Cell(row, col).SetValue("Ship To");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        if (model.GroupBySeason)
        {
            sheet.Cell(row, col).SetValue("Season");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        if (model.GroupByIsBlanket)
        {
            sheet.Cell(row, col).SetValue("Blanket");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        sheet.Cell(row, col).SetValue("Cases");
        sheet.Cell(row, col).Style.Font.Bold = true;

        sheet.Range(row, 1, row, col).Style.Border.BottomBorder = XLBorderStyleValues.Thin;

        row++;
        foreach (var item in model.Items)
        {
            col = 1;

            sheet.Cell(row, col).SetValue(item.SpirePartNumber);
            col++;
            sheet.Cell(row, col).SetValue(item.Description);
            col++;

            if (model.DateGrouping == "Year")
            {
                sheet.Cell(row, col).SetValue(item.Year);
                col++;
            }
            else if (model.DateGrouping == "Month")
            {
                sheet.Cell(row, col).SetValue($"{item.Month}, {item.Year}");
                col++;
            }
            else if (model.DateGrouping == "Week")
            {
                sheet.Cell(row, col).SetValue($"{item.Week}, {item.WeekYear}");
                col++;
            }
            else if (model.DateGrouping == "Date")
            {
                sheet.Cell(row, col).SetValue(item.Date);
                col++;
            }

            if (model.GroupByVendor)
            {
                sheet.Cell(row, col).SetValue(item.Vendor);
                col++;
            }

            if (model.GroupBySalesperson)
            {
                sheet.Cell(row, col).SetValue(item.Salesperson);
                col++;
            }

            if (model.GroupByCustomer)
            {
                sheet.Cell(row, col).SetValue(item.Customer);
                col++;
            }

            if (model.GroupByShipTo)
            {
                sheet.Cell(row, col).SetValue(item.ShipTo);
                col++;
            }

            if (model.GroupBySeason)
            {
                sheet.Cell(row, col).SetValue(item.Season);
                col++;
            }

            if (model.GroupByIsBlanket)
            {
                sheet.Cell(row, col).SetValue(item.IsBlanket ? "Yes" : "No");
                col++;
            }

            sheet.Cell(row, col).SetValue(item.OrderQuantity);
            sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");

            row++;
        }

        sheet.Cell(row, col).FormulaR1C1 = $"=SUM(R[-{model.Items.Count}]C:R[-1]C)";
        sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");


        sheet.Range(row, 1, row, col).Style.Border.TopBorder = XLBorderStyleValues.Double;
        sheet.Range(row, 1, row, col).Style.Font.Bold = true;

        sheet.Columns(1, col).AdjustToContents();

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }
}