﻿namespace FloraPack.API.Repositories.Prebooks;

public class PrebookCreate
{
    public string? Name { get; set; }
    public DateTime? RequiredDate { get; set; }
    public bool IsBlanket { get; set; }
    public DateTime? BlanketStartDate { get; set; }
    public bool BlanketIsClosed { get; set; }
    public string? SeasonName { get; set; }
    public string? BoxCode { get; set; }
    public int? VendorId { get; set; }
    public string? VendorName { get; set; }
    public int? SalespersonId { get; set; }
    public string? SalespersonName { get; set; }
    public int? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public int? ShipToId { get; set; }
    public string? ShipToName { get; set; }
    public string? Comments { get; set; }
    public string? GrowerItemNotes { get; set; }
    public string? Who { get; set; }

    public List<PrebookCreateItem> Items { get; set; } = new();
}

public class PrebookCreateItem
{
    public int SpireInventoryId { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int OrderQuantity { get; set; }
    public bool HasPotCover { get; set; }
    public string? PotCover { get; set; }
    public string? DateCode { get; set; }
    public string? Upc { get; set; }
    public bool WeightsAndMeasures { get; set; }
    public string? Retail { get; set; }
    public string? Comments { get; set; }
    public bool IsApproximate { get; set; }
    public int? BlanketItemId { get; set; }
    public string? BlanketWeekId { get; set; }
    public bool UpgradeSheet { get; set; }
    public string? BoekestynPlantId { get; set; }
    public string? BoekestynCustomerAbbreviation { get; set; }
    public decimal UpgradeLabourHours { get; set; }
    public int? QuantityPerFinishedItem { get; set; }
    public decimal? SpecialPrice { get; set; }
    public string? GrowerItemNotes { get; set; }

    public List<PrebookDetailItemBlanketOption> BlanketOptions { get; set; } = new();
    public List<PrebookItemBoekestynProduct> BoekestynProducts { get; set; } = new();
}