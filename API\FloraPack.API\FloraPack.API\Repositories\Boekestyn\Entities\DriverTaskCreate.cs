﻿using System.Text.Json.Serialization;
using FloraPack.API.Spire;

namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class DriverTaskCreate(SpireRepository.TaskListPurchaseOrder order, SpireRepository.TaskListPurchaseOrderItem item, string who)
{
    [JsonPropertyName("type")]
    public string Type => "driver-task";

    [JsonPropertyName("createdBy")]
    public string CreatedBy => who;

    [JsonPropertyName("createdOn")]
    public string CreatedOn => DateTime.Now.ToString("o");

    [JsonPropertyName("dueDate")]
    public string? DueDate => order.RequiredDate;

    [JsonPropertyName("assignedTo")]
    public DriverTaskDriver AssignedTo => new();

    [JsonPropertyName("priority")]
    public string Priority => item.Priority ? "High" : "Normal";

    [JsonPropertyName("notes")]
    public string Notes => $"{item.OrderQty}cs {item.Description}" + (string.IsNullOrWhiteSpace(item.BoxCode) ? string.Empty : $"\nBox Code {item.BoxCode}");

    [JsonPropertyName("status")]
    public string Status => "Not Started";

    [JsonPropertyName("fromLocation")]
    public string FromLocation => "Plant 3";

    [JsonPropertyName("toLocation")]
    public string ToLocation => "Flora Pack";

    public class DriverTaskDriver
    {
        [JsonPropertyName("name")]
        public string Name => "johan";

        [JsonPropertyName("email")] public string? Email => null;

        [JsonPropertyName("phone")]
        public string Phone => "9059317875";
    }
}