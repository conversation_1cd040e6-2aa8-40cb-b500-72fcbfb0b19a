﻿namespace FloraPack.API.Repositories.Prebooks;

public class PrebookListItem
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public string? RequiredDate { get; set; }
    public string? SeasonDate { get; set; }
    public bool IsBlanket { get; set; }
    public string? BlanketStartDate { get; set; }
    public bool BlanketIsClosed { get; set; }
    public string? Vendor { get; set; }
    public string? Salesperson { get; set; }
    public string? Customer { get; set; }
    public string? ShipTo { get; set; }
    public string? Season { get; set; }
    public string? BoxCode { get; set; }
    public long CaseCount { get; set; }
    public string? SpirePurchaseOrderNumber { get; set; }
    public int? FutureOrderId { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime Modified { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime? Sent { get; set; }
    public string? SentBy { get; set; }
    public DateTime? Confirmed { get; set; }
    public string? ConfirmedBy { get; set; }
    public bool IsDeleted { get; set; }

    public List<PrebookDetailItem> Items { get; set; } = [];
}