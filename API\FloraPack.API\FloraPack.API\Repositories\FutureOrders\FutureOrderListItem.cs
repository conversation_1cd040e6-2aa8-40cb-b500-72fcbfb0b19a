﻿namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderListItem
{
    public int Id { get; set; }
    public string? RequiredDate { get; set; }
    public string? SeasonDate { get; set; }
    public string? Season { get; set; }
    public string Customer { get; set; } = string.Empty;
    public string? ShipTo { get; set; }
    public string? Salesperson { get; set; }
    public string? Truck { get; set; }
    public string? CustomerPurchaseOrderNumber { get; set; }
    public long CaseCount { get; set; }
    public decimal DollarValue { get; set; }
    public bool RequiresLabels { get; set; }
    public bool PhytoRequired { get; set; }
    public bool PhytoOrdered { get; set; }
    public bool IsParentOrder { get; set; }
    public int? ParentOrderId { get; set; }
    public string? SpireSalesOrderNumber { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime Modified { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime? SentToSpire { get; set; }
    public string? SentToSpireBy { get; set; }

    public List<FutureOrderListItemPrebook> Prebooks { get; set; } = new();
}