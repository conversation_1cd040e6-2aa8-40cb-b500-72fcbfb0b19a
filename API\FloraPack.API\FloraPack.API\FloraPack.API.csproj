﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-FloraPack.API-5F8C90D5-99A2-4DAF-B281-D61BB68F3863</UserSecretsId>
    <AssemblyVersion>1.0.24</AssemblyVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Core.Document.Drawing.Gdi" Version="4.3.2" /> -->
    <PackageReference Include="Lamar.Microsoft.DependencyInjection" Version="14.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.16" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.16" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.9.3" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" Version="3.9.3" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.9.3" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Services\" />
    <Folder Include="Properties\PublishProfiles\" />
    <Folder Include="Reports\Labels\" />
    <Folder Include="Repositories\Security\" />
  </ItemGroup>
  <!-- Temporarily exclude all GrapeCity report files from build -->
  <ItemGroup>
    <!-- <PackageReference Include="GrapeCity.ActiveReports" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Chart" Version="17.1.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Chart.Win" Version="17.1.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Image" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Excel" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.SpreadBuilder" Version="2.0.0" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Word" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Html" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Xml" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Pdf" Version="17.2.2" /> -->
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>
</Project>