﻿namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderSummaryItem
{
    public int Id { get; set; }
    public string? Customer { get; set; }
    public string? ShipTo { get; set; }
    public string? Vendor { get; set; }
    public string? Salesperson { get; set; }
    public string? Season { get; set; }
    public string? Date { get; set; }
    public string? SeasonDate { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? CustomerItemCode { get; set; }
    public int OrderQuantity { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime Modified { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime? SentToSpire { get; set; }
    public string? SentToSpireBy { get; set; }
    public int FutureOrderId { get; set; }
    public string PrebookIdList { get; set; } = string.Empty;
    public bool PrebookEmailSent { get; set; }
}