﻿using FloraPack.API.Repositories.Boekestyn.Entities;
using FloraPack.API.Repositories.Settings;
using FloraPack.API.Spire;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Spire")]
public class SpireController : FloraPackControllerBase
{
    private readonly SpireRepository _spireRepository;
    private readonly SettingsRepository _settingsRepository;

    public SpireController(IConfiguration configuration, SpireRepository spireRepository, SettingsRepository settingsRepository)
        : base(configuration)
    {
        _spireRepository = spireRepository;
        _settingsRepository = settingsRepository;
    }

    [HttpGet("Inventory-Items")]
    public async Task<IActionResult> InventoryItems([FromQuery] string? search = null)
    {
        var inventoryItems = await _spireRepository.GetInventoryItems(search);
        var productDefaults = await _settingsRepository.ProductDefaults();
        var response = new SpireInventoryItemsResponse(inventoryItems, productDefaults);
        return Ok(response);
    }

    [HttpGet("Vendors")]
    public async Task<IActionResult> Vendors()
    {
        var vendors = await _spireRepository.GetVendors();
        var response = new VendorsResponse(vendors);
        return Ok(response);
    }

    [HttpGet("Customers")]
    public async Task<IActionResult> Customers()
    {
        var customers = await _spireRepository.GetCustomers();
        var response = new CustomersResponse(customers);
        return Ok(response);
    }

    [HttpGet("Shipping-Methods")]
    public async Task<IActionResult> ShippingMethods()
    {
        var shippingMethods = await _spireRepository.GetShippingMethods();
        var response = new ShippingMethodsResponse(shippingMethods);
        return Ok(response);
    }

    [HttpGet("Ship-Tos")]
    public async Task<IActionResult> ShipTos([FromQuery] string? search = null)
    {
        var shipTos = await _spireRepository.GetCustomerAddresses(search);
        var response = new SpireShipTosResponse(shipTos);
        return Ok(response);
    }

    [HttpGet("Ship-Tos/{id:int}")]
    public async Task<IActionResult> ShipToDetail(int id)
    {
        var results = await _spireRepository.GetAddressDetail(id);
        if (results == null) {
            return NotFound();
        }

        var shipToDefaults = await _settingsRepository.ShipToDefaults([results.Value.ShipTo.ShipId]);
        var priceLevelDefaults = await _settingsRepository.GetPriceLevelFreightRates();

        results.Value.ShipTo.DefaultFreightPerCase =
            shipToDefaults.FirstOrDefault(d => d.ShipToCode == results.Value.ShipTo.ShipId)?.DefaultFreightPerCase ??
            priceLevelDefaults.FirstOrDefault(d => d.PriceLevel == results.Value.ShipTo.PriceLevel)
                ?.DefaultFreightPerCase;

        var response = new SpireShipToDetailResponse(results.Value.Customer, results.Value.ShipTo);
        return Ok(response);
    }

    [HttpGet("Customers/{id:int}")]
    public async Task<IActionResult> CustomerDetail(int id)
    {
        var customer = await _spireRepository.GetCustomerDetail(id);
        if (customer == null) {
            return NotFound();
        }

        var customerSetting = await _settingsRepository.GetCustomerSetting(id);
        var shipTos = customer.ShippingAddresses.Select(a => a.Id);
        var customerItemCodes = await _settingsRepository.CustomerItemCodes(id, shipTos, customerSetting.CustomerItemCodeByShipTo);
        var potCovers = await _settingsRepository.PotCoversForCustomer(id);
        var shipToCodes = customer.ShippingAddresses.Select(a => a.ShipId).ToList();
        var shipToDefaults = await _settingsRepository.ShipToDefaults(shipToCodes);
        var priceLevelDefaults = await _settingsRepository.GetPriceLevelFreightRates();

        foreach (var address in customer.ShippingAddresses) {
            address.DefaultFreightPerCase =
                shipToDefaults.FirstOrDefault(d => d.ShipToCode == address.ShipId)?.DefaultFreightPerCase ??
                priceLevelDefaults.FirstOrDefault(d => d.PriceLevel == address.PriceLevel)?.DefaultFreightPerCase;
        }

        var response = new CustomerDetailResponse(customer, customerItemCodes, potCovers);
        return Ok(response);
    }

    [HttpGet("Salespeople")]
    public async Task<IActionResult> Salespeople()
    {
        var salepeople = await _spireRepository.GetSalespeople();
        var response = new SalespersonResponse(salepeople);
        return Ok(response);
    }

    [HttpGet("Inventory-Comments")]
    public async Task<IActionResult> InventoryComments()
    {
        var comments = await _spireRepository.GetInventoryComments();
        var response = new InventoryCommentsResponse(comments);
        return Ok(response);
    }

    [HttpGet("Email-Templates")]
    public async Task<IActionResult> EmailTemplates()
    {
        var templates = await _spireRepository.EmailTemplates("confirmation");
        var response = new EmailTemplatesResponse(templates);
        return Ok(response);
    }

    private record SpireInventoryItemsResponse(IEnumerable<InventoryItem> InventoryItems, IEnumerable<ProductDefault> ProductDefaults);

    private record VendorsResponse(IEnumerable<Vendor> Vendors);
    
    private record CustomersResponse(IEnumerable<Spire.Customer> Customers);

    private record ShippingMethodsResponse(IEnumerable<ShippingMethod> ShippingMethods);

    private record SpireShipTosResponse(IEnumerable<CustomerAddress> ShipTos);

    private record SpireShipToDetailResponse(CustomerDetail Customer, CustomerShippingAddress ShipTo);

    private record CustomerDetailResponse(CustomerDetail Customer, IEnumerable<CustomerItemCodeDefault> CustomerItemCodes, IEnumerable<string> PotCovers);

    private record SalespersonResponse(IEnumerable<Salesperson> Salespeople);

    private record InventoryCommentsResponse(IEnumerable<InventoryComment> Comments);

    private record EmailTemplatesResponse(IEnumerable<EmailTemplate> Templates);
}