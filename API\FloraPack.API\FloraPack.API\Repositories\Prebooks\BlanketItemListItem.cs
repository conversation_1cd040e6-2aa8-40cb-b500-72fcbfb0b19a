﻿namespace FloraPack.API.Repositories.Prebooks;

public class BlanketItemListItem
{
    public int Id { get; set; }
    public int PrebookId { get; set; }
    public string? Customer { get; set; }
    public string? ShipTo { get; set; }
    public string? Vendor { get; set; }
    public string? Salesperson { get; set; }
    public string? Season { get; set; }
    public string? RequiredDate { get; set; }
    public string? SeasonDate { get; set; }
    public string? BlanketStartDate { get; set; }
    public string? BlanketWeekId { get; set; }
    public string? SpirePartNumber { get; set; }
    public string? Description { get; set; }
    public long BlanketQuantity { get; set; }
    public long BookedQuantity { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime Modified { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime? Sent { get; set; }
    public string? SentBy { get; set; }
    public DateTime? Confirmed { get; set; }
    public string? ConfirmedBy { get; set; }

    public List<BlanketItemListBookingItem> BookingItems { get; set; } = new();
}