﻿using ClosedXML.Excel;
using FloraPack.API.Repositories.Boekestyn.Entities;

namespace FloraPack.API.Reports.Boekestyns
{
    public class HarvestingLabourReportFactory
    {
        public static MemoryStream CreateReport(DateTime start, DateTime end, IEnumerable<HarvestingLabourReportItem> items)
        {
            var book = new XLWorkbook();
            var sheet = book.AddWorksheet();

            var row = 2;
            var col = 1;

            sheet.Cell(row, col).SetValue("Schedule Date").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Size").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Crop").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Customer").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Variety").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Harvested").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Thrown Out").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("#2's").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Crew Size").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Hours").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Man-Hours").Style.Font.Bold = true;
            col++;
            sheet.Cell(row, col).SetValue("Comments").Style.Font.Bold = true;

            sheet.Range(sheet.Cell(1, 1), sheet.Cell(2, col)).Style.Font.Bold = true;

            row++;

            foreach (var item in items)
            {
                col = 1;
                sheet.Cell(row, col).SetValue(item.ScheduleDate).Style.DateFormat.Format = "yyyy-mm-dd";
                col++;
                sheet.Cell(row, col).SetValue(item.PlantSize);
                col++;
                sheet.Cell(row, col).SetValue(item.PlantCrop);
                col++;
                sheet.Cell(row, col).SetValue(item.Customer);
                col++;
                sheet.Cell(row, col).SetValue(item.Variety);
                col++;
                sheet.Cell(row, col).SetValue(item.Harvested).Style.NumberFormat.Format = "#,##0";
                col++;
                sheet.Cell(row, col).SetValue(item.ThrownOut).Style.NumberFormat.Format = "#,##0";
                col++;
                sheet.Cell(row, col).SetValue(item.NumberTwos).Style.NumberFormat.Format = "#,##0";
                col++;
                sheet.Cell(row, col).SetValue(item.CrewSize);
                col++;
                sheet.Cell(row, col).SetValue(item.ActualHours).Style.NumberFormat.Format = "#,##0.00";
                col++;
                sheet.Cell(row, col).SetValue(item.ManHours).Style.NumberFormat.Format = "#,##0.00";
                col++;
                sheet.Cell(row, col).SetValue(item.Comments).Style.Alignment.WrapText = true;

                row++;
            }

            sheet.Columns().AdjustToContents();

            sheet.Cell(1, 1).SetValue($"Harvesting Labour: {start:yyyy-MM-dd} - {end:yyyy-MM-dd}").Style.Font.Bold = true;

            var stream = new MemoryStream();
            book.SaveAs(stream);
            stream.Position = 0;
            return stream;
        }
    }
}