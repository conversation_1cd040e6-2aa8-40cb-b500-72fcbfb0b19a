namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class SpacingWorkOrder
{
    public int Id { get; init; }
    public int ScheduleId { get; init; }
    public int SortOrder { get; init; }
    public string OrderId { get; init; } = string.Empty;
    public string OrderNumber { get; init; } = string.Empty;
    public string PlantSize { get; init; } = string.Empty;
    public string PlantCrop { get; init; } = string.Empty;
    public bool PlantHasPinching { get; init; }
    public int SpacingPotsPerHour { get; init; }
    public int CuttingsPerPot { get; init; }
    public int CuttingsPerTableTight { get; init; }
    public int CuttingsPerTablePartiallySpaced { get; init; }
    public int CuttingsPerTableSpaced { get; init; }
    public string Customer { get; init; } = string.Empty;
    public int OrderPots { get; init; }
    public int OrderCases { get; init; }
    public string Zone { get; init; } = string.Empty;
    public decimal EstimatedHours { get; init; }
    public int PotsToSpace { get; init; }
    public int CrewSize { get; init; }
    public string FromSpaceType { get; init; } = string.Empty;
    public string ToSpaceType { get; init; } = string.Empty;
    public bool RequiresPinching { get; init; }
    public string? OrderComments { get; init; }
    public string? SpacingComments { get; init; }
    public string? RobotProgram { get; init; }
    public List<SpacingWorkOrderVariety> Varieties { get; init; } = new();
}
