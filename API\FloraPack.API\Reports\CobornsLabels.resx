﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="picLogo.ImageBase64String" xml:space="preserve">
    <value>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</value>
  </data>
  <metadata name="$this.ScriptEditorPositionForUndo" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
  <metadata name="$this.ScriptEditorPositionForRedo" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
</root>