﻿namespace FloraPack.API.Reports
{
    /// <summary>
    /// Summary description for CustomerConfirmationItem.
    /// </summary>
    partial class CustomerConfirmationItem
    {
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing) {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CustomerConfirmationItem));
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.rtfCases = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.txtPackQuantity = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPotCover = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtUnitPrice = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.txtUPC = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtDateCode = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtRetail = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtWeightsAndMeasures = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.rtfComments = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            this.rtfDescription = new GrapeCity.ActiveReports.SectionReportModel.RichTextBox();
            ((System.ComponentModel.ISupportInitialize)(this.txtPackQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPotCover)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUPC)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDateCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWeightsAndMeasures)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // detail
            // 
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.rtfCases,
            this.txtPackQuantity,
            this.txtPotCover,
            this.txtUnitPrice,
            this.txtUPC,
            this.txtDateCode,
            this.txtRetail,
            this.txtWeightsAndMeasures,
            this.rtfComments,
            this.rtfDescription});
            this.detail.Height = 0.2F;
            this.detail.Name = "detail";
            this.detail.Format += new System.EventHandler(this.OnFormat);
            this.detail.BeforePrint += new System.EventHandler(this.OnBeforePrint);
            // 
            // rtfCases
            // 
            this.rtfCases.AutoReplaceFields = true;
            this.rtfCases.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfCases.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfCases.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfCases.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfCases.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfCases.Height = 0.2F;
            this.rtfCases.Left = 0F;
            this.rtfCases.Name = "rtfCases";
            this.rtfCases.RTF = resources.GetString("rtfCases.RTF");
            this.rtfCases.Top = 0F;
            this.rtfCases.Width = 0.5F;
            // 
            // txtPackQuantity
            // 
            this.txtPackQuantity.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPackQuantity.DataField = "PackQuantity";
            this.txtPackQuantity.Height = 0.2F;
            this.txtPackQuantity.Left = 2.5F;
            this.txtPackQuantity.Name = "txtPackQuantity";
            this.txtPackQuantity.OutputFormat = "#,##0";
            this.txtPackQuantity.Style = "font-size: 8pt; text-align: center";
            this.txtPackQuantity.Text = "Pack";
            this.txtPackQuantity.Top = 0F;
            this.txtPackQuantity.Width = 0.5F;
            // 
            // txtPotCover
            // 
            this.txtPotCover.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPotCover.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPotCover.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPotCover.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtPotCover.DataField = "PotCover";
            this.txtPotCover.Height = 0.2F;
            this.txtPotCover.Left = 3F;
            this.txtPotCover.Name = "txtPotCover";
            this.txtPotCover.Style = "font-size: 8pt; text-align: center";
            this.txtPotCover.Text = "Cover";
            this.txtPotCover.Top = 0F;
            this.txtPotCover.Width = 1F;
            // 
            // txtUnitPrice
            // 
            this.txtUnitPrice.AutoReplaceFields = true;
            this.txtUnitPrice.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUnitPrice.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUnitPrice.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUnitPrice.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUnitPrice.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.txtUnitPrice.Height = 0.2F;
            this.txtUnitPrice.Html = resources.GetString("txtUnitPrice.Html");
            this.txtUnitPrice.Left = 4F;
            this.txtUnitPrice.Name = "txtUnitPrice";
            this.txtUnitPrice.Top = 0F;
            this.txtUnitPrice.Width = 0.75F;
            // 
            // txtUPC
            // 
            this.txtUPC.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPC.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPC.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPC.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtUPC.DataField = "UPC";
            this.txtUPC.Height = 0.2F;
            this.txtUPC.Left = 4.75F;
            this.txtUPC.Name = "txtUPC";
            this.txtUPC.Style = "font-size: 8pt; text-align: center";
            this.txtUPC.Text = "UPC";
            this.txtUPC.Top = 0F;
            this.txtUPC.Width = 1F;
            // 
            // txtDateCode
            // 
            this.txtDateCode.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDateCode.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDateCode.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDateCode.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtDateCode.DataField = "DateCode";
            this.txtDateCode.Height = 0.2F;
            this.txtDateCode.Left = 5.75F;
            this.txtDateCode.Name = "txtDateCode";
            this.txtDateCode.Style = "font-size: 8pt; text-align: center";
            this.txtDateCode.Text = "Date Code";
            this.txtDateCode.Top = 0F;
            this.txtDateCode.Width = 0.75F;
            // 
            // txtRetail
            // 
            this.txtRetail.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtRetail.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtRetail.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtRetail.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtRetail.DataField = "Retail";
            this.txtRetail.Height = 0.2F;
            this.txtRetail.Left = 6.5F;
            this.txtRetail.Name = "txtRetail";
            this.txtRetail.Style = "font-size: 8pt; text-align: center";
            this.txtRetail.Text = "Retail";
            this.txtRetail.Top = 0F;
            this.txtRetail.Width = 0.75F;
            // 
            // txtWeightsAndMeasures
            // 
            this.txtWeightsAndMeasures.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasures.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasures.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasures.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.txtWeightsAndMeasures.DataField = "=WeightsAndMeasures ? \"\" : \"\"";
            this.txtWeightsAndMeasures.Height = 0.2F;
            this.txtWeightsAndMeasures.Left = 7.25F;
            this.txtWeightsAndMeasures.Name = "txtWeightsAndMeasures";
            this.txtWeightsAndMeasures.Style = "font-family: Segoe UI Symbol; font-size: 8pt; text-align: center";
            this.txtWeightsAndMeasures.Text = "W&M";
            this.txtWeightsAndMeasures.Top = 0F;
            this.txtWeightsAndMeasures.Width = 0.5F;
            // 
            // rtfComments
            // 
            this.rtfComments.AutoReplaceFields = true;
            this.rtfComments.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfComments.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfComments.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfComments.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfComments.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfComments.Height = 0.2F;
            this.rtfComments.Left = 7.75F;
            this.rtfComments.Name = "rtfComments";
            this.rtfComments.RTF = resources.GetString("rtfComments.RTF");
            this.rtfComments.Top = 0F;
            this.rtfComments.Width = 3.25F;
            // 
            // rtfDescription
            // 
            this.rtfDescription.AutoReplaceFields = true;
            this.rtfDescription.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.LeftStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.RightStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Border.TopStyle = GrapeCity.ActiveReports.BorderLineStyle.Solid;
            this.rtfDescription.Font = new GrapeCity.ActiveReports.Document.Drawing.Font("Arial", 10F);
            this.rtfDescription.Height = 0.2F;
            this.rtfDescription.Html = resources.GetString("rtfDescription.Html");
            this.rtfDescription.Left = 0.5F;
            this.rtfDescription.Name = "rtfDescription";
            this.rtfDescription.Top = 0F;
            this.rtfDescription.Width = 2F;
            // 
            // CustomerConfirmationItem
            // 
            this.MasterReport = false;
            this.CompatibilityMode = GrapeCity.ActiveReports.Document.CompatibilityModes.CrossPlatform;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 11F;
            this.Sections.Add(this.detail);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; color: Black; fon" +
            "t-family: \"Calibri\"; ddo-char-set: 186; font-size: 9pt", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Times New Roman; font-size: 14pt; font-weight: bold; font-style: ita" +
            "lic", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold", "Heading3", "Normal"));
            ((System.ComponentModel.ISupportInitialize)(this.txtPackQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPotCover)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUPC)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDateCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWeightsAndMeasures)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfCases;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPackQuantity;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPotCover;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox txtUnitPrice;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtUPC;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtRetail;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtWeightsAndMeasures;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDateCode;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfComments;
        private GrapeCity.ActiveReports.SectionReportModel.RichTextBox rtfDescription;
    }
}
