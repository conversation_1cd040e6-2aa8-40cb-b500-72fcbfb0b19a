﻿using ClosedXML.Excel;
using FloraPack.API.Controllers;
using FloraPack.API.Repositories.FutureOrders;

namespace FloraPack.API.Reports.FutureOrders;

public static class FutureOrderUpgradeItemsListReport
{
    public static MemoryStream Create(FutureOrdersController.FutureOrderUpgradeItemsListReportModel model)
    {
        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 1;
        var col = 1;

        sheet.Cell(row, col).SetValue("Date");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Qty");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Pack");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Description");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Product Coming From");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("UPC");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        
        sheet.Cell(row, col).SetValue("Container/Pick Description");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Origins");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Costs");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Box Code");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Hours");
        sheet.Cell(row, col).Style.Font.Bold = true;
        var hoursColumn = col;

        var maxCol = col;

        sheet.Range(row, 1, row, maxCol).Style.Border.BottomBorder = XLBorderStyleValues.Thin;

        row++;

        var items = model.Items
            .OrderBy(i => i.Date)
            .ThenByDescending(i => i.Season)
            .ThenBy(i => i.BoxCode)
            .ToList();

        var dates = items
            .Select(i => new DateSeason(i.Date, i.Season))
            .Distinct()
            .OrderBy(d => d.Date)
            .ThenBy(d => d.Season);

        foreach (var date in dates) {
            col = 1;
            var itemSeason = date.Season ?? string.Empty;

            if (!string.IsNullOrEmpty(itemSeason)) {
                sheet.Cell(row, col).SetValue(itemSeason);
            } else {
                if (date.Date != null && DateTime.TryParse(date.Date, out var d)) {
                    sheet.Cell(row, col).SetValue(d.ToString("MMM d, yyyy"));
                }
            }

            var dateItems = items
                .Where(i => i.Date == date.Date && i.Season == date.Season)
                .ToList();

            var labourHours = dateItems
                .Sum(i => i.RoundedLabourHours);

            sheet.Cell(row, hoursColumn).SetValue(labourHours);
            sheet.Cell(row, hoursColumn).Style.NumberFormat.SetFormat("#,##0");

            sheet.Range(row, 1, row, maxCol).Style.Font.Bold = true;
            sheet.Range(row, 1, row, maxCol).Style.Fill.BackgroundColor = XLColor.LightGray;
            sheet.Range(row, 1, row, maxCol).Style.Border.BottomBorder = XLBorderStyleValues.Thin;
            row++;

            var customers = dateItems
                .Select(i => i.CustomerId.GetValueOrDefault())
                .Distinct()
                .OrderBy(c => c)
                .ToList();

            foreach (var customer in customers) {

                var customerItems = dateItems
                    .Where(i => i.CustomerId.GetValueOrDefault() == customer)
                    .ToList();


                var products = customerItems
                    .Select(i => i.SpirePartNumber)
                    .Distinct()
                    .OrderBy(p => p)
                    .ToList();

                foreach (var product in products) {
                    var productItems = customerItems
                        .Where(i => i.SpirePartNumber == product)
                        .ToList();

                    var groupedProductItems = new List<List<UpgradeItem>>();
                    foreach (var productItem in productItems) {
                        if (productItem.UpgradeConfirmed == null) {
                            groupedProductItems.Add(new() { productItem });
                        } else {
                            var group = groupedProductItems.Find(g => g.Any(i =>
                                i.BoxCode == productItem.BoxCode &&
                                string.Equals(i.Description, productItem.Description, StringComparison.InvariantCultureIgnoreCase) &&
                                string.Equals(i.ContainerPickDescription, productItem.ContainerPickDescription, StringComparison.InvariantCultureIgnoreCase) &&
                                string.Equals(i.Upc, productItem.Upc, StringComparison.InvariantCultureIgnoreCase) &&
                                string.Equals(i.ProductComingFrom, productItem.ProductComingFrom, StringComparison.InvariantCultureIgnoreCase)
                            ));
                            if (group != null) {
                                group.Add(productItem);
                            } else {
                                groupedProductItems.Add(new() { productItem });
                            }
                        }
                    }

                    foreach (var group in groupedProductItems) {
                        row = WriteLine(sheet, row, group);
                    }
                }

                sheet.Range(row - 1, 1, row - 1, maxCol).Style.Border.BottomBorder = XLBorderStyleValues.Thick;
            }
        }

        sheet.Columns(1, col).AdjustToContents();

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }

    private static int WriteLine(IXLWorksheet sheet, int row, List<UpgradeItem> shipToItems)
    {
        var col = 2;
        var item = shipToItems.First();

        var orderQuantity = shipToItems.Sum(i => i.OrderQuantity);
        sheet.Cell(row, col).SetValue(orderQuantity);
        col++;

        var packQuantity = item.PackQuantity;
        if (packQuantity.HasValue) {
            sheet.Cell(row, col).SetValue(packQuantity.Value);
        }

        col++;

        var description = item.Description;
        if (!string.IsNullOrWhiteSpace(item.Comments)) {
            description += $"\n{item.Comments}";
            sheet.Cell(row, col).Style.Alignment.WrapText = true;
        }

        if (!string.IsNullOrWhiteSpace(item.UpgradeComments)) {
            description += $"\n{item.UpgradeComments}";
            sheet.Cell(row, col).Style.Alignment.WrapText = true;
        }

        sheet.Cell(row, col).SetValue(description);
        col++;

        sheet.Cell(row, col).SetValue(item.ProductComingFrom);
        col++;

        var upcElements = new List<string?> { item.Upc, item.DateCode, item.Retail, item.WeightsAndMeasures ? "YES" : "" };
        var upc = string.Join(" | ", upcElements.Where(e => !string.IsNullOrWhiteSpace(e)));

        sheet.Cell(row, col).SetValue(upc);
        col++;

        sheet.Cell(row, col).SetValue(item.ContainerPickDescription);
        col++;

        sheet.Cell(row, col).SetValue(item.Origins);
        col++;

        sheet.Cell(row, col).SetValue(item.Costs);
        col++;

        if (shipToItems.Count > 1) {
            sheet.Cell(row, col).SetValue(string.Join("\n", shipToItems.Select(i => $"{i.BoxCode} - {i.OrderQuantity}")));
            sheet.Cell(row, col).Style.Alignment.WrapText = true;
        } else {
            sheet.Cell(row, col).SetValue(item.BoxCode);
        }

        col++;

        var shipToLabourHours = shipToItems.Sum(i => i.LabourHours);
        var rawHours =
            shipToLabourHours == 0 ? 0 : orderQuantity / shipToLabourHours;
        var roundedHours = Convert.ToInt32(Math.Round(
            rawHours > 0 && rawHours < 1 ? 1 : rawHours
        ));

        sheet.Cell(row, col).SetValue(roundedHours);
        sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");

        row++;
        return row;
    }

    private record DateSeason(string? Date, string? Season);
}