using System.Text.Json.Serialization;

namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class SpacingOrder
{
    [JsonPropertyName("_id")]
    public string Id { get; init; } = string.Empty;

    [JsonPropertyName("_rev")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Revision { get; init; }

    [JsonPropertyName("type")]
    public string Type { get; init; } = "order";

    [JsonPropertyName("orderNumber")]
    public string OrderNumber { get; init; } = string.Empty;

    [JsonPropertyName("spaceDate")]
    public string SpaceDate { get; init; } = string.Empty;

    [JsonPropertyName("hasPartialSpace")]
    public bool HasPartialSpace { get; init; }

    [JsonPropertyName("hasSpacing")]
    public bool HasSpacing { get; init; }

    [JsonPropertyName("fullSpaceDate")]
    public string? FullSpaceDate { get; init; }

    [JsonPropertyName("hasLightsOut")]
    public bool HasLightsOut { get; init; }

    [JsonPropertyName("lightsOutDate")]
    public string? LightsOutDate { get; init; }

    [JsonPropertyName("hasPinching")]
    public bool HasPinching { get; init; }

    [JsonPropertyName("flowerDate")]
    public string FlowerDate { get; init; } = string.Empty;

    [JsonPropertyName("cuttings")]
    public int Cuttings { get; init; }

    [JsonPropertyName("pots")]
    public int Pots { get; init; }

    [JsonPropertyName("cases")]
    public int Cases { get; init; }

    [JsonPropertyName("pinchDate")]
    public string? PinchDate { get; init; }

    [JsonPropertyName("tableCountTight")]
    public int TableCountTight { get; init; }

    [JsonPropertyName("tableCountSpaced")]
    public int TableCountSpaced { get; init; }

    [JsonPropertyName("notes")]
    public string? Notes { get; init; }

    [JsonPropertyName("customer")]
    public SpacingOrderCustomer Customer { get; init; } = new();

    [JsonPropertyName("plant")]
    public SpacingOrderPlant Plant { get; init; } = new();

    [JsonPropertyName("varieties")]
    public List<SpacingOrderVariety> Varieties { get; init; } = [];

    [JsonPropertyName("spaceZone")]
    public SpacingOrderZone SpaceZone { get; init; } = new();

    [JsonPropertyName("fullSpaceZone")]
    public SpacingOrderZone? FullSpaceZone { get; init; }

    [JsonPropertyName("lightsOutZone")]
    public SpacingOrderZone? LightsOutZone { get; init; }

    public decimal SpacingHours => Plant.SpacingPotsPerHour == 0 ? 0 : (decimal)Pots / Plant.SpacingPotsPerHour;

    public int PotsFullySpaced { get; set; }

    public int PotsPartiallySpaced { get; set; }

    public class SpacingOrderCustomer
    {
        [JsonPropertyName("abbreviation")]
        public string Abbreviation { get; init; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; init; } = string.Empty;
    }

    public class SpacingOrderPlant
    {
        [JsonPropertyName("_id")]
        public string Id { get; init; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("abbreviation")]
        public string Abbreviation { get; set; } = string.Empty;

        [JsonPropertyName("crop")]
        public string Crop { get; set; } = string.Empty;

        [JsonPropertyName("size")]
        public string Size { get; set; } = string.Empty;

        [JsonPropertyName("cuttingsPerPot")]
        public int CuttingsPerPot { get; set; }

        [JsonPropertyName("cuttingsPerTableTight")]
        public int CuttingsPerTableTight { get; set; }

        [JsonPropertyName("cuttingsPerTableSpaced")]
        public int CuttingsPerTableSpaced { get; set; }

        [JsonPropertyName("cuttingsPerTablePartiallySpaced")]
        public int CuttingsPerTablePartiallySpaced { get; set; }

        [JsonPropertyName("potsPerCase")]
        public int PotsPerCase { get; set; }

        [JsonPropertyName("stickingCuttingsPerHour")]
        public int StickingCuttingsPerHour { get; set; }

        [JsonPropertyName("spacingPotsPerHour")]
        public int SpacingPotsPerHour { get; set; }

        [JsonPropertyName("packingCasesPerHour")]
        public int PackingCasesPerHour { get; set; }

        [JsonPropertyName("colour")]
        public string Colour { get; set; } = string.Empty;

        [JsonPropertyName("hasPinching")]
        public bool HasPinching { get; set; }

        [JsonPropertyName("daysToPinch")]
        public int DaysToPinch { get; set; }
    }

    public class SpacingOrderVariety
    {
        [JsonPropertyName("name")]
        public string Name { get; init; } = string.Empty;

        [JsonPropertyName("cuttings")]
        public int Cuttings { get; init; }

        [JsonPropertyName("pots")]
        public int Pots { get; init; }

        [JsonPropertyName("cases")]
        public int Cases { get; init; }

        [JsonPropertyName("comment")]
        public string? Comment { get; init; }
    }

    public class SpacingOrderZone
    {
        [JsonPropertyName("name")]
        public string Name { get; init; } = string.Empty;

        [JsonPropertyName("tables")]
        public int Tables { get; init; }

        [JsonPropertyName("_id")]
        public string Id { get; init; } = string.Empty;
    }
}
