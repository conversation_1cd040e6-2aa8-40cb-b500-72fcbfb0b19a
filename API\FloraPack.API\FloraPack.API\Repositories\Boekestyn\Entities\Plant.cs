﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class Plant
{
    [JsonPropertyName("_id")]
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Abbreviation { get; set; } = string.Empty;
    public string Crop { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public int? CuttingsPerPot { get; set; }
    public int? CuttingsPerTableTight { get; set; }
    public int? CuttingsPerTableSpaced { get; set; }
    public int? CuttingsPerTablePartiallySpaced { get; set; }
    public int? PotsPerCase { get; set; }
    public bool HasLightsOut { get; set; }
    public int? StickingCuttingsPerHour { get; set; }
    public int? SpacingPotsPerHour { get; set; }
    public int? PinchingPotsPerHour { get; set; }
    public int? PackingCasesPerHour { get; set; }
    public string? Colour { get; set; }
    public bool HasPinching { get; set; }
    public int DaysToPinch { get; set; }
    public int? DefaultStickingCrewSize { get; init; }

    public List<PlantVariety>? Varieties { get; set; }
}