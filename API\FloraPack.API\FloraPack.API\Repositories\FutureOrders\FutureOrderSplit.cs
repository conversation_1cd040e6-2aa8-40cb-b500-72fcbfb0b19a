﻿namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderSplit
{
    public int? ExistingFutureOrderId { get; set; }

    public DateTime? RequiredDate { get; set; }
    public DateTime? ArrivalDate { get; set; }
    public string? SeasonName { get; set; }
    public string? BoxCode { get; set; }
    public int? SalespersonId { get; set; }
    public string? SalespersonName { get; set; }
    public int? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public int? ShipToId { get; set; }
    public string? ShipToName { get; set; }
    public int? ShipViaId { get; set; }
    public string? ShipViaName { get; set; }
    public string? CustomerPurchaseOrderNumber { get; set; }
    public decimal? FreightPerCase { get; set; }
    public decimal? FreightPerLoad { get; set; }
    public bool FreightIsActual { get; set; }
    public bool RequiresLabels { get; set; }
    public string? SpireNotes { get; set; }
    public string? GrowerItemNotes { get; set; }

    public List<FutureOrderSplitItem> Items { get; set; } = new();
    public List<FutureOrderSplitComment> Comments { get; set; } = new();
}

public class FutureOrderSplitItem
{
    public int OriginalId { get; set; }
    public int OrderQuantity { get; set; }
}

public class FutureOrderSplitComment
{
    public string? Comments { get; set; }
    public bool IsStandardComment { get; set; }
}