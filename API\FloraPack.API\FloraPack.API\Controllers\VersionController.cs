﻿using System.Reflection;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Version")]
public class VersionController : FloraPackControllerBase
{
    public VersionController(IConfiguration configuration) : base(configuration) { }

    [HttpGet("")]
    public IActionResult Index()
    {
        var version = Assembly.GetExecutingAssembly().GetName().Version;
        var response = new { version };
        return Ok(response);
    }
}