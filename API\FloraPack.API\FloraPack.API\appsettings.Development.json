{"AttachmentsPath": "C:\\", "AzureAd": {"Instance": "https://login.microsoftonline.com/", "TenantId": "66c22e4f-c9e0-467b-b745-0557cff9b2a1", "ClientId": "9ffce565-e06f-4922-a1d1-e8b5f92fe157", "ClientSecret": "****************************************", "Scopes": ["https://graph.microsoft.com/.default"]}, "GraphBeta": {"BaseUrl": "https://graph.microsoft.com/beta", "Scopes": "user.read, mail.send"}, "ConnectionStrings": {"DefaultConnection": "User ID=postgres;Password=****************;Host=localhost;Port=5432;Database=web;", "SpireConnection": "User ID=postgres;Password=****************;Host=localhost;Port=5432;Database=spire;"}, "ClientUrl": "http://localhost:3050", "Spire": {"ApiBase": "http://localhost:10880/", "UserName": "<PERSON><PERSON>", "Password": "N!chir1n", "CompanyName": "florapack"}, "CouchDB": {"ApiKey": "cliffe", "Password": "Password1", "UrlBase": "https://7bff221b-c372-4033-bf6c-59d2e859a520-bluemix.cloudant.com/", "Database": "boekestyn-test"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}