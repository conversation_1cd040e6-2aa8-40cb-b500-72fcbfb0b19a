﻿using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Repositories.Prebooks;
using FloraPack.API.Repositories.Settings;
using FloraPack.API.Spire;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Settings")]
public class SettingsController : FloraPackControllerBase
{
    private readonly SettingsRepository _settingsRepository;
    private readonly SpireRepository _spireRepository;

    public SettingsController(IConfiguration configuration, SettingsRepository settingsRepository,
        SpireRepository spireRepository) : base(configuration)
    {
        _settingsRepository = settingsRepository;
        _spireRepository = spireRepository;
    }

    [HttpGet("Customers/{id:int}")]
    public async Task<IActionResult> CustomerSettings(int id)
    {
        var customer = await _spireRepository.GetCustomerDetail(id);
        if (customer == null) {
            return NotFound();
        }

        var customerSetting = await _settingsRepository.GetCustomerSetting(id);
        var shipTos = customer.ShippingAddresses.Select(a => a.Id).ToList();
        var shipToCodes = customer.ShippingAddresses.Select(a => a.ShipId).ToList();
        var shipToDefaults = await _settingsRepository.ShipToDefaults(shipToCodes);
        var priceLevelFreightRates = await _settingsRepository.GetPriceLevelFreightRates();
        var customerProductDefaults = await _settingsRepository.ProductCustomerDefaults(id);
        var shipToProductDefaults = await _settingsRepository.ProductShipToDefaults(shipTos);
        var potCovers = await _settingsRepository.PotCoversForCustomer(id);

        var response = new CustomerSettingsResponse(customer, customerSetting.CustomerItemCodeByShipTo,
            shipToDefaults, priceLevelFreightRates, customerProductDefaults, shipToProductDefaults, potCovers);

        return Ok(response);
    }

    [HttpPut("Customers/{id:int}/Defaults")]
    public async Task<IActionResult> CustomerDefaults(int id, [FromBody] CustomerDefaultsModel model)
    {
        var customer = await _spireRepository.GetCustomerDetail(id);
        if (customer == null) {
            return NotFound();
        }

        await _settingsRepository.InsertOrUpdateCustomerSetting(id, customer.Name, model.CustomerItemCodeByShipTo);

        var customerSetting = await _settingsRepository.GetCustomerSetting(id);
        var shipTos = customer.ShippingAddresses.Select(a => a.Id).ToList();
        var shipToCodes = customer.ShippingAddresses.Select(a => a.ShipId).ToList();
        var shipToDefaults = await _settingsRepository.ShipToDefaults(shipToCodes);
        var priceLevelFreightRates = await _settingsRepository.GetPriceLevelFreightRates();
        var customerProductDefaults = await _settingsRepository.ProductCustomerDefaults(id);
        var shipToProductDefaults = await _settingsRepository.ProductShipToDefaults(shipTos);
        var potCovers = await _settingsRepository.PotCoversForCustomer(id);

        var response = new CustomerSettingsResponse(customer, customerSetting.CustomerItemCodeByShipTo,
            shipToDefaults, priceLevelFreightRates, customerProductDefaults, shipToProductDefaults, potCovers);

        return Ok(response);
    }

    [HttpDelete("Product-Customer-Defaults/{id:int}")]
    public async Task<IActionResult> DeleteProductCustomerDefault(int id)
    {
        await _settingsRepository.DeleteProductCustomerDefault(id);
        return Ok();
    }

    [HttpPut("Product-Customer-Defaults/{id:int}")]
    public async Task<IActionResult> UpdateProductCustomerDefault(int id,
        [FromBody] UpdateProductCustomerDefaultModel model)
    {
        var value = await _settingsRepository.UpdateProductCustomerDefaultCustomer(id, model.CustomerItemCode);
        var response = new UpdateProductCustomerDefaultResponse(value);
        return Ok(response);
    }

    [HttpDelete("Product-Ship-To-Defaults/{id:int}")]
    public async Task<IActionResult> DeleteProductShipToDefault(int id)
    {
        await _settingsRepository.DeleteProductShipToDefault(id);
        return Ok();
    }

    [HttpPut("Product-Ship-To-Defaults/{id:int}")]
    public async Task<IActionResult> UpdateProductShipToDefault(int id,
        [FromBody] UpdateProductShipToDefaultModel model)
    {
        var value = await _settingsRepository.UpdateProductShipToDefault(id, model.HasPotCover, model.PotCover,
            model.Upc, model.WeightsAndMeasures, model.Retail, model.UnitPrice, model.CustomerItemCode);
        var response = new UpdateProductShipToDefaultResponse(value);
        return Ok(response);
    }

    [HttpPost("Customers/{customerId:int}/Pot-Covers/{potCover}")]
    public async Task<IActionResult> UpdateCustomerPotCover(int customerId, string potCover)
    {
        await _settingsRepository.UpdateCustomerPotCover(customerId, potCover);
        return Ok();
    }

    [HttpDelete("Customers/{customerId:int}/Pot-Covers/{potCover}")]
    public async Task<IActionResult> DeleteCustomerPotCover(int customerId, string potCover)
    {
        await _settingsRepository.DeleteCustomerPotCover(customerId, potCover);
        return Ok();
    }

    [HttpGet("Upgrade-Options")]
    public async Task<IActionResult> UpgradeOptions()
    {
        var options = await _settingsRepository.UpgradeOptions();
        var response = new UpgradeOptionsResponse(options);
        return Ok(response);
    }

    [HttpPost("Upgrade-Options")]
    public async Task<IActionResult> CreateOrUpdateUpgradeOption([FromBody] CreateOrUpdateUpgradeOptionModel model)
    {
        await _settingsRepository.InsertOrUpdateUpgradeOption(model.ContainerPickDescription, model.Origins,
            model.Costs, model.TariffCode);

        var upgradeOptions = await _settingsRepository.UpgradeOptions();
        var response = new CreateOrUpdateUpgradeOptionResponse(upgradeOptions);
        return Ok(response);
    }

    [HttpPut("Upgrade-Options/{id:int}")]
    public async Task<IActionResult> UpdateUpgradeOption([FromBody] UpdateUpgradeOptionModel model)
    {
        await _settingsRepository.UpdateUpgradeOption(model.Id, model.ContainerPickDescription, model.Origins,
            model.Costs, model.TariffCode);

        var upgradeOptions = await _settingsRepository.UpgradeOptions();
        var response = new UpdateUpgradeOptionResponse(upgradeOptions);
        return Ok(response);
    }

    [HttpDelete("Upgrade-Options/{id:int}")]
    public async Task<IActionResult> DeleteUpgradeOption(int id)
    {
        await _settingsRepository.DeleteUpgradeOption(id);

        var upgradeOptions = await _settingsRepository.UpgradeOptions();
        var response = new UpdateUpgradeOptionResponse(upgradeOptions);
        return Ok(response);
    }

    [HttpPost("Product-Defaults/Labour-Hours")]
    public async Task<IActionResult> UpdateProductDefaultLabourHours(
        [FromBody] UpdateProductDefaultLabourHoursModel model)
    {
        var productDefault = await _settingsRepository.GetProductDefault(model.SpireInventoryId);

        await _settingsRepository.UpdateProductDefault(model.SpireInventoryId, productDefault?.BoekestynPlantId,
            productDefault?.BoekestynCustomerAbbreviation, model.UpgradeLabourHours,
            productDefault?.QuantityPerFinishedItem,
            (productDefault?.IsUpgrade).GetValueOrDefault(),
            (productDefault?.IgnoreOverrideQuantity).GetValueOrDefault());
        return Ok();
    }

    [HttpPost("Seasons")]
    public async Task<IActionResult> UpdateSeason([FromBody] UpdateSeasonModel model)
    {
        await _settingsRepository.InsertOrUpdateSeason(model.Name, model.SeasonDate);
        return Ok();
    }

    [HttpPost("Product-Defaults")]
    public async Task<IActionResult> UpdateProductDefault([FromBody] UpdateProductDefaultModel model)
    {
        await _settingsRepository.UpdateProductDefault(model.SpireInventoryId, model.BoekestynPlantId,
            model.BoekestynCustomerAbbreviation, model.UpgradeLabourHours, model.QuantityPerFinishedItem,
            model.IsUpgrade, model.IgnoreOverrideQuantity, model.Products, model.Overrides);

        if (model.UpdateExisting) {
            await _settingsRepository.UpdateExistingOrderProductDefaults(model.SpireInventoryId);
        }

        return Ok();
    }

    [HttpPost("Refresh-Spire")]
    public async Task<IActionResult> RefreshSpire([FromQuery] DateTime? refreshDate = null)
    {
        var refreshed = await _settingsRepository.RefreshSpireItems(refreshDate);

        var response = new RefreshSpireResponse(refreshed);
        return Ok(response);
    }

    [HttpGet("Price-Deviation-Warnings")]
    public async Task<IActionResult> PriceDeviationWarnings()
    {
        var warnings = await _settingsRepository.PriceDeviationWarnings();
        var response = new PriceDeviationWarningsResponse(warnings);
        return Ok(response);
    }

    [HttpGet("Default-Vendor-Overrides")]
    public async Task<IActionResult> DefaultVendorOverrides()
    {
        var overrides = await _settingsRepository.DefaultVendorOverrides();
        var response = new DefaultVendorOverrideResponse(overrides);
        return Ok(response);
    }

    [HttpPost("Default-Vendor-Overrides")]
    public async Task<IActionResult> UpdateDefaultVendorOverride([FromBody] UpdateDefaultVendorOverrideModel model)
    {
        await _settingsRepository.InsertVendorOverride(model.DefaultVendorOverride);
        return Ok();
    }

    [HttpPut("Default-Vendor-Overrides/{id:int}")]
    public async Task<IActionResult> UpdateDefaultVendorOverride(int id, [FromBody] UpdateDefaultVendorOverrideModel model)
    {
        await _settingsRepository.UpdateVendorOverride(model.DefaultVendorOverride);
        return Ok();
    }

    [HttpDelete("Default-Vendor-Overrides/{id:int}")]
    public async Task<IActionResult> DeleteDefaultVendorOverride(int id)
    {
        await _settingsRepository.DeleteDefaultVendorOverride(id);
        return Ok();
    }

    [HttpGet("Freight-Rates")]
    public async Task<IActionResult> FreightRates()
    {
        var priceLevels = await _spireRepository.GetPriceLevels();
        var freightRates = await _settingsRepository.GetPriceLevelFreightRates();
        var response = new FreightRateResponse(priceLevels, freightRates);
        return Ok(response);
    }

    [HttpPost("Freight-Rates")]
    public async Task<IActionResult> UpdateFreightRate([FromBody] UpdateFreightRateModel model)
    {
        await _settingsRepository.UpdatePriceLevelFreightRate(model.PriceLevel, model.DefaultFreightPerCase);
        return Ok();
    }

    [HttpPost("Ship-To-Defaults")]
    public async Task<IActionResult> UpdateShipToDefaults([FromBody] UpdateShipToDefaultModel model)
    {
        await _settingsRepository.UpdateShipToDefault(model.ShipToCode, model.DefaultFreightPerCase);
        return Ok();
    }

    private record CustomerSettingsResponse(
        CustomerDetail Customer,
        bool CustomerItemCodeByShipTo,
        IEnumerable<ShipToDefault> ShipToDefaults,
        IEnumerable<PriceLevelFreightRate> PriceLevelFreightRates,
        IEnumerable<ProductCustomerDefault> ProductCustomerDefaults,
        IEnumerable<ProductShipToDefault> ProductShipToDefaults,
        IEnumerable<string> PotCovers);

    public class CustomerDefaultsModel
    {
        public bool CustomerItemCodeByShipTo { get; set; }
    }

    public class UpdateProductCustomerDefaultModel
    {
        public string CustomerItemCode { get; set; } = string.Empty;
    }

    private record UpdateProductCustomerDefaultResponse(ProductCustomerDefault? ProductCustomerDefault);

    public class UpdateProductShipToDefaultModel
    {
        public bool HasPotCover { get; set; }
        public string? PotCover { get; set; }
        public string? Upc { get; set; }
        public bool WeightsAndMeasures { get; set; }
        public string? Retail { get; set; }
        public decimal? UnitPrice { get; set; }
        public string? CustomerItemCode { get; set; }
    }

    private record UpdateProductShipToDefaultResponse(ProductShipToDefault? ProductShipToDefault);

    public class CreateOrUpdateUpgradeOptionModel
    {
        public string ContainerPickDescription { get; set; } = string.Empty;
        public string? Origins { get; set; }
        public string? Costs { get; set; }
        public string? TariffCode { get; set; }
    }

    public class UpdateUpgradeOptionModel
    {
        public int Id { get; set; }
        public string ContainerPickDescription { get; set; } = string.Empty;
        public string? Origins { get; set; }
        public string? Costs { get; set; }
        public string? TariffCode { get; set; }
    }

    public class UpdateProductDefaultLabourHoursModel
    {
        public int SpireInventoryId { get; set; }
        public decimal UpgradeLabourHours { get; set; }
    }

    private record UpgradeOptionsResponse(IEnumerable<UpgradeOption> UpgradeOptions);

    private record CreateOrUpdateUpgradeOptionResponse(IEnumerable<UpgradeOption> UpgradeOptions);

    private record UpdateUpgradeOptionResponse(IEnumerable<UpgradeOption> UpgradeOptions);

    public class UpdateSeasonModel
    {
        public string Name { get; set; } = string.Empty;
        public DateTime? SeasonDate { get; set; }
    }

    public class UpdateProductDefaultModel
    {
        public int SpireInventoryId { get; set; }
        public string? BoekestynPlantId { get; set; }
        public string? BoekestynCustomerAbbreviation { get; set; }
        public decimal? UpgradeLabourHours { get; set; }
        public int? QuantityPerFinishedItem { get; set; }
        public bool IsUpgrade { get; set; }
        public bool IgnoreOverrideQuantity { get; set; }
        public bool UpdateExisting { get; set; }
        public List<ProductDefaultBoekestynProduct> Products { get; set; } = new();
        public List<ProductDefaultBoekestynProductOverride> Overrides { get; set; } = new();
    }

    private record RefreshSpireResponse(SpireRefreshItem Refreshed);

    private record PriceDeviationWarningsResponse(IEnumerable<PriceDeviationWarning> PriceDeviationWarnings);

    private record DefaultVendorOverrideResponse(IEnumerable<DefaultVendorOverride> Overrides);

    public class UpdateDefaultVendorOverrideModel
    {
        public DefaultVendorOverride DefaultVendorOverride { get; set; } = new();
    }

    public class CreateDefaultVendorOverrideModel
    {
        public DefaultVendorOverride DefaultVendorOverride { get; set; } = new();
    }

    public class UpdateShipToDefaultModel
    {
        public string ShipToCode { get; init; } = string.Empty;
        public decimal? DefaultFreightPerCase { get; init; }
    }

    private record FreightRateResponse(IEnumerable<PriceLevel> PriceLevels, IEnumerable<PriceLevelFreightRate> FreightRates);

    public class UpdateFreightRateModel
    {
        public string PriceLevel { get; set; } = string.Empty;
        public decimal? DefaultFreightPerCase { get; set; }
    }
}