﻿using Dapper;
using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Repositories.Prebooks;
using System.Data;

namespace FloraPack.API.Repositories.Settings;

public class SettingsRepository : RepositoryBase
{
    public SettingsRepository(IConfiguration configuration) : base(configuration) { }

    public async Task<CustomerSetting> GetCustomerSetting(int customerId) =>
        (await GetConnection()
            .QueryFirstOrDefaultAsync<CustomerSetting>(
                "SELECT * FROM customer_settings WHERE customer_id = @customerId", new { customerId })) ??
        new CustomerSetting { CustomerId = customerId };

    public async Task InsertOrUpdateCustomerSetting(int customerId, string customerName, bool customerItemCodeByShipTo)
    {
        await using var connection = GetConnection();

        await connection.ExecuteAsync(
            "INSERT INTO customer_settings (customer_id, customer_name, customer_item_code_by_ship_to) " +
            "VALUES (@customerId, @customerName, @customerItemCodeByShipTo) " +
            "ON CONFLICT (customer_id) DO " +
            "UPDATE SET customer_item_code_by_ship_to = @customerItemCodeByShipTo;",
            new { customerId, customerName, customerItemCodeByShipTo });

        if (!customerItemCodeByShipTo) {
            await connection.ExecuteAsync("DELETE FROM product_customer_defaults WHERE customer_id = @customerId",
                new { customerId });
        }
    }

    public async Task<IEnumerable<CustomerItemCodeDefault>> CustomerItemCodes(int customerId,
        IEnumerable<int> shipToIds, bool customerItemCodeByShipTo)
    {
        var connection = GetConnection();

        if (customerItemCodeByShipTo) {
            var ids = string.Join(", ", shipToIds);
            return await connection.QueryAsync<CustomerItemCodeDefault>(
                $"SELECT spire_inventory_id, @customerId AS customer_id, ship_to_id, customer_item_code FROM product_ship_to_defaults WHERE ship_to_id IN ({ids});",
                new { customerId });
        }

        return await connection.QueryAsync<CustomerItemCodeDefault>(
            "SELECT spire_inventory_id, customer_id, customer_item_code FROM product_customer_defaults WHERE customer_id = @customerId;",
            new { customerId });
    }

    public async Task<IEnumerable<ProductCustomerDefault>> ProductCustomerDefaults(int customerId) =>
        await GetConnection().QueryAsync<ProductCustomerDefault>(
            "SELECT * FROM product_customer_defaults WHERE customer_id = @customerId;", new { customerId });

    public async Task<ProductCustomerDefault> GetProductCustomerDefault(int spireInventoryId, int customerId) =>
        (await GetConnection().QueryFirstOrDefaultAsync<ProductCustomerDefault>(
            "SELECT * FROM product_customer_defaults " +
            "WHERE spire_inventory_id = @spireInventoryId AND customer_id = @customerId",
            new { spireInventoryId, customerId })) ?? new ProductCustomerDefault();

    public async Task<List<ShipToDefault>> ShipToDefaults(List<string> shipToIds) =>
        (
            await GetConnection()
            .QueryAsync<ShipToDefault>("SELECT * FROM ship_to_defaults WHERE ship_to_code = ANY (@codes);", new { codes = shipToIds.ToArray() })
        ).ToList();

    public async Task UpdateShipToDefault(string code, decimal? freightRate)
    {
        if (freightRate == null) {
            await GetConnection().ExecuteAsync("DELETE FROM ship_to_defaults WHERE ship_to_code = @code;", new { code });
        } else {
            await GetConnection().ExecuteAsync(@"
insert into ship_to_defaults (ship_to_code, default_freight_per_case)
values (@code, @freightRate)
on conflict (ship_to_code) do
update set default_freight_per_case = @freightRate;", new { code, freightRate });
        }
    }

    public async Task UpdatePriceLevelFreightRate(string priceLevel, decimal? freightRate)
    {
        if (freightRate == null) {
            await GetConnection().ExecuteAsync("DELETE FROM price_level_freight_rates WHERE price_level = @priceLevel;", new { priceLevel });
        } else {
            await GetConnection().ExecuteAsync(@"
insert into price_level_freight_rates (price_level, default_freight_per_case)
values (@priceLevel, @freightRate)
on conflict (price_level) do
update set default_freight_per_case = @freightRate;", new { priceLevel, freightRate });
        }
    }

    public async Task<IEnumerable<ProductShipToDefault>> ProductShipToDefaults(List<int> shipToIds)
    {
        if (!shipToIds.Any()) {
            return new List<ProductShipToDefault>();
        }

        var ids = string.Join(", ", shipToIds);
        return await GetConnection()
            .QueryAsync<ProductShipToDefault>($"SELECT * FROM product_ship_to_defaults WHERE ship_to_id IN ({ids});");
    }

    public async Task<ProductShipToDefault> GetProductShipToDefault(int spireInventoryId, int shipToId) =>
        (await GetConnection().QueryFirstOrDefaultAsync<ProductShipToDefault>(
            "SELECT * FROM product_ship_to_defaults " +
            "WHERE spire_inventory_id = @spireInventoryId AND ship_to_id = @shipToId",
            new { spireInventoryId, shipToId })) ?? new ProductShipToDefault();

    public async Task DeleteProductCustomerDefault(int id) =>
        await GetConnection().ExecuteAsync("DELETE FROM product_customer_defaults WHERE id = @id;", new { id });

    public async Task<ProductCustomerDefault?> UpdateProductCustomerDefaultCustomer(int id, string customerItemCode) =>
        await GetConnection().QueryFirstOrDefaultAsync<ProductCustomerDefault>(
            "UPDATE product_customer_defaults SET customer_item_code = @customerItemCode WHERE id = @id; " +
            "SELECT * FROM product_customer_defaults WHERE id = @id;", new { id, customerItemCode });

    public async Task DeleteProductShipToDefault(int id) =>
        await GetConnection().ExecuteAsync("DELETE FROM product_ship_to_defaults WHERE id = @id;", new { id });

    public async Task<ProductShipToDefault?> UpdateProductShipToDefault(int id, bool hasPotCover, string? potCover,
        string? upc, bool weightsAndMeasures, string? retail, decimal? unitPrice, string? customerItemCode) =>
        await GetConnection().QueryFirstOrDefaultAsync<ProductShipToDefault>("UPDATE product_ship_to_defaults " +
                                                                             "SET has_pot_cover = @hasPotCover, pot_cover = @potCover, upc = @upc, weights_and_measures = @weightsAndMeasures, retail = @retail, unit_price = @unitPrice, customer_item_code = @customerItemCode WHERE id = @id; " +
                                                                             "SELECT * FROM product_ship_to_defaults WHERE id = @id;",
            new { id, hasPotCover, potCover, upc, weightsAndMeasures, retail, unitPrice, customerItemCode });

    public async Task<IEnumerable<ProductDefault>> ProductDefaults()
    {
        await using var multi = await GetConnection().QueryMultipleAsync(
            @"SELECT *, boekestyn_quantity_per_finished_item AS quantity_per_finished_item FROM product_defaults;" +
            "SELECT * FROM product_default_boekestyn_products;" +
            "SELECT * FROM product_default_boekestyn_product_overrides;");

        var defaults = (await multi.ReadAsync<ProductDefault>()).ToList();
        var products = (await multi.ReadAsync<ProductDefaultBoekestynProduct>()).ToLookup(p => p.SpireInventoryId);
        var overrides =
            (await multi.ReadAsync<ProductDefaultBoekestynProductOverride>()).ToLookup(o => o.SpireInventoryId);

        foreach (var @default in defaults) {
            if (products.Contains(@default.SpireInventoryId)) {
                @default.Products.AddRange(products[@default.SpireInventoryId]);
            }

            if (overrides.Contains(@default.SpireInventoryId)) {
                @default.Overrides.AddRange(overrides[@default.SpireInventoryId]);
            }
        }

        return defaults;
    }

    public async Task<ProductDefault?> GetProductDefault(int spireInventoryId) =>
        await GetConnection().QueryFirstOrDefaultAsync<ProductDefault>(
            "SELECT *, boekestyn_quantity_per_finished_item AS quantity_per_finished_item FROM product_defaults WHERE spire_inventory_id = @spireInventoryId;",
            new { spireInventoryId });

    public async Task UpdateProductDefault(int spireInventoryId, string? boekestynPlantId,
        string? boekestynCustomerAbbreviation, decimal? upgradeLabourHours, int? quantityPerFinishedItem,
        bool isUpgrade, bool ignoreOverrideQuantity,
        List<ProductDefaultBoekestynProduct>? products = null,
        List<ProductDefaultBoekestynProductOverride>? overrides = null)
    {
        await using var connection = GetConnection();

        var existingLabourHours = await connection.QueryFirstOrDefaultAsync<decimal?>(
            "SELECT upgrade_labour_hours FROM product_defaults WHERE spire_inventory_id = @spireInventoryId;",
            new { spireInventoryId });

        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        await connection.ExecuteAsync(
            "INSERT INTO product_defaults (spire_inventory_id, boekestyn_plant_id, boekestyn_customer_abbreviation, upgrade_labour_hours, boekestyn_quantity_per_finished_item, is_upgrade, ignore_override_quantity) " +
            "VALUES (@spireInventoryId, @boekestynPlantId, @boekestynCustomerAbbreviation, @upgradeLabourHours, @quantityPerFinishedItem, @isUpgrade, @ignoreOverrideQuantity) " +
            "ON CONFLICT (spire_inventory_id) " +
            "DO UPDATE SET boekestyn_plant_id = @boekestynPlantId, " +
            "boekestyn_customer_abbreviation = @boekestynCustomerAbbreviation, " +
            "upgrade_labour_hours = @upgradeLabourHours, " +
            "boekestyn_quantity_per_finished_item = @quantityPerFinishedItem, " +
            "is_upgrade = @isUpgrade, " +
            "ignore_override_quantity = @ignoreOverrideQuantity;",
            new {
                spireInventoryId, boekestynPlantId, boekestynCustomerAbbreviation, upgradeLabourHours,
                quantityPerFinishedItem, isUpgrade, ignoreOverrideQuantity
            }, transaction: tx);

        if (products != null) {
            var inserts = products.Where(p => p.Id <= 0).ToList();
            var updates = products.Where(p => p.Id > 0).ToList();

            if (updates.Any()) {
                var ids = string.Join(", ", updates.Select(p => p.Id));
                await connection.ExecuteAsync(
                    $"DELETE FROM product_default_boekestyn_products WHERE spire_inventory_id = @spireInventoryId AND id NOT IN ({ids})",
                    new { spireInventoryId },
                    transaction: tx);

                await connection.ExecuteAsync(
                    "UPDATE product_default_boekestyn_products SET boekestyn_plant_id = @boekestynPlantId, " +
                    "boekestyn_customer_abbreviation = @boekestynCustomerAbbreviation, " +
                    "quantity_per_finished_item = @quantityPerFinishedItem " +
                    "WHERE id = @Id;",
                    updates.Select(u => new
                        { u.BoekestynPlantId, u.BoekestynCustomerAbbreviation, u.QuantityPerFinishedItem, u.Id }),
                    transaction: tx);
            }

            if (inserts.Any()) {
                await connection.ExecuteAsync(
                    "INSERT INTO product_default_boekestyn_products (spire_inventory_id, boekestyn_plant_id, boekestyn_customer_abbreviation, quantity_per_finished_item) " +
                    "VALUES (@spireInventoryId, @boekestynPlantId, @boekestynCustomerAbbreviation, @quantityPerFinishedItem);",
                    inserts.Select(i => new {
                        i.SpireInventoryId, i.BoekestynPlantId, i.BoekestynCustomerAbbreviation,
                        i.QuantityPerFinishedItem
                    }),
                    transaction: tx);
            }
        }

        if (overrides != null) {
            var inserts = overrides.Where(p => p.Id <= 0).ToList();
            var updates = overrides.Where(p => p.Id > 0).ToList();

            if (updates.Any()) {
                var ids = string.Join(", ", updates.Select(p => p.Id));
                await connection.ExecuteAsync(
                    $"DELETE FROM product_default_boekestyn_product_overrides WHERE spire_inventory_id = @spireInventoryId AND id NOT IN ({ids});",
                    new { spireInventoryId },
                    transaction: tx);

                await connection.ExecuteAsync(
                    "UPDATE product_default_boekestyn_product_overrides SET boekestyn_plant_id = @boekestynPlantId, " +
                    "boekestyn_customer_abbreviation = @boekestynCustomerAbbreviation, " +
                    "quantity_per_finished_item = @quantityPerFinishedItem, " +
                    "start_week = @startWeek," +
                    "end_week = @endWeek " +
                    "WHERE id = @Id;",
                    updates.Select(u => new {
                        u.BoekestynPlantId, u.BoekestynCustomerAbbreviation,
                        u.QuantityPerFinishedItem, u.StartWeek, u.EndWeek, u.Id
                    }),
                    transaction: tx);
            }

            if (inserts.Any()) {
                await connection.ExecuteAsync(
                    "INSERT INTO product_default_boekestyn_product_overrides (spire_inventory_id, boekestyn_plant_id, boekestyn_customer_abbreviation, " +
                    "quantity_per_finished_item, start_week, end_week) " +
                    "VALUES (@spireInventoryId, @boekestynPlantId, @boekestynCustomerAbbreviation, @quantityPerFinishedItem, @startWeek, @endWeek);",
                    inserts.Select(i => new {
                        i.SpireInventoryId, i.BoekestynPlantId, i.BoekestynCustomerAbbreviation,
                        i.QuantityPerFinishedItem, i.StartWeek, i.EndWeek
                    }),
                    transaction: tx);
            }
        }

        await tx.CommitAsync();

        // if the labour hours weren't set for the default, update existing Prebooks
        if (existingLabourHours.GetValueOrDefault() == default && upgradeLabourHours.GetValueOrDefault() != default) {
            await connection.ExecuteAsync(
                @"update prebook_items set upgrade_labour_hours = @upgradeLabourHours where spire_inventory_id = @spireInventoryId and prebook_id in (
    select id from prebooks where required_date >= current_date
);", new { upgradeLabourHours, spireInventoryId });
        }
    }

    public async Task UpdateExistingOrderProductDefaults(int spireInventoryId)
    {
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();
        await connection.ExecuteAsync("SELECT update_product_default_orders (@spireInventoryId);",
            new { spireInventoryId }, transaction: tx);
        await tx.CommitAsync();
    }

    public async Task<IEnumerable<string>> PotCoversForCustomer(int customerId) =>
        await GetConnection()
            .QueryAsync<string>("SELECT pot_cover FROM customer_pot_covers WHERE customer_id = @customerId;",
                new { customerId });

    public async Task UpdateCustomerPotCover(int customerId, string potCover) =>
        await GetConnection().ExecuteAsync("INSERT INTO customer_pot_covers (customer_id, pot_cover) " +
                                           "VALUES (@customerId, @potCover) " +
                                           "ON CONFLICT (customer_id, pot_cover) " +
                                           "DO UPDATE SET pot_cover = @potCover;", new { customerId, potCover });

    public async Task DeleteCustomerPotCover(int customerId, string potCover) =>
        await GetConnection()
            .ExecuteAsync(
                "DELETE FROM customer_pot_covers WHERE customer_id = @customerId AND pot_cover ilike @potCover;",
                new { customerId, potCover });

    public async Task<IEnumerable<UpgradeOption>> UpgradeOptions() =>
        await GetConnection()
            .QueryAsync<UpgradeOption>("SELECT * FROM upgrade_options ORDER BY container_pick_description;");

    public async Task InsertOrUpdateUpgradeOption(string containerPickDescription, string? origins, string? costs, string? tariffCode) =>
        await GetConnection().ExecuteAsync("INSERT INTO upgrade_options (container_pick_description, origins, costs, tariff_code) " +
                                           "VALUES (@containerPickDescription, @origins, @costs, @tariffCode) " +
                                           "ON CONFLICT (container_pick_description) DO " +
                                           "UPDATE SET origins = @origins, costs = @costs, tariff_code = coalesce(@tariffCode, upgrade_options.tariff_code);",
            new { containerPickDescription, origins, costs, tariffCode });

    public async Task UpdateUpgradeOption(int id, string containerPickDescription, string? origins, string? costs, string? tariffCode) =>
        await GetConnection().ExecuteAsync(
            "UPDATE upgrade_options SET container_pick_description = @containerPickDescription, origins = @origins, costs = @costs, tariff_code = coalesce(@tariffCode, tariff_code) " +
            "WHERE id = @id;", new { id, containerPickDescription, origins, costs, tariffCode });

    public async Task DeleteUpgradeOption(int id) =>
        await GetConnection().ExecuteAsync("DELETE FROM upgrade_options WHERE id = @id;", new { id });

    public async Task InsertOrUpdateSeason(string name, DateTime? seasonDate) =>
        await GetConnection().ExecuteAsync("INSERT INTO seasons (name, season_date) " +
                                           "VALUES (@name, @seasonDate) " +
                                           "ON CONFLICT (name) DO " +
                                           "UPDATE SET season_date = @seasonDate;", new { name, seasonDate });

    public async Task<SpireRefreshItem> RefreshSpireItems(DateTime? refreshDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", refreshDate, DbType.Date);
        return await GetConnection()
            .QuerySingleAsync<SpireRefreshItem>("SELECT * FROM refresh_spire_products(@required_start_date);",
                parameters);
    }

    public async Task<List<PriceDeviationWarning>> PriceDeviationWarnings() =>
        (await GetConnection().QueryAsync<PriceDeviationWarning>(
            "SELECT * FROM price_deviation_warnings ORDER BY coalesce(max_unit_price, 100000), coalesce(min_pack_size, 0);"))
        .ToList();

    public async Task<IEnumerable<DefaultVendorOverride>> DefaultVendorOverrides()
    {
        await using var multi = await GetConnection().QueryMultipleAsync(@"
SELECT * FROM default_vendor_overrides ORDER BY spire_part_numbers[1];
SELECT * FROM default_vendor_override_items");
        var overrides = (await multi.ReadAsync<DefaultVendorOverride>()).ToList();
        var items = (await multi.ReadAsync<DefaultVendorOverrideItem>()).ToLookup(i => i.DefaultVendorOverrideId);
        foreach (var @override in overrides.Where(o => items.Contains(o.Id))) {
            @override.Items.AddRange(items[@override.Id]);
        }

        return overrides;
    }

    public async Task InsertVendorOverride(DefaultVendorOverride defaultVendorOverride)
    {
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var id = await connection.QuerySingleAsync<int>(
            "INSERT INTO default_vendor_overrides (spire_part_numbers)" +
            "VALUES (@spirePartNumbers) " +
            "RETURNING id;",
            new {defaultVendorOverride.SpirePartNumbers},
            transaction: tx);

        await connection.ExecuteAsync(
            "INSERT INTO default_vendor_override_items (default_vendor_override_id, start_week, end_week, vendor_id) " +
            "VALUES (@id, @startWeek, @endWeek, @vendorId);",
            defaultVendorOverride.Items.Select(i => new {
                id,
                i.StartWeek,
                i.EndWeek,
                i.VendorId
            }), transaction: tx);

        await tx.CommitAsync();
    }

    public async Task UpdateVendorOverride(DefaultVendorOverride defaultVendorOverride)
    {
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        await connection.ExecuteAsync(
            "UPDATE default_vendor_overrides SET spire_part_numbers = @spirePartNumbers WHERE id = @id;",
            new {defaultVendorOverride.SpirePartNumbers, defaultVendorOverride.Id},
            transaction: tx);

        await connection.ExecuteAsync("DELETE FROM default_vendor_override_items WHERE default_vendor_override_id = @id;",
            new { defaultVendorOverride.Id }, transaction: tx);

        await connection.ExecuteAsync(
            "INSERT INTO default_vendor_override_items (default_vendor_override_id, start_week, end_week, vendor_id) " +
            "VALUES (@id, @startWeek, @endWeek, @vendorId);",
            defaultVendorOverride.Items.Select(i => new {
                defaultVendorOverride.Id,
                i.StartWeek,
                i.EndWeek,
                i.VendorId
            }), transaction: tx);

        await tx.CommitAsync();
    }

    public async Task DeleteDefaultVendorOverride(int id) =>
        await GetConnection().ExecuteAsync("DELETE FROM default_vendor_overrides WHERE id = @id;", new { id });

    public async Task<List<PriceLevelFreightRate>> GetPriceLevelFreightRates() =>
        (
            await GetConnection().QueryAsync<PriceLevelFreightRate>("SELECT * FROM price_level_freight_rates;")
        ).ToList();
}