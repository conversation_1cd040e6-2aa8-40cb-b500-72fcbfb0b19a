using System;
using System.Collections.Generic;

namespace FloraPack.API.Reports
{
    public partial class FutureOrderSummary : GrapeCity.ActiveReports.SectionReport
    {
        private bool _isFirstPage = true;

        public FutureOrderSummary(FutureOrderSummaryReportFutureOrder futureOrder, List<FutureOrderSummaryReportFutureOrderItem> items)
        {
            InitializeComponent();

            txtTotalPrice.Visible = futureOrder.TotalPrice > 0;
            lblSpecialLabels.Visible = futureOrder.RequiresLabels;
            txtSpecialLabels.Visible = futureOrder.RequiresLabels;
            txtSpecialLabelDetails.Visible = futureOrder.RequiresLabels && !string.IsNullOrWhiteSpace(futureOrder.SpecialLabelDetails);

            txtPhytos.Visible = futureOrder.RequiresPhytos;

            lblShipmentDetails.Visible = !string.IsNullOrWhiteSpace(futureOrder.SpireNotes);
            txtShipmentDetails.Visible = !string.IsNullOrWhiteSpace(futureOrder.SpireNotes);

            lblInternalNotes.Visible = !string.IsNullOrWhiteSpace(futureOrder.InternalNotes);
            txtInternalNotes.Visible = !string.IsNullOrWhiteSpace(futureOrder.InternalNotes);

            lblFreight.Visible = !string.IsNullOrWhiteSpace(futureOrder.FreightDisplay);
            txtFreight.Visible = !string.IsNullOrWhiteSpace(futureOrder.FreightDisplay);

            DataSource = new List<FutureOrderSummaryReportFutureOrder> { futureOrder };

            srptItems.Report = new FutureOrderSummaryItem { DataSource = items };
        }

        private void OnPageFooterFormat(object sender, EventArgs e)
        {
            if (!_isFirstPage) {
                lblShipmentDetails.Visible = false;
                txtShipmentDetails.Visible = false;
                lblInternalNotes.Visible = false;
                txtInternalNotes.Visible = false;
                pageFooter.Height = infoPageNumber.Height;
            }

            _isFirstPage = false;
        }
    }

    public class FutureOrderSummaryReportFutureOrder
    {
        public int Id { get; set; }
        public string SpireSalesOrderNumber { get; set; } = string.Empty;
        public DateTime? RequiredDate { get; set; }
        public string ArrivalDate { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string ShipToName { get; set; } = string.Empty;
        public string BoxCode { get; set; } = string.Empty;
        public string CustomerPurchaseOrderNumber { get; set; } = string.Empty;
        public string SalespersonName { get; set; } = string.Empty;
        public string ShipViaName { get; set; } = string.Empty;
        public bool RequiresLabels { get; set; }
        public int TotalOrderQuantity { get; set; }
        public decimal TotalPrice { get; set; }
        public bool RequiresPhytos { get; set; }
        public string SpireNotes { get; set; } = string.Empty;
        public string InternalNotes { get; set; } = string.Empty;
        public decimal? FreightPerCase { get; set; }
        public decimal? FreightPerLoad { get; set; }
        public bool FreightIsActual { get; set; }
        public string SpecialLabelDetails { get; set; } = string.Empty;

        public string FreightDisplay => FreightPerCase.HasValue && FreightPerLoad.HasValue ?
            ($"{FreightPerCase:C2}/cs, {FreightPerLoad:C2} total" + (FreightIsActual ? " (actual)" : "")) : string.Empty;
    }
}
