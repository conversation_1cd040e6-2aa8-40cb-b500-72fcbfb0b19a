﻿using ClosedXML.Excel;
using static FloraPack.API.Controllers.PrebooksController;

namespace FloraPack.API.Reports.Prebooks;

public static class PrebookListReport
{
    public static MemoryStream Create(PrebookListReportModel model)
    {

        var hideBlankets = model.Items.All(i => i.IsBlanket) || model.Items.All(i => !i.IsBlanket);

        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 1;
        var col = 1;

        sheet.Cell(row, col).SetValue("Date");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Vendor");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Customer");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Ship To");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Salesperson");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Cell(row, col).SetValue("Season");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        if (!hideBlankets)
        {
            sheet.Cell(row, col).SetValue("Blanket");
            sheet.Cell(row, col).Style.Font.Bold = true;
            col++;
        }

        sheet.Cell(row, col).SetValue("Cases");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Range(row, 1, row, col).Style.Border.BottomBorder = XLBorderStyleValues.Thin;

        row++;
        foreach (var item in model.Items)
        {
            col = 1;

            if (item.RequiredDate != null && DateTime.TryParse(item.RequiredDate, out var date))
            {
                sheet.Cell(row, col).SetValue(date.ToString("MMM d, yyyy"));
            }
            col++;

            sheet.Cell(row, col).SetValue(item.Vendor);
            col++;

            sheet.Cell(row, col).SetValue(item.Customer);
            col++;

            sheet.Cell(row, col).SetValue(item.ShipTo);
            col++;

            sheet.Cell(row, col).SetValue(item.Salesperson);
            col++;

            sheet.Cell(row, col).SetValue(item.Season);
            col++;

            if (!hideBlankets)
            {
                sheet.Cell(row, col).SetValue(item.IsBlanket ? "Yes" : "No");
                col++;
            }

            sheet.Cell(row, col).SetValue(item.CaseCount);
            sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");

            row++;
        }

        sheet.Cell(row, col).FormulaR1C1 = $"=SUM(R[-{model.Items.Count}]C:R[-1]C)";
        sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");


        sheet.Range(row, 1, row, col).Style.Border.TopBorder = XLBorderStyleValues.Double;
        sheet.Range(row, 1, row, col).Style.Font.Bold = true;

        sheet.Columns(1, col).AdjustToContents();

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }
}