﻿using Dapper;
using FloraPack.API.Repositories.Prebooks;
using FloraPack.API.Utilities;

namespace FloraPack.API.Repositories.LargeOrders;

public class LargeOrderEmailRepository(IConfiguration configuration) : RepositoryBase(configuration)
{
    public const int LargeOrderThreshold = 100;

    public async Task Create(PrebookDetail prebook, List<string> to, string subject, string body, string from)
    {
        if (!prebook.Items.Any(i => i.OrderQuantity >= LargeOrderThreshold && PackQuantityParser.Parse(i.Description) > 1)) {
            return;
        }

        var now = DateTime.Now;

        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var id = await connection.ExecuteScalarAsync<int>(@"insert into prebook_large_order_emails (
    prebook_id,
    required_date,
    created,
    created_by,
    subject,
    body,
    ""to"",
    ""from""
) values (
    @prebookId,
    @requiredDate,
    @now,
    @from,
    @subject,
    @body,
    @to,
    @from
) returning id;",
            new {
                PrebookId = prebook.Id,
                prebook.RequiredDate,
                now,
                from,
                subject,
                body,
                To = string.Join(", ", to)
            }, transaction: tx);

        await connection.ExecuteAsync(@"insert into prebook_large_order_email_items (
    prebook_large_order_email_id,
    prebook_item_id,
    spire_part_number,
    ""description"",
    order_quantity,
    comments
) values (
    @largeOrderEmailId,
    @id,
    @spirePartNumber,
    @description,
    @orderQuantity,
    @comments
);
", prebook.Items
            .Where(i => i.OrderQuantity >= LargeOrderThreshold && PackQuantityParser.Parse(i.Description) > 1)
            .Select(i => new {
                LargeOrderEmailId = id,
                i.Id,
                i.SpirePartNumber,
                i.Description,
                i.OrderQuantity,
                i.Comments
            }), transaction: tx);

        await tx.CommitAsync();
    }

    public async Task<bool> HasChanged(PrebookDetail detail)
    {
        var detailItems = detail.Items.Where(i => i.OrderQuantity >= LargeOrderThreshold && PackQuantityParser.Parse(i.Description) > 1).ToList();

        var connection = GetConnection();
        await using var multi = await connection.QueryMultipleAsync(@"
            select * from prebook_large_order_emails where prebook_id = @id order by created desc limit 1;
            select i.* from prebook_large_order_email_items i join prebook_large_order_emails e on e.id = i.prebook_large_order_email_id where e.prebook_id = @id;
", new { detail.Id });

        var email = await multi.ReadFirstOrDefaultAsync<LargeOrderEmail>();
        if (email == null) {
            return detailItems.Any();
        }

        if (detail.RequiredDate != email.RequiredDate) {
            return true;
        }

        var items = (await multi.ReadAsync<LargeOrderEmailItem>())
            .Where(i => i.PrebookLargeOrderEmailId == email.Id)
            .ToList();

        // new items not on the previous email
        if (detailItems.Any(i => !items.Any(ei => ei.PrebookItemId == i.Id))) {
            return true;
        }

        // removed items
        if (items.Any(ei => !detailItems.Any(i => i.Id == ei.PrebookItemId))) {
            return true;
        }

        //items that have changed
        if (items.Any(i => {
                var detailItem = detailItems.First(di => di.Id == i.PrebookItemId);
                return detailItem.OrderQuantity != i.OrderQuantity ||
                       detailItem.SpirePartNumber != i.SpirePartNumber ||
                       detailItem.Comments != i.Comments;
            })) {
            return true;
        }

        return false;
    }
}