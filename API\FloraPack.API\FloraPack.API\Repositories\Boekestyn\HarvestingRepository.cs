using System.Data;
using Dapper;
using DocumentFormat.OpenXml.Office2021.PowerPoint.Comment;
using FloraPack.API.Controllers;
using FloraPack.API.Repositories.Boekestyn.Entities;

namespace FloraPack.API.Repositories.Boekestyn;
public class HarvestingRepository(IConfiguration configuration) : BoekestynRepository(configuration)
{
    public async Task<IEnumerable<HarvestingLine>> GetHarvestingLines() =>
          await GetConnection().QueryAsync<HarvestingLine>("SELECT * FROM harvesting_lines");

    public async Task<IEnumerable<HarvestingSchedule>> GetHarvestingSchedules(DateTime date)
    {
        var multi = await GetConnection().QueryMultipleAsync(@"
SELECT id, to_char(date, 'yyyy-MM-dd') date, line_id FROM harvesting_schedules WHERE date = @date;
SELECT o.* FROM harvesting_schedules s join harvesting_work_orders o on s.id = o.schedule_id WHERE s.date = @date ORDER BY o.sort_order;
SELECT v.* FROM harvesting_schedules s join harvesting_work_orders o on s.id = o.schedule_id join harvesting_work_order_varieties v on o.id = v.work_order_id WHERE s.date = @date;
SELECT l.* FROM harvesting_schedules s join harvesting_work_orders o on s.id = o.schedule_id join harvesting_work_order_labour l on o.id = l.work_order_id WHERE s.date = @date;
SELECT lv.id, v.work_order_id, v.name variety_name, lv.harvested, lv.thrown_out, lv.number_two, lv.comments 
FROM harvesting_schedules s
JOIN harvesting_work_orders o on s.id = o.schedule_id
JOIN harvesting_work_order_varieties v on o.id = v.work_order_id
JOIN harvesting_work_order_labour_varieties lv on v.id = lv.variety_id
WHERE s.date = @date;", new { date });

        var schedules = (await multi.ReadAsync<HarvestingSchedule>()).ToList();
        var orders = (await multi.ReadAsync<HarvestingWorkOrder>()).ToLookup(o => o.ScheduleId);
        var varieties = (await multi.ReadAsync<HarvestingWorkOrderVariety>()).ToLookup(v => v.WorkOrderId);
        var labour = (await multi.ReadAsync<HarvestingWorkOrderLabour>()).ToLookup(l => l.WorkOrderId);
        var labourVarieties = (await multi.ReadAsync<HarvestingWorkOrderLabourVarietyItem>()).ToLookup(lv => lv.WorkOrderId);

        foreach (var line in schedules.Where(l => orders.Contains(l.Id))) {
            var workOrders = orders[line.Id].ToList();
            foreach (var order in workOrders) {
                if (varieties.Contains(order.Id)) {
                    order.Varieties.AddRange(varieties[order.Id]);
                }

                if (labour.Contains(order.Id)) {
                    order.Labour.AddRange(labour[order.Id]);
                }

                if (labourVarieties.Contains(order.Id)) {
                    order.LabourVarieties.AddRange(labourVarieties[order.Id]);
                }
            }

            line.WorkOrders.AddRange(workOrders);
        }

        return schedules;
    }

    public async Task<IEnumerable<HarvestingAdminOrderItem>> GetAdminOrderItems(DateTime? start, DateTime? end) =>
        await GetConnection().QueryAsync<HarvestingAdminOrderItem>(@"
select
  id sticking_work_order_id,
  order_id,
  order_number,
  plant_size,
  plant_crop,
  pots,
  colour,
  flower_date,
  order_comments notes
from
  sticking_work_orders
where
  flower_date between coalesce(@start, flower_date) and coalesce(@end, flower_date);", new { start, end });

    public async Task<HarvestingAdminOrderItem?> GetAdminOrderItem(string orderId) =>
        await GetConnection().QuerySingleOrDefaultAsync<HarvestingAdminOrderItem>(@"
select
  id sticking_work_order_id,
  order_id,
  order_number,
  plant_size,
  plant_crop,
  pots,
  colour,
  flower_date,
  order_comments notes
from
  sticking_work_orders
where
  order_id = @orderId;", new { orderId });

    public async Task<IEnumerable<HarvestingOrderRound>> GetHarvestingOrderRounds(string[] orderIds) =>
        await GetConnection().QueryAsync<HarvestingOrderRound>("SELECT * FROM boekestyn_harvesting_rounds(@work_orders_param);", new { work_orders_param = orderIds });

    public async Task<IEnumerable<HarvestingOrderRoundVariety>> GetHarvestingOrderRoundVarieties(string[] orderIds) =>
        await GetConnection().QueryAsync<HarvestingOrderRoundVariety>("SELECT * FROM boekestyn_harvesting_round_varieties(@work_orders_param);", new { work_orders_param = orderIds });

    public async Task CreateWorkOrders(CreateWorkOrdersModel model, bool deleteExisting = true)
    {
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var existingSchedules = await connection.QueryAsync<int>("SELECT DISTINCT s.id FROM harvesting_schedules s JOIN harvesting_work_orders o on s.id = o.schedule_id WHERE o.order_id = @orderId;", new { model.OrderId }, transaction: tx);
        if (deleteExisting)
        {
            foreach (var schedule in existingSchedules.Where(s => !model.Schedules.Any(sch => sch.Id == s)))
            {
                await connection.ExecuteAsync("DELETE FROM harvesting_schedules WHERE id = @id;", new { id = schedule }, transaction: tx);
            }
        }

        foreach (var schedule in model.Schedules) {
            var scheduleId = schedule.Id;

            if (scheduleId <= 0) {
                scheduleId = await connection.QuerySingleAsync<int>(@"
with s as (select id, date, line_id from harvesting_schedules where date = @date and line_id = @lineId), 
i as (insert into harvesting_schedules (date, line_id) select @date, @lineId where not exists (select 1 from s) returning id)
select id from i union all select id from s", new { scheduleId, schedule.LineId, schedule.Date }, transaction: tx);
            } else {
                var existingWorkOrders = await connection.QueryAsync<int>("SELECT id FROM harvesting_work_orders WHERE schedule_id = @scheduleId and order_id = @orderId", new { scheduleId, model.OrderId }, transaction: tx);
                foreach (var deleted in existingWorkOrders.Where(o => !schedule.WorkOrders.Any(wo => wo.Id == o))) {
                    await connection.ExecuteAsync("DELETE FROM harvesting_work_orders WHERE id = @id", new { id = deleted }, transaction: tx);
                }

                // find or create a new schedule
                var existingScheduleId = await connection.QuerySingleAsync<int>(@"
with s as (select id, date, line_id from harvesting_schedules where date = @date and line_id = @lineId), 
i as (insert into harvesting_schedules (date, line_id) select @date,  @lineId where not exists (select 1 from s) returning id)
select id from i union all select id from s", new { scheduleId, schedule.LineId, schedule.Date }, transaction: tx);
                // move the work orders to that schedule
                await connection.ExecuteAsync("UPDATE harvesting_work_orders SET schedule_id = @existingScheduleId WHERE schedule_id = @id and order_id = @orderId;", new { schedule.Id, existingScheduleId, model.OrderId }, transaction: tx);
                // now delete any schedules that have no work orders
                await connection.ExecuteAsync("DELETE FROM harvesting_schedules s WHERE NOT EXISTS (SELECT * FROM harvesting_work_orders WHERE schedule_id = s.id);", transaction: tx);
            }

            var sortOrder = await connection.QuerySingleAsync<int>("SELECT coalesce(max(sort_order), 0) + 1 FROM harvesting_work_orders WHERE schedule_id = @scheduleId", new { scheduleId }, transaction: tx);

            foreach (var order in schedule.WorkOrders) {
                var workOrderId = order.Id;

                if (workOrderId <= 0) {
                    workOrderId = await connection.ExecuteScalarAsync<int>(@"
INSERT INTO harvesting_work_orders (schedule_id, sort_order, order_id, order_number, plant_size, plant_crop, customer, pots, crew_size, order_comments, default_expected_harvest_percentage, final_round, harvesting_comments, sticking_work_order_id) 
VALUES (@scheduleId, @sortOrder, @orderId, @orderNumber, @plantSize, @plantCrop, @customer, @pots, @crewSize, @notes, @defaultExpectedHarvestPercentage, @finalRound, @harvestingComments, @stickingWorkOrderId)
RETURNING harvesting_work_orders.id;",
                        new {
                            scheduleId,
                            sortOrder = sortOrder++,
                            order.Order.OrderId,
                            order.Order.OrderNumber,
                            order.Order.PlantSize,
                            order.Order.PlantCrop,
                            order.Order.Customer,
                            order.Order.Pots,
                            order.CrewSize,
                            order.Order.Notes,
                            order.DefaultExpectedHarvestPercentage,
                            order.FinalRound,
                            order.HarvestingComments,
                            order.StickingWorkOrderId
                        },
                        transaction: tx);
                } else {
                    var existingVarieties = await connection.QueryAsync<int>("SELECT id FROM harvesting_work_order_varieties WHERE work_order_id = @workOrderId", new { workOrderId }, transaction: tx);
                    foreach (var deleted in existingVarieties.Where(v => !order.Varieties.Any(o => o.Id == v))) {
                        await connection.ExecuteAsync("DELETE FROM harvesting_work_order_varieties WHERE id = @id", new { id = deleted }, transaction: tx);
                    }

                    await connection.ExecuteScalarAsync<int>(@"
UPDATE harvesting_work_orders SET crew_size = @crewSize, default_expected_harvest_percentage = @defaultExpectedHarvestPercentage, final_round = @finalRound, harvesting_comments = @harvestingComments
WHERE id = @workOrderId;", new { workOrderId, order.CrewSize, order.DefaultExpectedHarvestPercentage, order.FinalRound, order.HarvestingComments }, transaction: tx);
                }

                foreach (var variety in order.Varieties) {
                    if (variety.Id <= 0) {
                        await connection.ExecuteAsync(@"
INSERT INTO harvesting_work_order_varieties (work_order_id, name, pots, expected_harvest_percentage, comment) 
VALUES (@workOrderId, @name, @pots, @expectedHarvestPercentage, @comment);",
                            new { workOrderId, variety.Name, variety.Pots, variety.ExpectedHarvestPercentage, variety.Comment },
                            transaction: tx);
                    } else {
                        await connection.ExecuteAsync(@"
UPDATE harvesting_work_order_varieties SET pots = @pots, expected_harvest_percentage = @expectedHarvestPercentage, comment = @comment 
WHERE id = @id;",
                            new { variety.Id, variety.Pots, variety.ExpectedHarvestPercentage, variety.Comment },
                            transaction: tx);
                    }
                }

                await connection.ExecuteAsync(@"
UPDATE harvesting_work_orders SET sort_order = u.sort_order
FROM (SELECT id, row_number() over (order by sort_order, id) sort_order from harvesting_work_orders u where schedule_id = @scheduleId) u
where u.id = harvesting_work_orders.id;", new { scheduleId }, transaction: tx);
            }
        }

        await tx.CommitAsync();
    }

    public async Task<IEnumerable<HarvestingSchedule>> GetSchedulesForOrderId(string orderId)
    {
        var multi = await GetConnection().QueryMultipleAsync(@"
SELECT DISTINCT s.id, to_char(s.date, 'yyyy-MM-dd') date, s.line_id FROM harvesting_schedules s JOIN harvesting_work_orders o on s.id = o.schedule_id WHERE o.order_id = @orderId;
SELECT * FROM harvesting_work_orders WHERE order_id = @orderId ORDER BY sort_order;
SELECT v.* FROM harvesting_work_orders o JOIN harvesting_work_order_varieties v on o.id = v.work_order_id WHERE o.order_id = @orderId;
SELECT l.* FROM harvesting_work_orders o JOIN harvesting_work_order_labour l on o.id = l.work_order_id WHERE o.order_id = @orderId;
SELECT lv.id, v.work_order_id, v.name variety_name, lv.harvested, lv.thrown_out, lv.number_two, lv.comments 
FROM harvesting_work_orders o
JOIN harvesting_work_order_varieties v on o.id = v.work_order_id
JOIN harvesting_work_order_labour_varieties lv on v.id = lv.variety_id
WHERE o.order_id = @orderId;", new { orderId });

        var schedules = (await multi.ReadAsync<HarvestingSchedule>()).ToList();
        var orders = (await multi.ReadAsync<HarvestingWorkOrder>()).ToLookup(o => o.ScheduleId);
        var varieties = (await multi.ReadAsync<HarvestingWorkOrderVariety>()).ToLookup(v => v.WorkOrderId);
        var labour = (await multi.ReadAsync<HarvestingWorkOrderLabour>()).ToLookup(l => l.WorkOrderId);
        var labourVarieties = (await multi.ReadAsync<HarvestingWorkOrderLabourVarietyItem>()).ToLookup(lv => lv.WorkOrderId);

        foreach (var line in schedules.Where(l => orders.Contains(l.Id))) {
            var workOrders = orders[line.Id].ToList();
            foreach (var order in workOrders) {
                if (varieties.Contains(order.Id)) {
                    order.Varieties.AddRange(varieties[order.Id]);
                }

                if (labour.Contains(order.Id)) {
                    order.Labour.AddRange(labour[order.Id]);
                }

                if (labourVarieties.Contains(order.Id)) {
                    order.LabourVarieties.AddRange(labourVarieties[order.Id]);
                }
            }

            line.WorkOrders.AddRange(workOrders);
        }

        return schedules;
    }

    public async Task<IEnumerable<HarvestingWorkOrderItem>> GetHarvestingWorkOrders(DateTime date)
    {
        await using var connection = GetConnection();

        var parameters = new DynamicParameters();
        parameters.Add("schedule_date_param", date, DbType.Date);
        var workOrders = (await connection.QueryAsync<HarvestingWorkOrderItem>("SELECT * FROM boekestyn_harvesting_work_orders(@schedule_date_param);", parameters)).ToList();
        var ids = workOrders.Count == 0 ? "-1" : string.Join(", ", workOrders.Select(w => w.Id));
        var varieties = (await connection.QueryAsync<HarvestingWorkOrderVariety>($"SELECT * FROM harvesting_work_order_varieties WHERE work_order_id IN ({ids})")).ToLookup(v => v.WorkOrderId);
        var labour = (await connection.QueryAsync<HarvestingWorkOrderLabour>(@$"
SELECT id, work_order_id, crew_size, to_char(cast(start_time at time zone 'UTC' at time zone 'EDT' as timestamp), 'yyyy-MM-dd HH24:MI:SS') start_time, 
to_char(cast(end_time at time zone 'UTC' at time zone 'EDT' as timestamp), 'yyyy-MM-dd HH24:MI:SS') end_time, comments, final_labour 
FROM harvesting_work_order_labour WHERE work_order_id IN ({ids})")).ToLookup(l => l.WorkOrderId);

        foreach (var order in workOrders)
        {
            if (varieties.Contains(order.Id))
            {
                order.Varieties.AddRange(varieties[order.Id]);
            }

            if (labour.Contains(order.Id))
            {
                order.Labour.AddRange(labour[order.Id]);
            }
        }

        return workOrders;
    }

    public async Task SortHarvestingWorkOrders(IEnumerable<HarvestingWorkOrderSort> workOrders) =>
        await GetConnection().ExecuteAsync("UPDATE harvesting_work_orders SET sort_order = @sortOrder WHERE id = @workOrderId;", workOrders);

    public async Task UpdateHarvestingWorkOrderComment(int id, string? comment) =>
        await GetConnection().ExecuteAsync("UPDATE harvesting_work_orders SET harvesting_comments = @comment WHERE id = @id;", new { id, comment });

    public async Task UpdateHarvestingWorkOrderCrewSize(int id, int crewSize) =>
        await GetConnection().ExecuteAsync("UPDATE harvesting_work_orders SET crew_size = @crewSize WHERE id = @id;", new { id, crewSize });

    public async Task DeleteHarvestingWorkOrder(int id)
    {
        await using var connection = GetConnection();
        var scheduleId = await connection.QuerySingleAsync<int>("SELECT schedule_id from harvesting_work_orders WHERE id = @id", new { id });
        await connection.ExecuteAsync(@"
DELETE FROM harvesting_work_orders WHERE id = @id;
update harvesting_work_orders set sort_order = u.sort_order
from (select id, row_number() over (order by sort_order, id) sort_order from harvesting_work_orders u where schedule_id = @scheduleId) u
where u.id = harvesting_work_orders.id;", new { id, scheduleId });
    }

    public async Task StartHarvestingLabour(int workOrderId, int crewSize)
    {
        await using var connection = GetConnection();
        await connection.ExecuteAsync("UPDATE harvesting_work_order_labour SET end_time = current_timestamp WHERE work_order_id = @workOrderId AND end_time IS NULL;",
            new { workOrderId, crewSize });
        await connection.ExecuteAsync("INSERT INTO harvesting_work_order_labour (work_order_id, crew_size) values (@workOrderId, @crewSize);", new { workOrderId, crewSize });
    }

    public async Task StopHarvestingLabour(int workOrderId, int crewSize, string? comments, bool finalLabour, List<HarvestLabourVarieties>? varieties = null)
    {
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();
        var labourId = await connection.QuerySingleOrDefaultAsync<int?>("SELECT id FROM harvesting_work_order_labour WHERE work_order_id = @workOrderId AND end_time IS NULL LIMIT 1;", new { workOrderId });

        await connection.ExecuteAsync("UPDATE harvesting_work_order_labour SET end_time = current_timestamp, comments = coalesce(@comments, comments), final_labour = @finalLabour WHERE work_order_id = @workOrderId AND end_time IS NULL;",
            new { workOrderId, crewSize, comments, finalLabour }, transaction: tx);

        if (varieties != null) {
            foreach (var variety in varieties)
            {
                if (variety.Harvested == 0 && variety.ThrownOut == 0 && variety.NumberTwos != 0)
                {
                    continue; // Skip varieties with no harvested or thrown out pots
                }
                var varietyId = await connection.QuerySingleAsync<int>("SELECT id FROM harvesting_work_order_varieties WHERE work_order_id = @workOrderId AND name = @varietyName;", new { workOrderId, variety.VarietyName });

                await connection.ExecuteAsync("INSERT INTO public.harvesting_work_order_labour_varieties (labour_id, variety_id, harvested, thrown_out) VALUES (@labourId, @varietyId, @harvested, @thrownOut);",
                    new { labourId, varietyId, variety.Harvested, variety.ThrownOut }, transaction: tx);

                Console.WriteLine("Number twos: " + variety.NumberTwos);

                if (variety.NumberTwos > 0)
                {
                    await connection.ExecuteAsync("INSERT INTO public.harvesting_work_order_labour_varieties (labour_id, variety_id, harvested, thrown_out, number_two) VALUES (@labourId, @varietyId, @numberTwos, 0, true);",
                        new { labourId, varietyId, numberTwos = variety.NumberTwos }, transaction: tx);
                }
            }
        }

        await tx.CommitAsync();
    }

    public async Task<IEnumerable<HarvestingLabourReportItem>> GetHarvestingLabourReportItems(DateTime start, DateTime end) =>
        await GetConnection().QueryAsync<HarvestingLabourReportItem>(@"
select
  s.date schedule_date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer,
  v.name variety,
  coalesce(sum(case when lv.number_two = false then lv.harvested else 0 end), 0) harvested,
  coalesce(sum(lv.thrown_out), 0) thrown_out,
  coalesce(sum(case when lv.number_two = true then lv.harvested else 0 end), 0) number_twos,
  avg(l.crew_size) crew_size,
  coalesce(sum(ROUND(EXTRACT(EPOCH FROM (l.end_time - l.start_time)) / 3600.0, 2)), 0) as actual_hours,
  coalesce(sum(ROUND(EXTRACT(EPOCH FROM (l.end_time - l.start_time)) / 3600.0, 2) * l.crew_size), 0) as man_hours,
  coalesce(string_agg(nullif(trim(l.comments), ''), '\n'), '') comments
from
  harvesting_schedules s
  join harvesting_work_orders wo on s.id = wo.schedule_id
  join harvesting_work_order_varieties v on wo.id = v.work_order_id
  left join harvesting_work_order_labour_varieties lv on v.id = lv.variety_id
  left join harvesting_work_order_labour l on lv.labour_id = l.id and l.end_time is not null
where
  s.date between @start and @end
group by
  s.date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer,
  v.name
order by
  s.date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer,
  v.name;
", new { start, end });

    public async Task<IEnumerable<HarvestingWorkOrderLabourVarietyItem>> GetHarvestingWorkOrderLabour(string orderId) =>
        await GetConnection().QueryAsync<HarvestingWorkOrderLabourVarietyItem>(@"
select lv.id, v.work_order_id, v.name variety_name, lv.harvested, lv.thrown_out, lv.comments 
from harvesting_work_orders o
join harvesting_work_order_varieties v on o.id = v.work_order_id
join harvesting_work_order_labour_varieties lv on v.id = lv.variety_id
where o.order_id = @orderId;", new { orderId });

    public async Task ThrowOutRemainder(int workOrderId) =>
        await GetConnection().ExecuteAsync(@"
with labour as (
  insert into harvesting_work_order_labour (work_order_id, crew_size, start_time, end_time, final_labour, harvest_complete)
  values (@workOrderId, 1, current_timestamp, current_timestamp, true, true)
  returning id
),
varieties as (
  select
    v.id,
    case when v.pots - coalesce(sum(lv.harvested + lv.thrown_out), 0) < 0 then 0
         else v.pots - coalesce(sum(lv.harvested + lv.thrown_out), 0) end as remaining
  from
    harvesting_work_order_varieties v
    left join harvesting_work_order_labour_varieties lv on lv.variety_id = v.id
  where
    work_order_id = @workOrderId
  group by
    v.id,
    v.pots
)
insert into harvesting_work_order_labour_varieties (labour_id, variety_id, harvested, thrown_out)
select
  l.id,
  v.id,
  0,
  v.remaining
from
labour l ,varieties v;", new { workOrderId });

    public async Task RemainderNumberTwos(int workOrderId) =>
        await GetConnection().ExecuteAsync(@"
with labour as (
  insert into harvesting_work_order_labour (work_order_id, crew_size, start_time, end_time, final_labour, harvest_complete)
  values (@workOrderId, 1, current_timestamp, current_timestamp, true, true)
  returning id
),
varieties as (
  select
    v.id,
    case when v.pots - coalesce(sum(lv.harvested + lv.thrown_out), 0) < 0 then 0
         else v.pots - coalesce(sum(lv.harvested + lv.thrown_out), 0) end as remaining
  from
    harvesting_work_order_varieties v
    left join harvesting_work_order_labour_varieties lv on lv.variety_id = v.id
  where
    work_order_id = @workOrderId
  group by
    v.id,
    v.pots
)
insert into harvesting_work_order_labour_varieties (labour_id, variety_id, harvested, thrown_out, number_two)
select
  l.id,
  v.id,
  v.remaining,
  0,
  true
from
labour l ,varieties v;", new { workOrderId });

    private static DateTime AddBusinessDays(DateTime startDate, int businessDays)
    {
        if (businessDays < 0)
            throw new ArgumentException("businessDays cannot be negative.", nameof(businessDays));

        var date = startDate;
        while (businessDays > 0)
        {
            date = date.AddDays(1);
            if (date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday)
            {
                businessDays--;
            }
        }
        return date;
    }

    public async Task SendBackRemainder(int workOrderId)
    {
        // Create a new harvest labour 2 days after the current one
        var currentWorkOrder = await GetConnection().QuerySingleOrDefaultAsync<HarvestingWorkOrder>("SELECT * FROM harvesting_work_orders WHERE id = @workOrderId", new { workOrderId });

        // set current work order as not final round
        await GetConnection().ExecuteAsync("UPDATE harvesting_work_orders SET final_round = false WHERE id = @workOrderId", new { workOrderId });

        // Get the schedule using schedule ID from the current work order
        var currentSchedule = await GetConnection().QuerySingleOrDefaultAsync<HarvestingSchedule>("SELECT * FROM harvesting_schedules WHERE id = @scheduleId", new { currentWorkOrder.ScheduleId });

        CreateWorkOrdersModel newWorkOrderModel = new CreateWorkOrdersModel();
        newWorkOrderModel.OrderId = currentWorkOrder.OrderId;
        newWorkOrderModel.Schedules = new List<HarvestOrderToScheduleModel>
        {
            new HarvestOrderToScheduleModel
            {
                Date = AddBusinessDays(DateTime.Parse(currentSchedule.Date), 2),
                LineId = currentSchedule.LineId,
                WorkOrders = new List<HarvestOrderToScheduleOrderModel>
                {
                    new HarvestOrderToScheduleOrderModel
                    {
                        Id = -1,
                        DefaultExpectedHarvestPercentage = 100, // Assuming 100% for sending back
                        FinalRound = true, 
                        HarvestingComments = "Remainder sent back",
                        Order = new HarvestingAdminOrderItem
                        {
                            OrderId = currentWorkOrder.OrderId,
                            OrderNumber = currentWorkOrder.OrderNumber,
                            PlantSize = currentWorkOrder.PlantSize,
                            PlantCrop = currentWorkOrder.PlantCrop,
                            Customer = currentWorkOrder.Customer,
                            Pots = currentWorkOrder.Pots,
                            Notes = currentWorkOrder.OrderComments + "\n(Remainder sent back)",
                            Varieties = currentWorkOrder.Varieties
                                .Select(v => new HarvestingOrder.HarvestingOrderVariety
                                {
                                    Name = v.Name,
                                    Cuttings = 0,
                                    Pots = v.Pots,
                                    Cases = 0,
                                    Comment = v.Comment
                                })
                                .ToList()
                        },
                        StickingWorkOrderId = currentWorkOrder.StickingWorkOrderId
                    }
                }
            }
        };

        await CreateWorkOrders(newWorkOrderModel, false);
    }

    public async Task TakeOutHarvesting(HarvestTakeoutModel model)
    {
        
    }

    public class HarvestingWorkOrderSort
    {
        public int WorkOrderId { get; init; }
        public int SortOrder { get; init; }
    }
}