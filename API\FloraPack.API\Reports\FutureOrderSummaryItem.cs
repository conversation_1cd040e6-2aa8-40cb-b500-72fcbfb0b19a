﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Web;
using FloraPack.API.Utilities;

namespace FloraPack.API.Reports
{
    public partial class FutureOrderSummaryItem : GrapeCity.ActiveReports.SectionReport
    {
        private const int BoekestynVendorId = 143;

        private int _formatIndex;
        private int _printIndex;

        public FutureOrderSummaryItem()
        {
            InitializeComponent();
        }

        private void OnBeforePrint(object sender, EventArgs e)
        {
            var height = detail.Height;
            rtfCases.Height = height;
            rtfDescription.Height = height;
            txtPackQuantity.Height = height;
            rtfPotCover.Height = height;
            txtUnitPrice.Height = height;
            rtfDateCode.Height = height;
            rtfUPC.Height = height;
            rtfRetail.Height = height;
            txtWeightsAndMeasures.Height = height;
            txtVendor.Height = height;
            rtfComments.Height = height;

            if (DataSource is List<FutureOrderSummaryReportFutureOrderItem> items && _printIndex < items.Count) {
                var item = items[_printIndex];
                txtSent.Visible = item.Sent;
                _printIndex++;
            }
        }

        private void OnFormat(object sender, EventArgs e)
        {
            if (DataSource is List<FutureOrderSummaryReportFutureOrderItem> items && _formatIndex < items.Count) {
                var item = items[_formatIndex];
                var quantityChanged = item.SpireOrderQuantity.HasValue &&
                                      item.SpireOrderQuantity.Value != item.OrderQuantity;
                var bold = item.IsNew || quantityChanged;
                var html =
                    "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='margin-left: 10px; font-family: Calibri; font-size: 8pt;" +
                    $"{(bold ? "font-weight: bold;" : "")}'>{(item.IsNew ? "*" : "")}{HttpUtility.HtmlEncode(item.Description)}</div>";

                if (!string.IsNullOrWhiteSpace(item.CustomerItemCode)) {
                    html += $"<div style='font-family: Calibri; font-size: 8pt; font-style: italic; margin-left: 10px;'>Item Code {item.CustomerItemCode}</div>";
                }

                if (item.SpecialPrice.HasValue) {
                    html += $"<div style='font-family: Calibri; font-size: 8pt; font-style: italic; margin-left: 10px;'>Special Price {item.SpecialPrice.Value:C2}</div>";
                }

                html += "</body></html>";
                rtfDescription.Html = html;

                if (quantityChanged) {
                    var quantityHtml =
                        $"<html><body style='margin: 0; padding: 0 3px 10px 3px;'><div style='text-align: center; font-family: Calibri; font-size: 8pt; text-decoration: line-through;'>{item.SpireOrderQuantity}</div><div style='text-align: center; font-family: Calibri; font-size: 8pt; font-weight: bold'>{item.OrderQuantity}</div></body></html>";
                    rtfCases.Html = quantityHtml;
                } else {
                    var quantityHtml =
                        "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='text-align: center; font-family: Calibri; font-size: 8pt; " +
                        $"{(bold ? "font-weight: bold;" : "")}'>{(item.IsNew ? "*" : "")}{item.OrderQuantityDisplay}</div></body></html>";
                    rtfCases.Html = quantityHtml;
                }

                //var prefix = item.PriceWarning == null ? string.Empty : "<span style='font-family: \"Segos UI Symbol\"; font-size: 8pt;'>&#xE171;&nbsp;</span>";
                var prefix = item.PriceWarning == null ? string.Empty : "<span style='font-family: \"Segos UI Symbol\"; font-size: 8pt;'>&#xE129;&nbsp;</span>";
                html = "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='font-family: Calibri; font-size: 8pt; text-align: center;'>" +
                       prefix + (item.UnitPrice.HasValue ? item.UnitPrice.Value.ToString("C2") : "") + "</div>";
                if (item.UnitPrice.HasValue && item.PackQuantity.HasValue && item.PackQuantity.Value > 1) {
                    html += $"<div style='font-family: Calibri; font-size: 8pt; text-align: center; font-style: italic;'>{item.UnitPrice.Value / item.PackQuantity.Value:C2}/ea</div>";
                }

                //if(item.PriceWarning != null) {
                //    html += $"<div style='font-family: Calibri; font-size: 8pt; text-align: center; font-style: italic; padding: 5px 0 0 0;'>{item.PriceWarning}</div>";
                //}

                html += "</body></html>";
                txtUnitPrice.Html = html;

                html = "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='text-align: center; font-family: Calibri; font-size: 8pt; " +
                       $"{(item.SpireCommentFieldsChanged ? "font-weight: bold;" : "")}'>{item.Retail}</div></body></html>";
                rtfRetail.Html = html;

                html = "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='text-align: center; font-family: Calibri; font-size: 8pt; " +
                       $"{(item.SpireCommentFieldsChanged ? "font-weight: bold;" : "")}'>{item.PotCover}</div></body></html>";
                rtfPotCover.Html = html;

                html = "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='text-align: center; font-family: Calibri; font-size: 8pt; " +
                    $"{(item.SpireCommentFieldsChanged ? "font-weight: bold;" : "")}'>{item.DateCode}</div></body></html>";
                rtfDateCode.Html = html;

                html = "<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='text-align: center; font-family: Calibri; font-size: 8pt; " +
                       $"{(item.SpireCommentFieldsChanged ? "font-weight: bold;" : "")}'>{item.Upc}</div></body></html>";
                rtfUPC.Html = html;

                var commentHtml = $"<html><body style='margin: 0; padding: 0 0 10px 0; {(item.SpireCommentFieldsChanged ? "font-weight: bold;" : "")}'>";

                var comments = HttpUtility.HtmlEncode(item.Comments ?? string.Empty).Replace("\n", "<br>");
                if (!string.IsNullOrWhiteSpace(item.Comments) && !string.IsNullOrWhiteSpace(item.GrowerItemNotes)) {
                    comments += "<br>";
                }

                if (!string.IsNullOrWhiteSpace(item.GrowerItemNotes)) {
                    comments += HttpUtility.HtmlEncode(item.GrowerItemNotes).Replace("\n", "<br>");
                }

                if (!string.IsNullOrWhiteSpace(comments)) {
                    commentHtml += $"<div style='font-family: Calibri; font-size: 8pt;'>{comments}</div>";
                }

                commentHtml += "</body></html>";
                rtfComments.Html = commentHtml;

                var vendorHtml = $"<html><body style='margin: 0; padding: 0 0 10px 0;'><div style='font-family: Calibri; font-size: 8pt;'>{item.VendorName}</div>";
                if (!string.IsNullOrWhiteSpace(item.PrebookId)) {
                    vendorHtml += $"<div style='font-family: Calibri; font-size: 8pt;'>Prebook {item.PrebookId:00000}</div>";
                }
                if (item.VendorId == BoekestynVendorId) {
                    var symbol = item.UpgradeSheet ? "" : "";
                    vendorHtml += $"<div style='font-family: \"Segoe UI Symbol\"; font-size: 8pt;'>Upgrade Sheet: {symbol}</div>";
                }
                vendorHtml += "</body></html>";
                txtVendor.Html = vendorHtml;

                _formatIndex++;
            }
        }
    }

    public class FutureOrderSummaryReportFutureOrderItem
    {
        public string PrebookId { get; set; } = string.Empty;   
        public int VendorId { get; set; }
        public string VendorName { get; set; } = string.Empty;
        public string SpirePartNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int OrderQuantity { get; set; }
        public bool IsApproximate { get; set; }
        public string PotCover { get; set; } = string.Empty;
        public string DateCode { get; set; } = string.Empty;
        public string Upc { get; set; } = string.Empty;
        public bool WeightsAndMeasures { get; set; }
        public string Retail { get; set; } = string.Empty;
        public decimal? UnitPrice { get; set; }
        public string CustomerItemCode { get; set; } = string.Empty;
        public string Comments { get; set; } = string.Empty;
        public string GrowerItemNotes { get; set; } = string.Empty;
        public decimal? SpecialPrice { get; set; }
        public string? PriceWarning { get; set; }
        public bool Sent { get; set; }
        public bool UpgradeSheet { get; set; }
        public bool IsNew { get; set; }
        public int? SpireOrderQuantity { get; set; }
        public bool SpireCommentFieldsChanged { get; set; }

        public string OrderQuantityDisplay => OrderQuantity.ToString("#,##0") + (IsApproximate ? $" +/-" : "");

        public int? PackQuantity => PackQuantityParser.Parse(Description);

        public decimal? PotPrice
        {
            get
            {
                if (PackQuantity == null || PackQuantity.Value == 0 || UnitPrice == null) {
                    return null;
                }

                return UnitPrice.Value / PackQuantity.Value;
            }
        }
    }
}
