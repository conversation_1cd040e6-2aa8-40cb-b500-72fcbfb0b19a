﻿using System.Text.Json.Serialization;

namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class StickingOrder
{
    [JsonPropertyName("_id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("_rev")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Revision { get; set; }

    [JsonPropertyName("type")]
    public string Type { get; set; } = "order";

    [JsonPropertyName("orderNumber")]
    public string OrderNumber { get; set; } = string.Empty;

    [JsonPropertyName("stickDate")]
    public string StickDate { get; set; } = string.Empty;

    [JsonPropertyName("hasPartialSpace")]
    public bool HasPartialSpace { get; set; }

    [JsonPropertyName("hasSpacing")]
    public bool HasSpacing { get; set; }

    [JsonPropertyName("fullSpaceDate")]
    public string? FullSpaceDate { get; set; }

    [JsonPropertyName("hasLightsOut")]
    public bool HasLightsOut { get; set; }

    [JsonPropertyName("lightsOutDate")]
    public string? LightsOutDate { get; set; }

    [JsonPropertyName("hasPinching")]
    public bool HasPinching { get; set; }

    [JsonPropertyName("flowerDate")]
    public string FlowerDate { get; set; } = string.Empty;

    [JsonPropertyName("cuttings")]
    public int Cuttings { get; set; }

    [JsonPropertyName("pots")]
    public int Pots { get; set; }

    [JsonPropertyName("cases")]
    public int Cases { get; set; }

    [JsonPropertyName("pinchDate")]
    public string? PinchDate { get; set; }

    [JsonPropertyName("tableCountTight")]
    public int TableCountTight { get; set; }

    [JsonPropertyName("tableCountSpaced")]
    public int TableCountSpaced { get; set; }

    [JsonPropertyName("notes")]
    public string? Notes { get; set; }

    [JsonPropertyName("stickingScheduled")]
    public bool StickingScheduled { get; set; }

    [JsonPropertyName("customer")]
    public StickingOrderCustomer Customer { get; set; } = new();

    [JsonPropertyName("plant")]
    public StickingOrderPlant Plant { get; set; } = new();

    [JsonPropertyName("varieties")]
    public List<StickingOrderVariety> Varieties { get; set; } = [];

    [JsonPropertyName("stickZone")]
    public StickingOrderZone StickZone { get; set; } = new();

    [JsonPropertyName("fullSpaceZone")]
    public StickingOrderZone? FullSpaceZone { get; set; }

    [JsonPropertyName("lightsOutZone")]
    public StickingOrderZone? LightsOutZone { get; set; }

    public decimal StickingHours => Plant.StickingCuttingsPerHour == 0 ? 0 : (decimal)Cuttings / Plant.StickingCuttingsPerHour;

    public class StickingOrderCustomer
    {
        [JsonPropertyName("abbreviation")]
        public string Abbreviation { get; set; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;
    }

    public class StickingOrderPlant
    {
        [JsonPropertyName("_id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("abbreviation")]
        public string Abbreviation { get; set; } = string.Empty;

        [JsonPropertyName("crop")]
        public string Crop { get; set; } = string.Empty;

        [JsonPropertyName("size")]
        public string Size { get; set; } = string.Empty;

        [JsonPropertyName("cuttingsPerPot")]
        public int CuttingsPerPot { get; set; }

        [JsonPropertyName("cuttingsPerTableTight")]
        public int CuttingsPerTableTight { get; set; }

        [JsonPropertyName("cuttingsPerTableSpaced")]
        public int CuttingsPerTableSpaced { get; set; }

        [JsonPropertyName("cuttingsPerTablePartiallySpaced")]
        public int? CuttingsPerTablePartiallySpaced { get; set; }

        [JsonPropertyName("potsPerCase")]
        public int PotsPerCase { get; set; }

        [JsonPropertyName("stickingCuttingsPerHour")]
        public int StickingCuttingsPerHour { get; set; }

        [JsonPropertyName("spacingPotsPerHour")]
        public int SpacingPotsPerHour { get; set; }

        [JsonPropertyName("packingCasesPerHour")]
        public int PackingCasesPerHour { get; set; }

        [JsonPropertyName("colour")]
        public string Colour { get; set; } = string.Empty;

        [JsonPropertyName("hasPinching")]
        public bool HasPinching { get; set; }

        [JsonPropertyName("daysToPinch")]
        public int DaysToPinch { get; set; }

        [JsonPropertyName("defaultStickingCrewSize")]
        public int? DefaultStickingCrewSize { get; set; }
    }

    public class StickingOrderVariety
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("cuttings")]
        public int Cuttings { get; set; }

        [JsonPropertyName("pots")]
        public int Pots { get; set; }

        [JsonPropertyName("cases")]
        public int Cases { get; set; }

        [JsonPropertyName("comment")]
        public string? Comment { get; set; }
    }

    public class StickingOrderZone
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("tables")]
        public int Tables { get; set; }

        [JsonPropertyName("_id")]
        public string Id { get; set; } = string.Empty;
    }
}