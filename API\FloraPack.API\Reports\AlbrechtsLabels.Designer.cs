namespace FloraPack.API.Reports
{
    /// <summary>
    /// Summary description for AlbrechtsLabels.
    /// </summary>
    partial class AlbrechtsLabels
    {
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing) {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(AlbrechtsLabels));
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.txtDescription = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtLabelCount = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.picLogo = new GrapeCity.ActiveReports.SectionReportModel.Picture();
            this.txtStoreName = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtStoreNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPartNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtDeliveryDate = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPurchaseOrderNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLabelCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPartNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDeliveryDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseOrderNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // detail
            // 
            this.detail.CanGrow = false;
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.txtDescription,
            this.txtLabelCount,
            this.picLogo,
            this.txtStoreName,
            this.txtStoreNumber,
            this.txtPartNumber,
            this.txtDeliveryDate,
            this.txtPurchaseOrderNumber});
            this.detail.Height = 3.5F;
            this.detail.Name = "detail";
            // 
            // txtDescription
            // 
            this.txtDescription.DataField = "DescriptionDisplay";
            this.txtDescription.Height = 1F;
            this.txtDescription.Left = 0.855F;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Style = "font-size: 24pt; font-weight: normal; text-align: left; vertical-align: top";
            this.txtDescription.Text = "Description";
            this.txtDescription.Top = 1F;
            this.txtDescription.Width = 5.185F;
            // 
            // txtLabelCount
            // 
            this.txtLabelCount.Angle = 900;
            this.txtLabelCount.DataField = "LabelNumberDisplay";
            this.txtLabelCount.Height = 3.5F;
            this.txtLabelCount.HyperLink = null;
            this.txtLabelCount.Left = 0.6350001F;
            this.txtLabelCount.Name = "txtLabelCount";
            this.txtLabelCount.Style = "color: Black; font-size: 10pt; font-weight: normal; text-align: left; vertical-al" +
    "ign: middle; ddo-font-vertical: none";
            this.txtLabelCount.Text = "X of Y";
            this.txtLabelCount.Top = 0F;
            this.txtLabelCount.Width = 0.2F;
            // 
            // picLogo
            // 
            this.picLogo.Height = 3.5F;
            this.picLogo.ImageBase64String = resources.GetString("picLogo.ImageBase64String");
            this.picLogo.Left = 0F;
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeMode = GrapeCity.ActiveReports.SectionReportModel.SizeModes.Zoom;
            this.picLogo.Top = 0F;
            this.picLogo.Width = 0.6350001F;
            // 
            // txtStoreName
            // 
            this.txtStoreName.Border.BottomStyle = GrapeCity.ActiveReports.BorderLineStyle.ThickSolid;
            this.txtStoreName.Height = 0.815F;
            this.txtStoreName.Left = 0.855F;
            this.txtStoreName.Name = "txtStoreName";
            this.txtStoreName.Style = "font-size: 18pt; font-weight: bold; text-align: left";
            this.txtStoreName.Text = "FRED W ALBRECHT GROCERY CO";
            this.txtStoreName.Top = 0F;
            this.txtStoreName.Width = 5.165F;
            // 
            // txtStoreNumber
            // 
            this.txtStoreNumber.DataField = "StoreNumberDisplay";
            this.txtStoreNumber.Height = 0.815F;
            this.txtStoreNumber.Left = 3.988F;
            this.txtStoreNumber.Name = "txtStoreNumber";
            this.txtStoreNumber.Style = "font-size: 32pt; font-weight: normal; text-align: right; vertical-align: bottom";
            this.txtStoreNumber.Text = "Store #";
            this.txtStoreNumber.Top = 0F;
            this.txtStoreNumber.Width = 2.012F;
            // 
            // txtPartNumber
            // 
            this.txtPartNumber.DataField = "ProductNumber";
            this.txtPartNumber.Height = 0.625F;
            this.txtPartNumber.Left = 0.855F;
            this.txtPartNumber.Name = "txtPartNumber";
            this.txtPartNumber.Style = "font-size: 36pt; font-weight: bold; text-align: left; vertical-align: middle; whi" +
    "te-space: nowrap; ddo-wrap-mode: nowrap";
            this.txtPartNumber.Text = "Product Number";
            this.txtPartNumber.Top = 2F;
            this.txtPartNumber.Width = 5.185F;
            // 
            // txtDeliveryDate
            // 
            this.txtDeliveryDate.DataField = "DeliveryDateDisplay";
            this.txtDeliveryDate.Height = 0.3F;
            this.txtDeliveryDate.Left = 0.855F;
            this.txtDeliveryDate.Name = "txtDeliveryDate";
            this.txtDeliveryDate.Style = "font-size: 16pt; font-weight: normal; text-align: left; vertical-align: middle";
            this.txtDeliveryDate.Text = "Delivery Date";
            this.txtDeliveryDate.Top = 2.8F;
            this.txtDeliveryDate.Width = 5.185F;
            // 
            // txtPurchaseOrderNumber
            // 
            this.txtPurchaseOrderNumber.DataField = "PurchaseOrderNumberDisplay";
            this.txtPurchaseOrderNumber.Height = 0.3F;
            this.txtPurchaseOrderNumber.Left = 0.855F;
            this.txtPurchaseOrderNumber.Name = "txtPurchaseOrderNumber";
            this.txtPurchaseOrderNumber.Style = "font-size: 16pt; font-weight: normal; text-align: left; vertical-align: middle";
            this.txtPurchaseOrderNumber.Text = "Purchase Order Number";
            this.txtPurchaseOrderNumber.Top = 3.15F;
            this.txtPurchaseOrderNumber.Width = 5.185F;
            // 
            // AlbrechtsLabels
            // 
            this.MasterReport = false;
            this.CompatibilityMode = GrapeCity.ActiveReports.Document.CompatibilityModes.CrossPlatform;
            this.PageSettings.DefaultPaperSize = false;
            this.PageSettings.Margins.Bottom = 0.25F;
            this.PageSettings.Margins.Left = 0.25F;
            this.PageSettings.Margins.Right = 0.25F;
            this.PageSettings.Margins.Top = 0.25F;
            this.PageSettings.PaperHeight = 4F;
            this.PageSettings.PaperKind = GrapeCity.ActiveReports.Printing.PaperKind.Custom;
            this.PageSettings.PaperName = "Custom paper";
            this.PageSettings.PaperWidth = 6F;
            this.PrintWidth = 6.02F;
            this.Sections.Add(this.detail);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-style: normal; text-decoration: none; font-weight: normal; color: Black; fon" +
            "t-family: \"Calibri\"; font-size: 11pt; ddo-char-set: 186", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-weight: bold; ddo-char-set: 186; font-size: 24pt", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 14pt; font-weight: bold; font-style: italic; font-family: \"Calibri\"; d" +
            "do-char-set: 186", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold; ddo-char-set: 186", "Heading3", "Normal"));
            this.ReportStart += new System.EventHandler(this.OnReportStart);
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLabelCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPartNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDeliveryDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseOrderNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion

        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDescription;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtStoreNumber;
        private GrapeCity.ActiveReports.SectionReportModel.Label txtLabelCount;
        private GrapeCity.ActiveReports.SectionReportModel.Picture picLogo;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtStoreName;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPartNumber;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDeliveryDate;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPurchaseOrderNumber;
    }
}
