﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D50F1F8E-E50C-42CB-B78F-7A4B74DA7EB4}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Reports</RootNamespace>
    <AssemblyName>Reports</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=3.0.2.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.3.0.2\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml.Framework, Version=3.0.2.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.Framework.3.0.2\lib\net46\DocumentFormat.OpenXml.Framework.dll</HintPath>
    </Reference>
    <!-- <Reference Include="Gcef.Data.DataEngine, Version=3.3.0.0, Culture=neutral, PublicKeyToken=0b5e1563d2869208, processorArchitecture=MSIL">
      <HintPath>..\packages\Gcef.Data.DataEngine.3.3.0\lib\net462\Gcef.Data.DataEngine.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="Gcef.Data.ExpressionInfo, Version=3.3.0.0, Culture=neutral, PublicKeyToken=0b5e1563d2869208, processorArchitecture=MSIL">
      <HintPath>..\packages\Gcef.Data.ExpressionInfo.3.3.0\lib\net462\Gcef.Data.ExpressionInfo.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="Gcef.Data.VBFunctionLib, Version=3.3.0.0, Culture=neutral, PublicKeyToken=0b5e1563d2869208, processorArchitecture=MSIL">
      <HintPath>..\packages\Gcef.Data.VBFunctionLib.3.3.0\lib\net462\Gcef.Data.VBFunctionLib.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.17.1.1\lib\net462\GrapeCity.ActiveReports.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Chart, Version=17.0.3.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Chart.17.0.3\lib\net462\GrapeCity.ActiveReports.Chart.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Chart.Win, Version=17.0.3.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Chart.Win.17.0.3\lib\net462\GrapeCity.ActiveReports.Chart.Win.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.DataProviders, Version=2.1.3.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.DataProviders.2.1.3\lib\net462\GrapeCity.ActiveReports.Core.DataProviders.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Document, Version=4.1.1.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Document.4.1.1\lib\net462\GrapeCity.ActiveReports.Core.Document.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Document.Drawing.Gc, Version=4.1.1.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Document.Drawing.Gc.4.1.1\lib\net462\GrapeCity.ActiveReports.Core.Document.Drawing.Gc.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Document.Drawing.Gdi, Version=4.1.1.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Document.Drawing.Gdi.4.1.1\lib\net462\GrapeCity.ActiveReports.Core.Document.Drawing.Gdi.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Drawing.Gc, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Drawing.Gc.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Drawing.Gc.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Drawing.Gdi, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Drawing.Gdi.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Drawing.Gdi.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Excel.Page, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Excel.Page.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Export.Excel.Page.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Html.Page, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Html.Page.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Export.Html.Page.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Image.Page, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Image.Page.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Export.Image.Page.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Pdf.Page, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Pdf.Page.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Export.Pdf.Page.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Pdf.Section, Version=4.1.1.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Pdf.Section.4.1.1\lib\net462\GrapeCity.ActiveReports.Core.Export.Pdf.Section.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Svg.Page, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Svg.Page.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Export.Svg.Page.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Text.Page, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Text.Page.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Export.Text.Page.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Tiff.Section, Version=4.1.1.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Tiff.Section.4.1.1\lib\net462\GrapeCity.ActiveReports.Core.Export.Tiff.Section.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Export.Word.Page, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Export.Word.Page.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Export.Word.Page.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Rdl, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Rdl.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Rdl.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Rendering, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Rendering.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Rendering.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Core.Scripting, Version=*******, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Core.Scripting.3.6.0\lib\net462\GrapeCity.ActiveReports.Core.Scripting.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Design.Win, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Design.Win.17.1.1\lib\net462\GrapeCity.ActiveReports.Design.Win.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Export.Excel, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Export.Excel.17.1.1\lib\net462\GrapeCity.ActiveReports.Export.Excel.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Export.Html, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Export.Html.17.1.1\lib\net462\GrapeCity.ActiveReports.Export.Html.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Export.Image, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Export.Image.17.1.1\lib\net462\GrapeCity.ActiveReports.Export.Image.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Export.Image.Win, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Export.Image.17.1.1\lib\net462\GrapeCity.ActiveReports.Export.Image.Win.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Export.Pdf, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Export.Pdf.17.1.1\lib\net462\GrapeCity.ActiveReports.Export.Pdf.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Export.Pdf.Win, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Export.Pdf.17.1.1\lib\net462\GrapeCity.ActiveReports.Export.Pdf.Win.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Export.Word, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Export.Word.17.1.1\lib\net462\GrapeCity.ActiveReports.Export.Word.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Export.Xml, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Export.Xml.17.1.1\lib\net462\GrapeCity.ActiveReports.Export.Xml.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Serializer, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Serializer.VS2022.17.1.1\lib\net472\GrapeCity.ActiveReports.Serializer.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.SpreadBuilder, Version=2.0.0.0, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.SpreadBuilder.2.0.0\lib\net462\GrapeCity.ActiveReports.SpreadBuilder.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Viewer.Common, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Viewer.Common.17.1.1\lib\net462\GrapeCity.ActiveReports.Viewer.Common.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Viewer.Win, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.Viewer.Win.17.1.1\lib\net462\GrapeCity.ActiveReports.Viewer.Win.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.ActiveReports.Win, Version=********, Culture=neutral, PublicKeyToken=cc4967777c49a3ff, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.ActiveReports.17.1.1\lib\net462\GrapeCity.ActiveReports.Win.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.DataVisualization.Chart, Version=0.4.136.0, Culture=neutral, PublicKeyToken=8dbeb54e07a9de91, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.DataVisualization.Chart.0.4.136\lib\net461\GrapeCity.DataVisualization.Chart.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.Documents.DX.Windows, Version=6.0.5.0, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.DX.Windows.6.0.5\lib\net461\GrapeCity.Documents.DX.Windows.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.Documents.Imaging, Version=6.0.5.0, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Imaging.6.0.5\lib\net461\GrapeCity.Documents.Imaging.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.Documents.Imaging.Windows, Version=6.0.5.0, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Imaging.Windows.6.0.5\lib\net461\GrapeCity.Documents.Imaging.Windows.dll</HintPath>
    </Reference> -->
    <!-- <Reference Include="GrapeCity.Documents.Pdf, Version=6.0.5.0, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Pdf.6.0.5\lib\net461\GrapeCity.Documents.Pdf.dll</HintPath>
    </Reference> -->
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.VisualStudio.DpiAwareness, Version=7.10.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.VisualStudio.DpiAwareness.7.10.34910\lib\net46\Microsoft.VisualStudio.DpiAwareness.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Text.Encodings.Web, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=8.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.8.0.5\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CustomerConfirmationItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="CustomerConfirmationItem.Designer.cs">
      <DependentUpon>CustomerConfirmationItem.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomerConfirmation.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="CustomerConfirmation.Designer.cs">
      <DependentUpon>CustomerConfirmation.cs</DependentUpon>
    </Compile>
    <Compile Include="HeinensLabels.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HeinensLabels.Designer.cs">
      <DependentUpon>HeinensLabels.cs</DependentUpon>
    </Compile>
    <Compile Include="AlbrechtsLabels.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="AlbrechtsLabels.Designer.cs">
      <DependentUpon>AlbrechtsLabels.cs</DependentUpon>
    </Compile>
    <Compile Include="FutureOrderSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="FutureOrderSummary.Designer.cs">
      <DependentUpon>FutureOrderSummary.cs</DependentUpon>
    </Compile>
    <Compile Include="FutureOrderSummaryItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="FutureOrderSummaryItem.Designer.cs">
      <DependentUpon>FutureOrderSummaryItem.cs</DependentUpon>
    </Compile>
    <Compile Include="CobornsLabels.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="CobornsLabels.Designer.cs">
      <DependentUpon>CobornsLabels.cs</DependentUpon>
    </Compile>
    <Compile Include="Prebook.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Prebook.Designer.cs">
      <DependentUpon>Prebook.cs</DependentUpon>
    </Compile>
    <Compile Include="PrebookItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="PrebookItem.Designer.cs">
      <DependentUpon>PrebookItem.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UpgradeSheet.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UpgradeSheet.Designer.cs">
      <DependentUpon>UpgradeSheet.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CustomerConfirmationItem.resx">
      <DependentUpon>CustomerConfirmationItem.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CustomerConfirmation.resx">
      <DependentUpon>CustomerConfirmation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HeinensLabels.resx">
      <DependentUpon>HeinensLabels.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AlbrechtsLabels.resx">
      <DependentUpon>AlbrechtsLabels.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FutureOrderSummary.resx">
      <DependentUpon>FutureOrderSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FutureOrderSummaryItem.resx">
      <DependentUpon>FutureOrderSummaryItem.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CobornsLabels.resx">
      <DependentUpon>CobornsLabels.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Prebook.resx">
      <DependentUpon>Prebook.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrebookItem.resx">
      <DependentUpon>PrebookItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="UpgradeSheet.resx">
      <DependentUpon>UpgradeSheet.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>