﻿namespace FloraPack.API.Repositories.FutureOrders;

public class FutureOrderDetailItem
{
    public int Id { get; set; }
    public int FutureOrderId { get; set; }
    public int SortOrder { get; set; }
    public int? VendorId { get; set; }
    public string? VendorName { get; set; }
    public int SpireInventoryId { get; set; }
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int OrderQuantity { get; set; }
    public bool IsApproximate { get; set; }
    public int? BlanketItemId { get; set; }
    public bool HasPotCover { get; set; }
    public string? PotCover { get; set; }
    public string? DateCode { get; set; }
    public string? Upc { get; set; }
    public bool WeightsAndMeasures { get; set; }
    public string? Retail { get; set; }
    public bool UseAvailabilityPricing { get; set; }
    public decimal? UnitPrice { get; set; }
    public string? CustomerItemCode { get; set; }
    public bool UpgradeSheet { get; set; }
    public bool PhytoRequired { get; set; }
    public bool PhytoOrdered { get; set; }
    public string? Comments { get; set; }
    public string? BoekestynPlantId { get; set; }
    public string? BoekestynCustomerAbbreviation { get; set; }
    public decimal? SpecialPrice { get; set; }
    public int? SpireSalesOrderItemId { get; set; }
    public int? SpirePurchaseOrderItemId { get; set; }
    public bool BoekestynItemIsPriority { get; set; }
    public string? UpcPrinted { get; set; }
    public string? GrowerItemNotes { get; set; }

    public List<FutureOrderDetailItemBoekestynProduct> BoekestynProducts { get; set; } = new();
}
