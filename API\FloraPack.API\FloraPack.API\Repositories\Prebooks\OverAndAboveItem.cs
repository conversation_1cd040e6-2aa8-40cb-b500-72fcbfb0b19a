﻿namespace FloraPack.API.Repositories.Prebooks;

public class OverAndAboveItem
{
    public int PrebookId { get; set; }
    public DateTime RequiredDate { get; set; }
    public int Week { get; set; }
    public string Vendor { get; set; } = string.Empty;
    public string Customer { get; set; } = string.Empty;
    public string BoxCode { get; set; } = string.Empty;
    public string SpirePartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public long OrderQuantity { get; set; }
    public long BlanketQuantity { get; set; }
    public long Ordered { get; set; }
}